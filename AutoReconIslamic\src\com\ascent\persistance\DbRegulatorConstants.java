package com.ascent.persistance;

public interface DbRegulatorConstants {
	public static final String BATCH_SIZE = "BATCH_SIZE";
	public static final String PARAM_VALUE_MAP = "PARAM_VALUE_MAP";
	public static final String UPDATE_QRY_PARAMS = "UPDATE_QRY_PARAMS";
	public static final String UPDATE_QRY = "UPDATE_QRY";
	public static final String DELETE_QRY_PARAMS = "DELETE_QRY_PARAMS";
	public static final String DELETE_QRY = "DELETE_QRY";
	
	public static final String LOAD_QRY_PARAMS = "LOAD_QRY_PARAMS";
	public static final String LOAD_QRY = "LOAD_QRY";
	
	public static final String INSERT_QRY_PARAMS = "INSERT_QRY_PARAMS";
	public static final String INSERT_QRY = "INSERT_QRY";
}
