package com.ascent.boot.etl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Properties;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;
import com.ascent.util.AscentAutoReconConstants;

public class EtlMetaInstance {

	public static final String ETL_QUERY_CONF = "ETL_QUERY_CONF";

	public static final String ETL_CONF = "ETL_CONF";

	private static Logger logger = LogManager.getLogger(EtlMetaInstance.class);

	private static EtlMetaInstance instance;
	private Properties bootProperties;
	private Properties dbProperties;
	private Properties applicationProperties;
	private Queries etlQueryConfs = null;
	private Integrations etlConfs = null;

	private EtlMetaInstance() throws Exception {

		String bootPropFileName = "boot.properties";
		String dbPropFileName = "db.properties";
		String applicationPropFileName = "application.properties";

		this.bootProperties = new Properties();
		this.dbProperties = new Properties();
		this.applicationProperties = new Properties();

		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(bootPropFileName);

		if (inputStream != null) {
			try {
				this.bootProperties.load(inputStream);

				//logger.trace("Loaded bootProperties ");
				for (Object key : this.bootProperties.keySet()) {
					//logger.trace(key + " : " + this.bootProperties.get(key));
					this.bootProperties.get(key);
				}
				//logger.trace("Sucessfully ");
				String appMode = (String) this.bootProperties.get("APP_MODE");
				try {

					InputStream dbInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + dbPropFileName);

					InputStream appInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + applicationPropFileName);

					if (dbInputStream != null) {
						try {
							this.dbProperties.load(dbInputStream);

							//logger.trace("Loaded dbProperties ");
							for (Object key : this.dbProperties.keySet()) {
								//logger.trace(key + " : " + this.dbProperties.get(key));
								this.dbProperties.get(key);
							}
							//logger.trace("Sucessfully ");

						} catch (Exception e) {
							//logger.trace("Unable to load dbProperties ");
							e.printStackTrace();
							throw e;

						}
					} else {
						//logger.trace("property file '" + dbPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + dbPropFileName + "' not found in the classpath");
					}

					if (appInputStream != null) {

						try {
							this.applicationProperties.load(appInputStream);

							//logger.trace("Loaded applicationProperties ");
							/*for (Object key : this.applicationProperties.keySet()) {
								logger.trace(key + " : " + this.applicationProperties.get(key));
							}*/
							//logger.trace("Sucessfully ");

						} catch (Exception e) {
							//logger.trace("Unable to load applicationProperties ");
							e.printStackTrace();
							throw e;

						}

					} else {
						//logger.trace("property file '" + applicationPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + applicationPropFileName + "' not found in the classpath");
					}

				} catch (Exception e) {
					e.printStackTrace();
					//logger.trace("unable to load properties under the APP_MODE " + appMode);
					throw new Exception("unable to load properties under the APP_MODE " + appMode);
				}
			} catch (Exception e) {
				//logger.trace("Unable to load bootProperties properties");
				e.printStackTrace();
				throw e;
			}

		} else {
			//logger.trace("property file '" + bootPropFileName + "' not found in the classpath");
			throw new FileNotFoundException("property file '" + bootPropFileName + "' not found in the classpath");
		}
		try {
			loadEtlConf();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			throw e;
		}
	}

	static {
		try {
			instance = new EtlMetaInstance();
		} catch (Exception e) {
			throw new RuntimeException("Exception occured in creating singleton instance");
		}
	}

	public void loadEtlConf() {

		JAXBContext jaxbContext = null;
		Unmarshaller jaxbUnmarshaller = null;
		try {
			

			File queryCutomizationFile = new File(
					this.applicationProperties.getProperty(AscentAutoReconConstants.ETL_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(ETL_QUERY_CONF));

			InputStream queryCutomizationFileStream = getClass().getClassLoader().getResourceAsStream(
					this.applicationProperties.getProperty(AscentAutoReconConstants.ETL_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(ETL_QUERY_CONF));

			jaxbContext = JAXBContext.newInstance(Queries.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			if (queryCutomizationFileStream != null) {
				try {
					this.etlQueryConfs = (Queries) jaxbUnmarshaller.unmarshal(queryCutomizationFileStream);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
				}
			} else {
				try {
					this.etlQueryConfs = (Queries) jaxbUnmarshaller.unmarshal(queryCutomizationFile);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
					throw e;
				}
			}
			this.etlQueryConfs.bootConf();
			//logger.trace("Loaded Integration Query Configurations");

			File integrationConfFile = new File(
					this.applicationProperties.getProperty(AscentAutoReconConstants.ETL_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(ETL_CONF));

			InputStream integrationConfFileIs = getClass().getClassLoader().getResourceAsStream(
					this.applicationProperties.getProperty(AscentAutoReconConstants.ETL_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(ETL_CONF));

			jaxbContext = JAXBContext.newInstance(Integrations.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();

			if (integrationConfFileIs != null) {
				try {
					this.etlConfs = (Integrations) jaxbUnmarshaller.unmarshal(integrationConfFileIs);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
				}
			} else {
				try {
					this.etlConfs = (Integrations) jaxbUnmarshaller.unmarshal(integrationConfFile);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
					throw e;
				}
			}
			this.etlConfs.bootConf(this.etlQueryConfs);

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public static EtlMetaInstance getInstance() {
		return instance;
	}

	public Properties getBootProperties() {
		return bootProperties;
	}

	public void setBootProperties(Properties bootProperties) {
		this.bootProperties = bootProperties;
	}

	public Properties getDbProperties() {
		return dbProperties;
	}

	public void setDbProperties(Properties dbProperties) {
		this.dbProperties = dbProperties;
	}

	public Properties getApplicationProperties() {
		return applicationProperties;
	}

	public void setApplicationProperties(Properties applicationProperties) {
		this.applicationProperties = applicationProperties;
	}

	public Queries getEtlQueryConfs() {
		return etlQueryConfs;
	}

	public void setEtlQueryConfs(Queries etlQueryConfs) {
		this.etlQueryConfs = etlQueryConfs;
	}

	public Integration getEtlConf(String etlConfName) throws Exception {
		Integration etlConf = null;
		if (this.etlConfs != null) {
			etlConf = this.etlConfs.getIntegrationMap().get(etlConfName);
			if (etlConf == null) {
				throw new Exception("No ETL Configuration available with the name" + etlConfName);
			}
		} else {
			throw new Exception("ETL configuration not loaded ");
		}
		return etlConf;
	}

	public Integrations getEtlConfs() {
		return etlConfs;
	}

	public void setEtlConfs(Integrations etlConfs) {
		this.etlConfs = etlConfs;
	}
}