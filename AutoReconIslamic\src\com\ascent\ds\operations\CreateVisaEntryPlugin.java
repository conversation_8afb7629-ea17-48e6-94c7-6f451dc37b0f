package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CreateVisaEntryPlugin extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = 1L;
public DSResponse executeFetch(DSRequest request)throws Exception{
	DSResponse response =new DSResponse();
	Map requestCriteriaMap=request.getValues();
	System.out.println("requestCriteriaMap"+requestCriteriaMap);
	Map<String,Object>result=null;
	Map<String,Object>visaEntryMap=new HashMap<String, Object>();
	HttpSession httpSession = request.getHttpServletRequest().getSession();
	User user = (User) httpSession.getAttribute("userId");
	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		response.setData(result);
		return response;
	}
	try{
		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");
		visaEntryMap=(Map<String, Object>) requestCriteriaMap.get("debitCreditAccDetails");//remarks  selectedRecords
		Map<String,Object> visaEntryData=(Map<String, Object>) visaEntryMap.get("visaEntryData");
		Map<String,Object> visaEntryCustomerData=(Map<String, Object>) visaEntryMap.get("visaEntryCustomerData");
		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) requestCriteriaMap.get("selectedRecords");
	    System.out.println("selectedRecords"+selectedRecords);
		String reconDataSource = (String) requestCriteriaMap.get("reconDataSource");
		String action = (String) requestCriteriaMap.get("action");
		String moduleName = (String) visaEntryMap.get("moduleName");
		String remarks = (String) visaEntryMap.get("remarks");
		String accCatgeory = (String) visaEntryMap.get("accCatgeory");
		Map<String, Object> recordsList = (Map<String, Object>) requestCriteriaMap.get("recordsList");
		String dsName = (String) requestCriteriaMap.get("dsName");
		String comments = (String) requestCriteriaMap.get("comments");
		String integrationName = (String) requestCriteriaMap.get("integrationName");
		
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
		String centrifugalAmountField= requestCriteriaMap.get("centrifugalAmount").toString();
		
		System.out.println("visaEntryCustomerData :" + visaEntryCustomerData);
		
		Map<String, Object> paramsMap = new HashMap<String, Object>();
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		//paramsMap.put(GL_CODE, glcode);
		paramsMap.put(ACC_CATEGORY, accCatgeory);
		paramsMap.put("REMARKS", remarks);
		paramsMap.put(DEBIT_CREDIT_DETAILS, visaEntryMap);
		paramsMap.put("reconDataSource", reconDataSource);
		paramsMap.put("VISA_ENTRY_DATA", visaEntryData);
		paramsMap.put("VISA_ENTRY_CUSTOMER_DATA", visaEntryCustomerData);
		
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		if(centrifugalAmountField!=null){
			paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		}
		result = process(paramsMap);

		response.setData(result);
	}catch(Exception e){
		e.printStackTrace();
	}
	return response;
}
private Map<String, Object> process(Map<String, Object> createVisaParamsMap) {
	Connection connection = null;
	Map<String, Object> result = null;
	try{
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();
		String userId = (String) createVisaParamsMap.get(USER_ID);
		String dsName = (String) createVisaParamsMap.get(DS_NAME);
		createVisaParamsMap.put(PERSIST_CLASS,CREAT_VISA_ENTRY_PLUGIN_CLAS_NAME);
		activityDataMap.put("activity_data", createVisaParamsMap);

		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;
			String businessArea = (String) createVisaParamsMap.get(BUSINES_AREA);
			String reconName = (String) createVisaParamsMap.get(RECON_NAME);
			String comments = (String) createVisaParamsMap.get(COMMENTS);
			String moduleName = (String) createVisaParamsMap.get(MODULE);
			userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
					CREATE_VISA_ENTRY_OPERATION, activityDataMap, activityStatus, comments);

			String integrationName= (String) createVisaParamsMap.get(INTEGRATION_NAME);
			LoadRegulator loadRegulator=new LoadRegulator();
			InsertRegulator insertRegulator=new InsertRegulator();
		
			String tableNameStg=integrationName+"_STG";
			String tableNameStgAudit=integrationName+"_STG_AUDIT";
		//	String tableNameAudit=integrationName+"_STG_AUDIT";
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameStgAudit, connection);
			
	//		System.out.println(insertQueryConf);
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement stagingUpdatePstmt=null;
			try{
				List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) createVisaParamsMap.get(SELECTED_RECORDS);
				
				for(Map<String,Object> selectedRec:selectedRecords){
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedRec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameStgAudit, connection);
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					Map paramValueMap=new HashMap();
					for(Map rec : auditData){
						
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}
				
					
					//System.out.println("vbnc");
				
					
					
					long version=(long) selectedRec.get("VERSION");
					++version;
					selectedRec.put("VERSION", version);
					selectedRec.put("WORKFLOW_STATUS", "Y");
					String reqComments=user.getUserId()+":"+(String) createVisaParamsMap.get(COMMENTS);
					selectedRec.put("ACTIVITY_COMMENTS", reqComments);
					long sid=(long) selectedRec.get("SID");
					
					stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='Y',ACTIVITY_COMMENTS=?,VERSION="+version+" WHERE SID="+sid);
					stagingUpdatePstmt.setObject(1, reqComments);
					
					int rows=	stagingUpdatePstmt.executeUpdate();
				
				//	selectAuditStmt=connection.prepareStatement(auditSelectQry);
				//	auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
			      //audit(loadRegulator, insertRegulator, insertQueryConf, selectAuditStmt, auditInsertPstmt, selectedRec);
					
					updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
					//logger.trace(SUCCESS+" "+TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				}
			}catch(Exception e){
				//e.printStackTrace();
				//logger.error("Error : "+e);
			}finally{
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closeConnection(connection);
				
				
			}
			return result;
		/*updateResultStatus(result, SUCCESS, "Transactions submitted for Approval Sucessfully");
		return result;*/
		}else{
			result = persist(activityDataMap, APPROVED, connection);
		}
		}catch(Exception e){
		e.printStackTrace();
		updateResultStatus(result, FAILED, "Operation Failed");
		//return result;
	}	
 finally {
	try {
		if (connection != null && !connection.isClosed()) {
			connection.close();
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
	
}
	return result;
}
public Map<String, Object> persist(Map<String, Object> activityDataMap,
		String status, Connection connection) {


	Map<String, Object> result = new HashMap<String, Object>();
	LoadRegulator loadRegulator=new LoadRegulator();
	InsertRegulator insertRegulator=new InsertRegulator();
	try {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
        String userid=(String) activityDataMap.get("userId");
		String comment=(String) activityDataMap.get("comment");
		Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
		String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
		List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		List<Map<String, Object>> glList=new ArrayList<Map<String, Object>>();
		String reconDataSource=(String) activityRecordsMap.get("reconDataSource");
		String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
		Map<String,Object> VISA_ENTRY_MAP=(Map<String, Object>) activityRecordsMap.get(DEBIT_CREDIT_DETAILS);
		
		Map<String,Object> DEBITCREDITDETAILS=(Map<String, Object>) VISA_ENTRY_MAP.get("visaEntryData");
		Map<String,Object> CUSTOMERDEBITCREDITDETAILS=(Map<String, Object>) VISA_ENTRY_MAP.get("visaEntryCustomerData");
		
		System.out.println("CUSTOMERDEBITCREDITDETAILS :" + CUSTOMERDEBITCREDITDETAILS);
		
		connection = DbUtil.getConnection();
		
		String businessArea = (String) activityRecordsMap.get(BUSINES_AREA);
		String reconName = (String) activityRecordsMap.get(RECON_NAME);
		String comments = (String) activityRecordsMap.get(COMMENTS);
		String remarks=(String)activityRecordsMap.get("REMARKS");
		String tableNameStg=integrationName+"_STG";
		String tableNameAudit=integrationName+"_STG_AUDIT";
		if(integrationName.equalsIgnoreCase("AUTH_ISS")){
			
			tableNameStg="AUTH_ISSUER_STG";
			tableNameAudit="AUTH_ISSUER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("AUTH_ACQ")){
			
			tableNameStg="AUTH_ACQUIRER_STG";
			tableNameAudit="AUTH_ACQUIRER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("MAST")){
			
			tableNameStg="MASTER_STG";
			tableNameAudit="MASTER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("CTL")){
			if(reconTableName.contains("ATM")){
				tableNameStg="CISO_STG";
				tableNameAudit="CISO_STG_AUDIT";
				}else if(reconTableName.contains("POS")){
					if(reconTableName.contains("ISSUER")){
						tableNameStg="CISO_STG";
						tableNameAudit="CISO_STG_AUDIT";
					}else{
						tableNameStg="MISO_STG";
						tableNameAudit="MISO_STG_AUDIT";
					}
				}
			}else if(integrationName.equalsIgnoreCase("VISA")){
				if(reconTableName.contains("ISSUER")){
					tableNameStg="VISA_ISSUER_STG";
					tableNameAudit="VISA_ISSUER_STG_AUDIT";
				}
			}
		
		
		
		
		
		if (APPROVED.equalsIgnoreCase(status)) {
			
			String stagingTableIdSeqName = tableNameStg + "MM_ID_SEQ";
			String stagingTableIdGenQry = "SELECT NEXT VALUE FOR " + stagingTableIdSeqName + " as sno";
			String idseqname=tableNameStg + "MM_ID_SEQ";
				
			PreparedStatement stagingTableIdGenPstmt = connection.prepareStatement(stagingTableIdGenQry);
			String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
			String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
			
			PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
			 
			String reconTableReconIdSeqName = reconTableName + "MM_RECON_ID_SEQ";
			String reconTableReconIdGenQry = "SELECT NEXT VALUE FOR " + reconTableReconIdSeqName + " as sno";
				
			PreparedStatement reconTableReconIdGenPstmt = connection.prepareStatement(reconTableReconIdGenQry);
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			String debitOrCredit=null;
			String mainOrReversalIndOrig=null;
			String mainOrReversalIndGl=null;
			int debOrCreIndGl=0;
			int debOrCreIndOrig=0;
			
			PreparedStatement updateStmt=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement	stagingInsertPstmt=null;
			PreparedStatement	stagingUpdatePstmt=null;
			PreparedStatement reconDataSelectFromStagingPstmt=null;
			PreparedStatement reconDataInsertPstmt=null;
			PreparedStatement generateInsertPstmt=null;
			PreparedStatement generateInsertPstmtOrig=null;
			try{
			        
				
				
			        long reconId=PersistanceUtil.generateSeqNo(connection, reconTableReconIdGenPstmt, reconTableReconIdSeqName);
			        
			        reconId=reconId+System.currentTimeMillis();
			for(Map<String,Object> selectedrecord:records){
				
				selectAuditStmt=connection.prepareStatement(auditSelectQry);
				
				List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
				
				Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				
				  int version=0;
				for(Map rec : auditData){
					Map paramValueMap=new HashMap();
					paramValueMap.put("PARAM_VALUE_MAP", rec);
					version=Integer.parseInt(rec.get("VERSION").toString());
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
				//	logger.trace("Record Inserted to "+tableNameAudit);
				}
				
				String approveComments=userid+" : "+comment;
				
				long sid=(long) selectedrecord.get("SID");
				
////////////////////////////////
				
				String reocnside="";
				if(integrationName.contains("VISA")){
					reocnside="VISA";
				}else if(integrationName.contains("MAST")){
					reocnside="MAST";
				}
				else{ 
					reocnside=integrationName;
				}
				
				String queryName=reconTableName+"_"+reocnside;
				
				Query query = queries.getQueryConf(queryName);
				reconDataSelectFromStagingPstmt=connection.prepareStatement(query.getQueryString());
				reconDataSelectFromStagingPstmt.setObject(1, sid);
				ResultSet unreconRs=reconDataSelectFromStagingPstmt.executeQuery();
				ResultSetMetaData unreconRsm= unreconRs.getMetaData();
				int columnCount=unreconRsm.getColumnCount();
				Map<String,Object> reconDataMap=new HashMap<String,Object>();
				while(unreconRs.next()){
							
					for(int i=1;i<=columnCount;i++){
						System.out.println(unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
						reconDataMap.put(unreconRsm.getColumnName(i), unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
					}
				}
			
				
				long recid=PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName);
				recid=recid+System.currentTimeMillis();
				reconDataMap.put("ID",recid);
				reconDataMap.put("RECON_ID", reconId);
				reconDataMap.put("UPDATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
				reconDataMap.put("MATCH_TYPE",  "MM");
				reconDataMap.put("USER_ID",  "SYSTEM");
				reconDataMap.put("WORKFLOW_STATUS",  "N");
				reconDataMap.put("ACTIVE_INDEX",  "Y");
				reconDataMap.put("SID", sid );
				reconDataMap.put("COMMENTS", "MANUAL GL ENTRY" );
				reconDataMap.put("STATUS", "MANUAL GL ENTRY");
				reconDataMap.put("VERSION", 1 );
				reconDataMap.put("RECON_SIDE", "VISA");
				reconDataMap.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
				//reconDataMap.put("COMMENTS",  "SYSTEM");
			    Query reconQuery=	OperationsUtil.getInsertQueryConf(reconTableName, connection);
			    reconDataInsertPstmt=connection.prepareStatement(reconQuery.getQueryString());
			    
			    Map<String, Object> paramValueMap=new HashMap<String, Object>();
				paramValueMap.put("PARAM_VALUE_MAP", reconDataMap);
			 
			    insertRegulator.insert(reconDataInsertPstmt, paramValueMap, reconQuery.getQueryParam());
			//    logger.trace("Record Inserted to "+reconTableName+" with RECON_ID "+reconId);
				++version;
				stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='N',ACTIVITY_COMMENTS=?,RECON_STATUS='MM',RECON_ID=?,VERSION="+version+" WHERE SID="+sid);
				stagingUpdatePstmt.setObject(1, approveComments);
				stagingUpdatePstmt.setObject(2, reconId);
				int rows=	stagingUpdatePstmt.executeUpdate();
				
				
				
				//////////////////////
				
				
				
				
				///////////////////////////////
			
				
				/////////////////////////////////////////////////////
				
			}
			/*String DOC_ALP=(String)records.get(0).get("DOC_ALP");
			String DOC_NUM=(String)records.get(0).get("DOC_NUM");
			if(records.get(0).get("DOC_ALP")==null)
			{
				DOC_ALP="0000";
			}
			if(records.get(0).get("DOC_NUM")==null)
			{
				DOC_NUM="00000000000";
			}*/
			// ADDING LAST_4_DIGIT_DOC_NO
			
			/*String retrive_4_Digit_Doc_No= DEBITCREDITDETAILS.get("docNum").toString();
			String Last_4_digit_DOC_No="";
			if(retrive_4_Digit_Doc_No.length()>4){
		       	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No.substring(retrive_4_Digit_Doc_No.length()-4);
		        }else{
		        	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No;
		        }*/
			String custTranType= (String) DEBITCREDITDETAILS.get("custTranType");
			int hostval=0;
			int custval=0;
			if(custTranType.equalsIgnoreCase("Credit")){
				hostval=1;
				custval=2;
			}else{
				hostval=2;
				custval=1;

			}
			
			Query glQuery=OperationsUtil.getInsertQueryConf("GENERATE_GL_ENTRY", connection);
			Map<String,Object> generateGlEntryMap=new HashMap<String,Object>();
		//	generateGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
			generateGlEntryMap.put("BRANCH_CODE", DEBITCREDITDETAILS.get("branchCode"));
			generateGlEntryMap.put("CUSTOMER", DEBITCREDITDETAILS.get("customerNum"));
			generateGlEntryMap.put("CHECK_DIGIT", DEBITCREDITDETAILS.get("checkDigit"));
			generateGlEntryMap.put("LEDGER_CODE", DEBITCREDITDETAILS.get("ledgerCode"));
			generateGlEntryMap.put("SUB_ACC_CODE", DEBITCREDITDETAILS.get("subAccCode"));
			generateGlEntryMap.put("DEB_CRE_IND", hostval );
			generateGlEntryMap.put("TRANSACTION_AMOUNT", DEBITCREDITDETAILS.get("transactionAmount") );
			generateGlEntryMap.put("RECON_ID", reconId);
			generateGlEntryMap.put("BUSINESS_AREA", businessArea);
			generateGlEntryMap.put("RECON_NAME", reconName);
			
			generateGlEntryMap.put("CURR_CODE", DEBITCREDITDETAILS.get("currencyCode"));
			generateGlEntryMap.put("GL_FLAG", "N");
			generateGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
			generateGlEntryMap.put("INSERT_USER", userid);
			generateGlEntryMap.put("REMARKS",  remarks);
			//generateGlEntryMap.put("REMARKS",  records.get(0).get("REMARKS"));
			generateGlEntryMap.put("REASON",  comments);
			generateGlEntryMap.put("DOCUMENT_ALPHA",  0000);
			generateGlEntryMap.put("DOCUMENT_NUMBER",  0000);
			
			Map<String,Object> generateOriginalGlEntryMap=new HashMap<String,Object>();
			// generateOriginalGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
			generateOriginalGlEntryMap.put("BRANCH_CODE", CUSTOMERDEBITCREDITDETAILS.get("branchCode_Cust"));
			generateOriginalGlEntryMap.put("CUSTOMER", CUSTOMERDEBITCREDITDETAILS.get("customerNum_Cust"));
			generateOriginalGlEntryMap.put("CHECK_DIGIT",CUSTOMERDEBITCREDITDETAILS.get("checkDigit_Cust"));
			generateOriginalGlEntryMap.put("LEDGER_CODE", CUSTOMERDEBITCREDITDETAILS.get("ledgerCode_Cust"));
			generateOriginalGlEntryMap.put("SUB_ACC_CODE", CUSTOMERDEBITCREDITDETAILS.get("subAccCode_Cust"));
			generateOriginalGlEntryMap.put("DEB_CRE_IND",custval );
			generateOriginalGlEntryMap.put("TRANSACTION_AMOUNT", CUSTOMERDEBITCREDITDETAILS.get("transactionAmount_Cust"));
			generateOriginalGlEntryMap.put("RECON_ID", reconId);
			generateOriginalGlEntryMap.put("BUSINESS_AREA", businessArea);
			generateOriginalGlEntryMap.put("RECON_NAME", reconName);
			generateOriginalGlEntryMap.put("CURR_CODE", CUSTOMERDEBITCREDITDETAILS.get("currencyCode_Cust"));
			generateOriginalGlEntryMap.put("GL_FLAG",  "N");
			generateOriginalGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
			generateOriginalGlEntryMap.put("INSERT_USER", userid);
			generateOriginalGlEntryMap.put("REMARKS",  remarks);
			generateOriginalGlEntryMap.put("DOCUMENT_ALPHA",   0000);
			generateOriginalGlEntryMap.put("DOCUMENT_NUMBER",  0000);
			 generateInsertPstmt=connection.prepareStatement(glQuery.getQueryString());
			Map<String, Object> paramValueMap=new HashMap<String, Object>();
			paramValueMap.put("PARAM_VALUE_MAP", generateGlEntryMap);
			insertRegulator.insert(generateInsertPstmt, paramValueMap, glQuery.getQueryParam());
			// logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
			 generateInsertPstmtOrig=connection.prepareStatement(glQuery.getQueryString());
			Map<String, Object> paramValueMapOrig=new HashMap<String, Object>();
			paramValueMapOrig.put("PARAM_VALUE_MAP", generateOriginalGlEntryMap);
			insertRegulator.insert(generateInsertPstmtOrig, paramValueMapOrig, glQuery.getQueryParam());
			//logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
			
			}catch(Exception e){
				e.printStackTrace();
				//logger.error(e.getMessage()+" "+e);
			}
			finally{
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closePreparedStatement(stagingInsertPstmt);
				DbUtil.closePreparedStatement(stagingTableIdGenPstmt);
				DbUtil.closePreparedStatement(reconTableIdGenPstmt);
				DbUtil.closePreparedStatement(reconTableReconIdGenPstmt);
				DbUtil.closePreparedStatement(reconDataInsertPstmt);
				DbUtil.closePreparedStatement(reconDataSelectFromStagingPstmt);	
				DbUtil.closePreparedStatement(generateInsertPstmt);
				DbUtil.closePreparedStatement(generateInsertPstmtOrig);
				//logger.trace("Activity Approved!..");
			}
		
			
		} else if (REJECTED.equalsIgnoreCase(status)) {
		
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			
			PreparedStatement updateStmt=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			
			try{
				for(Map<String,Object> selectedrecord:records){
					
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					
					for(Map rec : auditData){
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}
					
					String approveComments=userid+" : "+comment;
					
					long sid=(long) selectedrecord.get("SID");
					int version=Integer.parseInt( selectedrecord.get("VERSION").toString());
					++version;
					PreparedStatement stagingUpdatePstmt = connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='N',ACTIVITY_COMMENTS=?,VERSION="+version+" WHERE SID="+sid);
					stagingUpdatePstmt.setObject(1, approveComments);
					
					int rows=	stagingUpdatePstmt.executeUpdate();
					
					
				}
			}catch(Exception e){
				e.printStackTrace();
				//logger.error(e.getMessage()+" "+e);
			}
			finally{
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				//logger.trace("Activity Rejected!..");			
			}
			
		} else if("PENDING".equalsIgnoreCase(status)){
			
		}

	} catch (Exception e) {
		e.printStackTrace();
	}
	finally{
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		//	logger.error(e.getMessage()+" "+e);
		}
				
	}
	return result;
}
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}
}
