package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CreateGlExceptionPlugIn extends BasicDataSource implements PagesConstants{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public CreateGlExceptionPlugIn() {
		
	}
public DSResponse executeFetch(final DSRequest request)throws Exception
{
	Map<String, Object> result = null;
	DSResponse response=new DSResponse();
	@SuppressWarnings("rawtypes")
	Map requestMap=request.getValues();
	
	HttpSession httpSession = request.getHttpServletRequest().getSession();

	User user = (User) httpSession.getAttribute("userId");

	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		response.setData(result);
		return response;
	}
	String userId = user.getUserId();
	String userName=user.getUserName();
	String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
	String reconName = (String) httpSession.getAttribute("user_selected_recon");
	List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) requestMap.get("selectedRecords");
	//String integrationName = (String) requestMap.get("integrationName");
	String action = (String) requestMap.get("action");
	String comments = (String) requestMap.get("comments");
	String moduleName =(String)requestMap.get("moduleName");
	String dsName=(String)requestMap.get("dsName");
	String accCatgeory=(String)requestMap.get("accCatgeory");
	String reconDataSource=(String)requestMap.get("reconDataSource");
	String integrationName=(String)requestMap.get("integrationName");
	Map<String,Object> reconSelectedRecord=(Map<String, Object>) requestMap.get("reconSelectedRecord");
	
	Map<String,Object> debitCreditAccDetails=(Map<String, Object>) requestMap.get("debitCreditAccDetails");
	if (selectedRecords != null) {

		StringBuilder commentSb = new StringBuilder();
		List<Object> workflowIds = new ArrayList<Object>();
		//for (Map<String, Object> rec : selectedRecords) {
          if ((reconSelectedRecord != null && reconSelectedRecord.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) reconSelectedRecord.get("WORKFLOW_STATUS"))) || (reconSelectedRecord != null && reconSelectedRecord.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) reconSelectedRecord.get("WORKFLOW_STATUS")))) {
				
			}else{
				workflowIds.add(reconSelectedRecord.get("RECON_ID"));
			}
		//}*/
		if (workflowIds.size() > 0) {
			result = new HashMap<String, Object>();
			String commentPrefix = " ";
			if (workflowIds.size() == 1) {
				commentSb.append("Selected record with RECON_ID ");
			} else if (workflowIds.size() > 1) {
				commentSb.append("Selected records with RECON_IDs ");
			}
			for (Object obj : workflowIds) {
				if (commentSb.length() != 0) {
					commentSb.append(",");
				}
				commentSb.append(obj);
			}
			commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
			updateResultStatus(result, FAILED, commentPrefix);
			response.setData(result);
			return response;
		}
					
	}
Map<String, Object> paramsMap = new HashMap<String, Object>();
	
	paramsMap.put(ACTION, action);
	paramsMap.put(USER_ID, userId);
	paramsMap.put(SELECTED_RECORDS, selectedRecords);
	paramsMap.put(BUSINES_AREA, businesArea);
	paramsMap.put(RECON_NAME, reconName);
	paramsMap.put(COMMENTS, comments);
	paramsMap.put(MODULE, moduleName);
	paramsMap.put(DS_NAME, dsName);
	paramsMap.put(ACC_CATEGORY, accCatgeory);
	paramsMap.put(DEBIT_CREDIT_DETAILS, debitCreditAccDetails);
	paramsMap.put("reconSelectedRecord", reconSelectedRecord);
	paramsMap.put("reconDataSource", reconDataSource);
	paramsMap.put("integrationName", integrationName);
	
	
	result = process(paramsMap);
	response.setData(result);
	return response;
}
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}
private Map<String, Object> process(Map<String, Object> createGlExceptionArgs) {

	Connection connection = null;
	Map<String, Object> result = null;
	PreparedStatement selectAuditStmt=null;
	PreparedStatement auditInsertPstmt=null;
	PreparedStatement reconDataSelectPstmt=null;
	PreparedStatement stagingDataSelectPstmt=null;
	PreparedStatement auditDataInsertPstmt=null;
	PreparedStatement stagingDataUpdatePstmt=null;
	PreparedStatement reconDataUpdatePstmt=null;
	try {
		
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();

		String userId = (String) createGlExceptionArgs.get(USER_ID);
		String dsName = (String) createGlExceptionArgs.get(DS_NAME);
		createGlExceptionArgs.put(PERSIST_CLASS, CREATE_GL_EXCEPTION_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", createGlExceptionArgs);
	//	String moduleName=(String) createGlExceptionArgs.get(MODULE);
		
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;

			String businessArea = (String) createGlExceptionArgs.get(BUSINES_AREA);
			String reconName = (String) createGlExceptionArgs.get(RECON_NAME);
			String comments = (String) createGlExceptionArgs.get(COMMENTS);
			String requesterComments=userId+" : "+comments;
			String moduleName=(String)createGlExceptionArgs.get(MODULE);
			String reconDataSource=(String)createGlExceptionArgs.get("reconDataSource");
			Map<String,Object> reconSelectedRecord=(Map<String, Object>) createGlExceptionArgs.get("reconSelectedRecord");
			String integrationName=(String) createGlExceptionArgs.get("integrationName");
			List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) createGlExceptionArgs.get(SELECTED_RECORDS);
			long reconId=(long) reconSelectedRecord.get("RECON_ID");
			String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
			reconDataSelectPstmt=connection.prepareStatement("select SID,RECON_SIDE,VERSION from "+reconTableName+" where RECON_ID="+reconId);
			ResultSet reconselectrs=reconDataSelectPstmt.executeQuery();
			LoadRegulator loadRegulator=new LoadRegulator();
			InsertRegulator insertRegulator=new InsertRegulator();
			while(reconselectrs.next()){
				int recVersion= Integer.parseInt(reconselectrs.getObject("VERSION").toString());
				++recVersion;
				long sid=(long) reconselectrs.getObject("SID");
				String reconSide=(String) reconselectrs.getObject("RECON_SIDE");
				String stgTableName=reconSide+"_STG";
				String auditTableName=reconSide+"_STG_AUDIT";
				if(reconSide.equalsIgnoreCase("AUTH_ISS")){
					
					stgTableName="AUTH_ISSUER_STG";
					auditTableName="AUTH_ISSUER_STG_AUDIT";
				}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
					
					stgTableName="AUTH_ACQUIRER_STG";
					auditTableName="AUTH_ACQUIRER_STG_AUDIT";
				}else if(reconSide.equalsIgnoreCase("MAST")){
					
					stgTableName="MASTER_STG";
					auditTableName="MASTER_STG_AUDIT";
				}else if(reconSide.equalsIgnoreCase("CTL")){
					
					stgTableName="CISO_STG";
					auditTableName="CISO_STG_AUDIT";
				}
				Map selectedRec=new HashMap();
				
				selectedRec.put("SID", sid);
				String auditSelectQry="	select * from "+stgTableName+"  where version=(	select max(version) from "+stgTableName+" where sid =?) and sid=?";
				selectAuditStmt =connection.prepareStatement(auditSelectQry);
				List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedRec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
				
				Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
				auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
				Map paramValueMap=new HashMap();
				int version=0;
				for(Map rec : auditData){
					version=Integer.parseInt(rec.get("VERSION").toString());
					paramValueMap.put("PARAM_VALUE_MAP", rec);
					insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
				}
				
			//	selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				++version;
				String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='Y',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
				stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
				stagingDataUpdatePstmt.setObject(1, version);
				stagingDataUpdatePstmt.setObject(2, requesterComments);
				stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
				stagingDataUpdatePstmt.setObject(4, sid);
				int row=	stagingDataUpdatePstmt.executeUpdate();
				
				
				
				reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='Y',RECON_ID=?,STATUS='CREATE GL REQUEST',UPDATED_ON=?,VERSION=?  WHERE SID=?");
				reconDataUpdatePstmt.setObject(1, reconId);
				
				reconDataUpdatePstmt.setObject(2, new Timestamp(Calendar.getInstance().getTimeInMillis()));
				//reconDataUpdatePstmt.setObject(4, userId);
				reconDataUpdatePstmt.setObject(3, recVersion);
				reconDataUpdatePstmt.setObject(4, sid);
			    int r=	reconDataUpdatePstmt.executeUpdate();
				
			}
			
			
			
			
			
			
			userAdminManager.createActivity(connection, user, businessArea, reconName,moduleName,
					CREATE_GL_OPERATION_EXCEPTION, activityDataMap, activityStatus, comments);
			
			
			
			
			
			
			
			
			updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);	
		}
		else {
			//result = persist(activityDataMap, APPROVED, connection);

		}

	
}
	catch (Exception e){
		e.printStackTrace();
		updateResultStatus(result, FAILED,OPERATION_FAILED );
		
	}
	finally{
		DbUtil.closePreparedStatement(selectAuditStmt);
		DbUtil.closePreparedStatement(auditInsertPstmt);
		DbUtil.closePreparedStatement(auditDataInsertPstmt);
		DbUtil.closePreparedStatement(reconDataSelectPstmt);
		DbUtil.closePreparedStatement(stagingDataSelectPstmt);
		DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
		DbUtil.closePreparedStatement(reconDataUpdatePstmt);
		
				
	}
	return result;
	
}
public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {

	Map<String, Object> result = new HashMap<String, Object>();
	LoadRegulator loadRegulator=new LoadRegulator();
	InsertRegulator insertRegulator=new InsertRegulator();
	PreparedStatement selectAuditStmt=null;
	PreparedStatement auditInsertPstmt=null;
	PreparedStatement reconDataSelectPstmt=null;
	PreparedStatement stagingDataSelectPstmt=null;
	PreparedStatement auditDataInsertPstmt=null;
	PreparedStatement stagingDataUpdatePstmt=null;
	PreparedStatement stagingInsertPstmt=null;
	PreparedStatement reconDataSelectFromStagingPstmt=null;
	PreparedStatement reconDataUpdatePstmt=null;
	PreparedStatement reconDataInsertPstmt=null;
	PreparedStatement generateInsertPstmt=null;
	try {
		
		
		
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
        String userid=(String) activityDataMap.get("userId");
		String comment=(String) activityDataMap.get("comment");
		Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
		
		List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		List<Map<String, Object>> glList=new ArrayList<Map<String, Object>>();
		
		
		Map<String,Object> debitCreditAccDetails=(Map<String, Object>) activityRecordsMap.get(DEBIT_CREDIT_DETAILS);
		connection = DbUtil.getConnection();
		
		String businessArea = (String) activityRecordsMap.get(BUSINES_AREA);
		String reconName = (String) activityRecordsMap.get(RECON_NAME);
		String comments = (String) activityRecordsMap.get(COMMENTS);
		String approverComments=userid+" : "+comments;
		String reconDataSource=(String)activityRecordsMap.get("reconDataSource");
		Map<String,Object> reconSelectedRecord=(Map<String, Object>) activityRecordsMap.get("reconSelectedRecord");
		String integrationName=(String) activityRecordsMap.get("integrationName");
		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		long reconId=(long) reconSelectedRecord.get("RECON_ID");
		String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
		
		 reconDataSelectPstmt=connection.prepareStatement("select SID,RECON_SIDE,VERSION from "+reconTableName+" where RECON_ID="+reconId);
		 ResultSet reconselectrs=reconDataSelectPstmt.executeQuery();
		
		 String debitOrCredit=null;
		 
		
		
		if (APPROVED.equalsIgnoreCase(status)) {
			
			 for(Map<String,Object> selectedrecord:records){
				
				 String tableNameStg=integrationName+"_STG";
					String tableNameAudit=integrationName+"_STG_AUDIT";
				
					if(integrationName.equalsIgnoreCase("AUTH_ISS")){
						
						tableNameStg="AUTH_ISSUER_STG";
						tableNameAudit="AUTH_ISSUER_STG_AUDIT";
					}else if(integrationName.equalsIgnoreCase("AUTH_ACQ")){
						
						tableNameStg="AUTH_ACQUIRER_STG";
						tableNameAudit="AUTH_ACQUIRER_STG_AUDIT";
					}else if(integrationName.equalsIgnoreCase("MAST")){
						
						tableNameStg="MASTER_STG";
						tableNameAudit="MASTER_STG_AUDIT";
					}else if(integrationName.equalsIgnoreCase("CTL")){
						
						tableNameStg="CISO_STG";
						tableNameAudit="CISO_STG_AUDIT";
					}
				 
					String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
					   String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
						
					 PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
					String stagingTableIdSeqName = tableNameStg + "MM_ID_SEQ";
					  String stagingTableIdGenQry = "SELECT NEXT VALUE FOR " + stagingTableIdSeqName + " as sno";
						String idseqname=tableNameStg + "MM_ID_SEQ";
						
					 PreparedStatement stagingTableIdGenPstmt = connection.prepareStatement(stagingTableIdGenQry);
					 String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
				 
				 
		        	selectAuditStmt=connection.prepareStatement(auditSelectQry);
		        	selectAuditStmt.setObject(1, selectedrecord.get("SID"));
		        	List<Map<String,Object>> insertData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		        	Query insertQuery=OperationsUtil.getInsertQueryConf(tableNameStg, connection);
		        	stagingInsertPstmt=connection.prepareStatement(insertQuery.getQueryString());
					Map paramValueMap=new HashMap();
					for(Map rec : insertData){
						long sid=PersistanceUtil.generateSeqNo(connection, stagingTableIdGenPstmt, stagingTableIdSeqName);
						rec.put("SID", sid);
						rec.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
						rec.put("VERSION",1);
						rec.put("UPDATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
						debitOrCredit=(String) rec.get("DEB_CRE_IND");
						if(debitOrCredit.equalsIgnoreCase("DEBIT")){
							debitOrCredit="CREDIT";
						}else if(debitOrCredit.equalsIgnoreCase("CREDIT")){
							debitOrCredit="DEBIT";
						}
						rec.put("DEB_CRE_IND",debitOrCredit);
						rec.put("COMMENTS", "New Gl Entry");
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(stagingInsertPstmt, paramValueMap, insertQuery.getQueryParam());
						//glList.add(rec);
						String queryName=reconTableName+"_"+integrationName; 
						Query query = queries.getQueryConf(queryName);
						reconDataSelectFromStagingPstmt=connection.prepareStatement(query.getQueryString());
						reconDataSelectFromStagingPstmt.setObject(1, sid);
						ResultSet unreconRs=reconDataSelectFromStagingPstmt.executeQuery();
						ResultSetMetaData unreconRsm= unreconRs.getMetaData();
						int columnCount=unreconRsm.getColumnCount();
						Map<String,Object> reconDataMap=new HashMap<String,Object>();
						while(unreconRs.next()){
						
							
							for(int i=1;i<=columnCount;i++){
								System.out.println(unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
								reconDataMap.put(unreconRsm.getColumnName(i), unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
							}
						}
					
						
						
						reconDataMap.put("ID", PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName));
						reconDataMap.put("RECON_ID", reconId);
						reconDataMap.put("UPDATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconDataMap.put("MATCH_TYPE",  "MM");
						reconDataMap.put("USER_ID",  "SYSTEM");
						reconDataMap.put("WORKFLOW_STATUS",  "N");
						reconDataMap.put("ACTIVE_INDEX",  "Y");
						reconDataMap.put("SID", sid );
						reconDataMap.put("VERSION", 1);
						reconDataMap.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
						//reconDataMap.put("COMMENTS",  "SYSTEM");
					    Query reconQuery=	OperationsUtil.getInsertQueryConf(reconTableName, connection);
					    reconDataInsertPstmt=connection.prepareStatement(reconQuery.getQueryString());
					    
					    Map<String, Object> paramValuesMap=new HashMap<String, Object>();
					    paramValuesMap.put("PARAM_VALUE_MAP", reconDataMap);
					 
					    insertRegulator.insert(reconDataInsertPstmt, paramValuesMap, reconQuery.getQueryParam());
						
						
						
						
						
					}
		        	
		        	
		        }
			 
			
				
				while(reconselectrs.next()){
					long sid=(long) reconselectrs.getObject("SID");
					int recVersion= Integer.parseInt(reconselectrs.getObject("VERSION").toString());
					++recVersion;
					String reconSide=(String) reconselectrs.getObject("RECON_SIDE");
					String stgTableName=reconSide+"_STG";
					String auditTableName=reconSide+"_STG_AUDIT";
					if(reconSide.equalsIgnoreCase("AUTH_ISS")){
						
						stgTableName="AUTH_ISSUER_STG";
						auditTableName="AUTH_ISSUER_STG_AUDIT";
					}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
						
						stgTableName="AUTH_ACQUIRER_STG";
						auditTableName="AUTH_ACQUIRER_STG_AUDIT";
					}else if(reconSide.equalsIgnoreCase("MAST")){
						
						stgTableName="MASTER_STG";
						auditTableName="MASTER_STG_AUDIT";
					}else if(reconSide.equalsIgnoreCase("CTL")){
						
						stgTableName="CISO_STG";
						auditTableName="CISO_STG_AUDIT";
					}
					Map selectedRec=new HashMap();
					
					selectedRec.put("SID", sid);
					String auditSelectQry="	select * from "+stgTableName+"  where version=(	select max(version) from "+stgTableName+" where sid =?) and sid=?";
					selectAuditStmt =connection.prepareStatement(auditSelectQry);
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedRec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
					auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					Map paramValueMap=new HashMap();
					int version=0;
					for(Map rec : auditData){
						version=Integer.parseInt(rec.get("VERSION").toString());
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}
					
				//	selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
					++version;
					String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
					stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
					stagingDataUpdatePstmt.setObject(1, version);
					stagingDataUpdatePstmt.setObject(2, approverComments);
					stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					stagingDataUpdatePstmt.setObject(4, sid);
					int row=	stagingDataUpdatePstmt.executeUpdate();
	
					reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='N',RECON_ID=?,STATUS='CREATE GL',MATCH_TYPE=?,UPDATED_ON=?,VERSION=?  WHERE SID=?");
					reconDataUpdatePstmt.setObject(1, reconId);
					reconDataUpdatePstmt.setObject(2, "MM");
					reconDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					//reconDataUpdatePstmt.setObject(4, userId);
					reconDataUpdatePstmt.setObject(4, recVersion);
					reconDataUpdatePstmt.setObject(5, sid);
				    int r=	reconDataUpdatePstmt.executeUpdate();
					
					
				}
			 
			 
				Map<String,Object> generateGlEntryMap=new HashMap<String,Object>();
				generateGlEntryMap.put("BRANCH_CODE", debitCreditAccDetails.get("bra_code"));
				generateGlEntryMap.put("CUSTOMER", debitCreditAccDetails.get("cust_ID"));
				generateGlEntryMap.put("CHECK_DIGIT", debitCreditAccDetails.get("check_digit"));
				generateGlEntryMap.put("LEDGER_CODE", debitCreditAccDetails.get("led_code"));
				generateGlEntryMap.put("SUB_ACC_CODE", debitCreditAccDetails.get("sub_acc_code"));
				generateGlEntryMap.put("DEB_CRE_IND", debitOrCredit);
				generateGlEntryMap.put("TRANSACTION_AMOUNT", records.get(0).get("TRA_AMT"));
				generateGlEntryMap.put("RECON_ID", reconId);
				generateGlEntryMap.put("BUSINESS_AREA", businessArea);
				generateGlEntryMap.put("RECON_NAME", reconName);
				generateGlEntryMap.put("GL_FLAG", "N");
				generateGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
				generateGlEntryMap.put("INSERT_USER", userid);
				
				Query glQuery=OperationsUtil.getInsertQueryConf("GENERATE_GL_ENTRY", connection);
				 generateInsertPstmt=connection.prepareStatement(glQuery.getQueryString());
				Map<String, Object> paramValueMap=new HashMap<String, Object>();
				paramValueMap.put("PARAM_VALUE_MAP", generateGlEntryMap);
				insertRegulator.insert(generateInsertPstmt, paramValueMap, glQuery.getQueryParam());
			 
			 
			
			
			
			
			
			
			
			
		} else if (REJECTED.equalsIgnoreCase(status)) {
			
			
			while(reconselectrs.next()){
				int recVersion= Integer.parseInt(reconselectrs.getObject("VERSION").toString());
			long sid=(long) reconselectrs.getObject("SID");
			String reconSide=(String) reconselectrs.getObject("RECON_SIDE");
			String stgTableName=reconSide+"_STG";
			String auditTableName=reconSide+"_STG_AUDIT";
			if(reconSide.equalsIgnoreCase("AUTH_ISS")){
				
				stgTableName="AUTH_ISSUER_STG";
				auditTableName="AUTH_ISSUER_STG_AUDIT";
			}else if(reconSide.equalsIgnoreCase("AUTH_ACQ")){
				
				stgTableName="AUTH_ACQUIRER_STG";
				auditTableName="AUTH_ACQUIRER_STG_AUDIT";
			}else if(reconSide.equalsIgnoreCase("MAST")){
			
				stgTableName="MASTER_STG";
				auditTableName="MASTER_STG_AUDIT";
			}else if(reconSide.equalsIgnoreCase("CTL")){
				
				stgTableName="CISO_STG";
				auditTableName="CISO_STG_AUDIT";
			}
			Map selectedRec=new HashMap();
			
			selectedRec.put("SID", sid);
			String auditSelectQry="	select * from "+stgTableName+"  where version=(	select max(version) from "+stgTableName+" where sid =?) and sid=?";
			selectAuditStmt =connection.prepareStatement(auditSelectQry);
			List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedRec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
			
			Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
			auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
			Map paramValueMap=new HashMap();
			int version=0;
			for(Map rec : auditData){
				version=Integer.parseInt(rec.get("VERSION").toString());
				paramValueMap.put("PARAM_VALUE_MAP", rec);
				insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
			}
			
		//	selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			++version;
			String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
			stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
			stagingDataUpdatePstmt.setObject(1, version);
			stagingDataUpdatePstmt.setObject(2, approverComments);
			stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
			stagingDataUpdatePstmt.setObject(4, sid);
			int row=	stagingDataUpdatePstmt.executeUpdate();
					
			++recVersion;
			reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='N',RECON_ID=?,STATUS='CREATE GL REJECTED',UPDATED_ON=?,VERSION=?  WHERE SID=?");
			reconDataUpdatePstmt.setObject(1, reconId);
			
			reconDataUpdatePstmt.setObject(2, new Timestamp(Calendar.getInstance().getTimeInMillis()));
			//reconDataUpdatePstmt.setObject(4, userId);
			reconDataUpdatePstmt.setObject(3, recVersion);
			reconDataUpdatePstmt.setObject(4, sid);
		    int r=	reconDataUpdatePstmt.executeUpdate();
			
		}
			} 
		else if("PENDING".equalsIgnoreCase(status)){
			
		}

	} catch (Exception e) {
		e.printStackTrace();
	}finally{
		DbUtil.closePreparedStatement(selectAuditStmt);
		DbUtil.closePreparedStatement(auditInsertPstmt);
		DbUtil.closePreparedStatement(auditDataInsertPstmt);
		DbUtil.closePreparedStatement(reconDataSelectPstmt);
		DbUtil.closePreparedStatement(stagingDataSelectPstmt);
		DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
		DbUtil.closePreparedStatement(reconDataUpdatePstmt);
		DbUtil.closePreparedStatement(generateInsertPstmt);
		
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
				
	}
	return result;
}





}
