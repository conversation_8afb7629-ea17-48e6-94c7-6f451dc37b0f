package com.ascent.ds.login;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpSession;
import com.ascent.service.dto.User;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class <PERSON><PERSON><PERSON><PERSON> extends BasicDataSource {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private User user;

	public SessionChecker() {
		System.out.println(" Session Checker... ");

	}

	static SessionChecker checker = new SessionChecker();
	private static final String ACTION = "CHAIN_ACTION_VIEW";
	private static final String EXPIRED_SESSION = "Session Is Expired ,Please Re-Login";

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		DSResponse dsResponse = new DSResponse();
		Map<String, String> respMsg = new HashMap<String, String>();
		Map criteriaMap = (Map) request.getValues();
		
		String action = "";
		if(criteriaMap.get("action")!=null){
			 action = (String) criteriaMap.get("action");
		}
		 if (action != null && action.equals(ACTION)) {

			updateSessionValues(criteriaMap, request);

		} else {

			User logedInUser = SessionChecker.userSessionChecker(request);

			if (logedInUser == null) {
				respMsg.put("EXPIRED_SESSION", EXPIRED_SESSION);
				dsResponse.setData(respMsg);
				return dsResponse;
			} else {
				return dsResponse;
			}
		}
		return dsResponse;
	}

	private void updateSessionValues(Map criteriaMap, DSRequest request) {

		HttpSession httpSession = request.getHttpServletRequest().getSession();
		String selectedBisunessArea = (String) criteriaMap.get("B_Area");
		String selectedRecon = (String) criteriaMap.get("Recon");
		httpSession.setAttribute("user_selected_business_area", selectedBisunessArea);
		httpSession.setAttribute("user_selected_recon", selectedRecon);
		
		
		/*httpSession.removeAttribute("fromDate");
		httpSession.removeAttribute("toDate");
*/
		System.out.println("***************** NEW VALUES UPDATED IN SESSION ************ ");
	}

	private static User userSessionChecker(DSRequest dsRequest) {
		HttpSession httpSession = dsRequest.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		System.out.println(user.getUserId() + " " + user.getUserName());
		return user;
	}

	public User sessionUser(DSRequest dsRequest) {
		HttpSession httpSession = dsRequest.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		System.out.println(user.toString());
		SessionChecker checker = new SessionChecker();
		checker.setUser(user);

		return user;
	}

	public static User getUserSession(DSRequest dsRequest) {
		User user = userSessionChecker(dsRequest);

		System.out.println(" Session Checker *** current Session " + user);

		return user;

	}

	/**
	 * @return the user
	 */
	public User getUser() {
		return user;
	}

	/**
	 * @param user
	 *            the user to set
	 */
	public void setUser(User user) {
		this.user = user;
	}

}
