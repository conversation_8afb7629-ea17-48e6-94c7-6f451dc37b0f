package com.ascent.admin.authorize;

import java.io.IOException;
import java.io.Serializable;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.SessionScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.ValueChangeEvent;

import org.primefaces.context.RequestContext;

import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;

@ManagedBean(name="activityManager")
@SessionScoped
public class  ActivityManager implements PagesConstants,Serializable {
	
	private static final long serialVersionUID = -3450210764190353216L;
	private ArrayList<Map<String, Object>> requestedActivities;
	private ArrayList<Map<String, Object>> activitiesForApproval;
	private ArrayList<Map<String, Object>>  selectedActivity;
	private ArrayList<Map<String, Object>>  auditTrail;
	private int activeIndex;
	
	private String comment;
	private String submitType;
	private User user;
	
	@SuppressWarnings("unchecked")
	@PostConstruct
	public void init(){
		intialize();
	}	
	
	public void intialize(){
		
		this.selectedActivity=new ArrayList<Map<String,Object>>();
		this.auditTrail = new ArrayList<Map<String,Object>>();
		// this.user = BeanUtil.getLoginUser( FacesContext.getCurrentInstance());
		
		UserAdminManager userAdmin = UserAdminManager.getAuthorizationManagerSingleTon();
		
		if(user!=null){
		try {
			//this.requestedActivities = (ArrayList<Map<String, Object>>) userAdmin.getActivityByUser(user, reconName);
			//this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getPendingActivitiesForUser(user);
			this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getPendingActivitiesForUser(user,"");
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		}else{
			this.requestedActivities=new ArrayList<Map<String,Object>>();
			this.activitiesForApproval=new ArrayList<Map<String,Object>>();			
		}
	}
	String submitTempType;
	public void submitType(ActionEvent event) {		
		submitType = (String) event.getComponent().getAttributes().get("submitType");		
	}
	public void loadApprovals(ValueChangeEvent event){
		String queryType = (String) event.getNewValue();
		UserAdminManager userAdmin = UserAdminManager.getAuthorizationManagerSingleTon();
		if(queryType.equalsIgnoreCase("Pending")){
			try {
				//this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getPendingActivitiesForUser(user);
				this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getPendingActivitiesForUser(user,"");
			} catch (ClassNotFoundException e) {				
				e.printStackTrace();
			} catch (SQLException e) {				
				e.printStackTrace();
			} catch (IOException e) {				
				e.printStackTrace();
			}
		}else if(queryType.equalsIgnoreCase("Approved")){
			try {
				this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getApprovedActivitiesForUser(user);
			} catch (ClassNotFoundException e) {				
				e.printStackTrace();
			} catch (SQLException e) {				
				e.printStackTrace();
			} catch (IOException e) {				
				e.printStackTrace();
			}
		}else if(queryType.equalsIgnoreCase("Rejected")){
			try {
				this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getRejectedActivitiesForUser(user);
			} catch (ClassNotFoundException e) {				
				e.printStackTrace();
			} catch (SQLException e) {				
				e.printStackTrace();
			} catch (IOException e) {				
				e.printStackTrace();
			}
		}else if(queryType.equalsIgnoreCase("All")){
			try {
				this.activitiesForApproval = (ArrayList<Map<String, Object>>) userAdmin.getActivitiesForUser(user);
			} catch (ClassNotFoundException e) {			
				e.printStackTrace();
			} catch (SQLException e) {				
				e.printStackTrace();
			} catch (IOException e) {				
				e.printStackTrace();
			}
		}
	}
	public void processActivity(ActionEvent event){
		String reSubmit =(String) event.getComponent().getAttributes().get("submitType");
		if(reSubmit != null){
			if(reSubmit.equalsIgnoreCase(RE_SUBMIT)){
				Map<String, Object> dataMap =(Map<String, Object>) event.getComponent().getAttributes().get("EntityMap");			
				
				Map<String,Object> activityInfoMap=(Map<String,Object>)dataMap.get("activity_info");
				Long activityId=(Long) activityInfoMap.get("activity_id");
				int activityLevel=(Integer) activityInfoMap.get("activity_level");
				
				Map<String,Object> activityData=new HashMap<String, Object>();
				activityData.putAll(activityInfoMap);
				dataMap.remove("activity_info");
				activityData.put("activity_data",dataMap);	
				
				String comment ="ReSubmit";
				UserAdminManager userAdminManager=UserAdminManager.getAuthorizationManagerSingleTon();
				
				try{
				userAdminManager.processActivity(activityId, activityLevel, RE_SUBMIT,user.getUserId(),comment,activityData);
				}catch(Exception e){
					e.printStackTrace();
					FacesContext.getCurrentInstance().addMessage(
							null,
							new FacesMessage(FacesMessage.SEVERITY_WARN,
									"", "Unable to process the activity"));
				}
				return;
			}
		}
		
		if (APPROVED.equalsIgnoreCase(submitType)	|| REJECTED.equalsIgnoreCase(submitType)) {
			org.primefaces.context.RequestContext.getCurrentInstance().execute("PF('commentDialog').hide()");
		}
		if(comment != null){
		}	
		UserAdminManager userAdminManager=UserAdminManager.getAuthorizationManagerSingleTon();
		for (Map<String, Object> map : selectedActivity) {
			Long activityId=(Long) map.get("activity_id");
			int activityLevel=(Integer) map.get("activity_level");
		try{	
			userAdminManager.processActivity(activityId, activityLevel, submitType, user.getUserId(),comment,null);
		}catch(Exception e){
			e.printStackTrace();
			FacesContext.getCurrentInstance().addMessage(
					null,
					new FacesMessage(FacesMessage.SEVERITY_WARN,
							"", "Unable to process the activity"));
		}			
		}	
	}
	public void updateAuditTrail(ActionEvent event){/*
		System.out.println("ID: "+event.getComponent().getAttributes().get("activityID"));
		long activityID = (long) event.getComponent().getAttributes().get("activityID");
		UserAdminManager userAdmin = UserAdminManager.getAuthorizationManagerSingleTon();
		try {
			this.auditTrail = (ArrayList<Map<String, Object>>) userAdmin.getActivityAudit(activityID);
		} catch (ClassNotFoundException e) {			
			e.printStackTrace();
		} catch (SQLException e) {			
			e.printStackTrace();
		} catch (IOException e) {			
			e.printStackTrace();
		}
		for (Map<String, Object> map : auditTrail) {
			System.out.println("Audit trail: "+map.get("activity_id"));
		}		
	*/}	
	public void updateActiveIndex(ActionEvent event){
		this.activeIndex = Integer.parseInt((String) event.getComponent().getAttributes().get("activeIndex"));
		RequestContext.getCurrentInstance().update("upperTabView:activityManager");
	}
	public ArrayList<Map<String, Object>> getRequestedActivities() {
		return requestedActivities;
	}
	public void setRequestedActivities(ArrayList<Map<String, Object>> requestedActivities) {
		this.requestedActivities = requestedActivities;
	}
	public ArrayList<Map<String, Object>> getActivitiesForApproval() {
		return activitiesForApproval;
	}
	public void setActivitiesForApproval(
			ArrayList<Map<String, Object>> activitiesForApproval) {
		this.activitiesForApproval = activitiesForApproval;
	}
	public ArrayList<Map<String, Object>> getSelectedActivity() {
		return selectedActivity;
	}
	public void setSelectedActivity(ArrayList<Map<String, Object>> selectedActivity) {
		this.selectedActivity = selectedActivity;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	public String getSubmitType() {
		return submitType;
	}
	public void setSubmitType(String submitType) {
		this.submitType = submitType;
	}
	public ArrayList<Map<String, Object>> getAuditTrail() {
		return auditTrail;
	}
	public void setAuditTrail(ArrayList<Map<String, Object>> auditTrail) {
		this.auditTrail = auditTrail;
	}
	public int getActiveIndex() {
		return activeIndex;
	}
	public void setActiveIndex(int activeIndex) {
		this.activeIndex = activeIndex;
	}
}
