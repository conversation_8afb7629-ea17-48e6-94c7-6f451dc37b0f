package com.ascent.persistance;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class RegulatorUtil {
	private static Logger logger = LogManager.getLogger(LoadRegulator.class
			.getName());
	
	public static void closeConnection(Connection connection) {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {

			logger.error("ERROR OCCURED WHILE CLOSING THE CONNECTION", e);
		}
	}

	public static void closePreparedStatement(PreparedStatement preparedStatement) {
		try {
			// System.out.println(preparedStatement.isClosed());
			if (preparedStatement != null && !preparedStatement.isClosed()) {
				preparedStatement.close();
			}
		} catch (Exception e) {

			logger.error("ERROR OCCURED WHILE CLOSING THE PREPARED STATEMENT", e);
		}
	}
	
	public static void closeStatement(Statement statement) {
		try {
			
			if (statement != null && !statement.isClosed()) {
				statement.close();
			}
		} catch (Exception e) {

			logger.error("ERROR OCCURED WHILE CLOSING THE  STATEMENT", e);
		}
	}
	
	public static void closeResultSet(ResultSet resultSet) {
		try {
			
			if (resultSet != null && !resultSet.isClosed()) {
				resultSet.close();
			}
		} catch (Exception e) {

			logger.error("ERROR OCCURED WHILE CLOSING THE  RESULTSET", e);
		}
	}
	
	
	// Utility Methods---Start------------------------------------------------------------

	
}
