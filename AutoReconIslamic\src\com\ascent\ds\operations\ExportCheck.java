package com.ascent.ds.operations;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.ServletOutputStream;

import com.isomorphic.servlet.*;
import com.isomorphic.rpc.*;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.isomorphic.log.Logger;

public class ExportCheck {
    private static final long serialVersionUID = 1L;
    private static Logger log = new Logger(DSRequest.class.getName());
    
	public ExportCheck() {
		System.out.println("ExportCheck class is called");
	}
	public static void customExport(RPCManager rpc, HttpServletResponse response, DSRequest request)throws Exception
		    {
		        try{
		        	 rpc.doCustomResponse();
		        	 Map dataMap=request.getCriteria();
		        	 System.out.println("data map : "+dataMap);
		        	 String FILE_PATH=(String)dataMap.get("FilePath");
		        	 String FIle_Name=(String)dataMap.get("FileName");
                     RequestContext.setNoCacheHeaders(response);
                     String sCurrentLine;
		             response.setContentType("application/dat");
		             response.addHeader("content-disposition", "attachment; filename="+FIle_Name+".dat");
		             log.warn("about to fetch data");
		             StringBuilder content = new StringBuilder();
		             BufferedReader br = null;
		             String path="D:\\BULK_FILES\\Onus_Debit_ATM_Bulk_2016011805_29_02.dat";
		             br = new BufferedReader(new FileReader(FILE_PATH));
		             while ((sCurrentLine = br.readLine()) != null) {
		 				System.out.println(sCurrentLine);
		 					content.append("\n");
		 					content.append(sCurrentLine);
		 					content.append("\n");
		 		       
		 			}
		             log.warn("FINDME - content is " + content.toString());
		             ServletOutputStream os = response.getOutputStream();
		             os.print(content.toString());
		           
		             os.flush();
		        }catch(Exception e){
		        	e.printStackTrace();
		        	
		        }
		        return;
		    }
}
