package com.ascent.ds.operations;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.LoadRegulator;
import com.ascent.util.PagesConstants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.core.util.MultivaluedMapImpl;

/**
 * <AUTHOR>
 *
 */
public class EtlRecon extends BasicDataSource implements PagesConstants{

	private static final long serialVersionUID = 1L;

	private static final String ETL_STATUS = "ETL_STATUS";
	private static final String RECON_STATUS = "RECON_STATUS";

	private static Logger logger = LogManager.getLogger(EtlRecon.class.getName());
	
	DSResponse dsResponse = new DSResponse();
	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	LoadRegulator loadRegulator = new LoadRegulator();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();

	public DSResponse executeFetch(final DSRequest request)throws Exception 
	{
		@SuppressWarnings("rawtypes")
		Map requestParams = request.getCriteria();
		String action = (String) requestParams.get("action");

		String fromDate = null;
		String toDate = null;
		if(action.endsWith("-ETL")) {
			fromDate = new SimpleDateFormat("yyyy-MM-dd").format((Date) requestParams.get("from_date"));
			toDate = new SimpleDateFormat("yyyy-MM-dd").format((Date) requestParams.get("to_date"));
		}

		if (action != null && action.equals("ETL-STATUS")) {

			String reconName = (String) requestParams.get("reconName");

			List<Map<String, Object>> etlStatus = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(ETL_STATUS);
			paramMap.put("MODULE", reconName);
			etlStatus = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(etlStatus);
			logger.debug("etlStatus : "+etlStatus);

		}
		else if (action != null && action.equals("RECON-STATUS")) {

			String reconName = (String) requestParams.get("reconName");

			List<Map<String, Object>> reconStatus = new ArrayList<Map<String, Object>>();
			Map<String, Object> paramMap = new HashMap<String, Object>();
			Query queryConf = queryConfs.getQueryConf(RECON_STATUS);
			paramMap.put("MODULE", reconName);
			reconStatus = loadRegulator.loadCompleteData(paramMap, queryConf);
			dsResponse.setData(reconStatus);
			logger.debug("statusEtlRecon : "+reconStatus);

		}

		//FINANCE DEPARTMENT
		if (action != null && action.equals("ONS-ETL")) {

			Map<String,Object> result = callWebService("checkonsetl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ONS-RECON")) {

			Map<String,Object> result = callWebService("checkONSForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("MPCLEAR-ETL")) {

			Map<String,Object> result = callWebService("checkmpclearetl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("MPCLEAR-RECON")) {

			Map<String,Object> result = callWebService("checkMPCLEARForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("SUSPENSE ACCOUNTS-ETL")) {

			Map<String,Object> result = callWebService("checkSuspenseForETL.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("SUSPENSE ACCOUNTS-RECON")) {

			Map<String,Object> result = callWebService("checkSuspenseForReconRun.ascent");
			dsResponse.setData(result);

		}

		//CARD DEPARTMENT
		else if (action != null && action.equals("ATM MASTER CARD ACQUIRER-ETL")) {

			Map<String,Object> result = callEtlWebService("checkatmmccEtl.ascent",fromDate,toDate);
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM MASTER CARD ACQUIRER-RECON")) {

			Map<String,Object> result = callWebService("checkATMMASTERCARDACQUIRERForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM VISA ACQUIRER-ETL")) {

			Map<String,Object> result = callEtlWebService("checkatmvisaacqEtl.ascent",fromDate,toDate);
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM VISA ACQUIRER-RECON")) {

			Map<String,Object> result = callWebService("checkATMVISAACQForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("POS NI ACQUIRER-ETL")) {

			Map<String,Object> result = callEtlWebService("checkposniacqetl.ascent",fromDate,toDate);
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("POS NI ACQUIRER-RECON")) {

			Map<String,Object> result = callWebService("checkPOSNIACQUIRERForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("CREDIT CARD STATEMENT-ETL")) {

			Map<String,Object> result = callWebService("checkcreditcardetl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("CREDIT CARD STATEMENT-RECON")) {

			Map<String,Object> result = callWebService("checkCREDITCARDSTATEMENTForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM/POS VISA ISSUER-ETL")) {

			Map<String,Object> result = callWebService("checkatmvisaissEtl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM/POS VISA ISSUER-RECON")) {

			Map<String,Object> result = callWebService("checkAtmVisaIssrecon.ascent");
			dsResponse.setData(result);

		}

		//CENTRAL OPERATION DEPT
		else if (action != null && action.equals("CDM-ETL")) {

			Map<String,Object> result = callWebService("checkcdmEtl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("CDM-RECON")) {

			Map<String,Object> result = callWebService("checkCDMForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM TRANSACTIONS-ETL")) {

			Map<String,Object> result = callWebService("checkatmetl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ATM TRANSACTIONS-RECON")) {

			Map<String,Object> result = callWebService("checkATMForReconRun.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ACH-ETL")) {

			Map<String,Object> result = callWebService("checkachEtl.ascent");
			dsResponse.setData(result);

		}
		else if (action != null && action.equals("ACH-RECON")) {

			Map<String,Object> result = callWebService("checkACHForReconRun.ascent");
			dsResponse.setData(result);

		}

		return dsResponse;
	}


	private Map<String,Object> callWebService(String url) {
		Client restClient = Client.create();
		String URI = getServerURL()+url;
		WebResource webResource = restClient.resource(URI);
		webResource.type(MediaType.APPLICATION_JSON);
		ClientResponse response = webResource.accept("application/json").post(ClientResponse.class);
		Map<String, Object> result = checkResponse(response);
		return result;
	}
	
	private Map<String,Object> callEtlWebService(String url, String fromDate, String toDate) {
		MultivaluedMap<String, String> params = new MultivaluedMapImpl();
		params.add("fromDate", fromDate);
		params.add("toDate", toDate);
		Client restClient = Client.create();
		String URI = getServerURL()+url;
		WebResource webResource = restClient.resource(URI);
		webResource.type(MediaType.APPLICATION_JSON);
		ClientResponse response = webResource.queryParams(params).accept("application/json").post(ClientResponse.class);
		Map<String, Object> result = checkResponse(response);
		return result;
	}

	private Map<String, Object> checkResponse(ClientResponse response) {
		Map<String, Object> result = new HashMap<String, Object>();
		ObjectMapper mapper = new ObjectMapper();
		if(response.getStatus() == 200){
			String output = response.getEntity(String.class);
			output = output.replace("]", "").replace("[", "");
			logger.debug("output : "+output);
			try {
				result = mapper.readValue(output, new TypeReference<Map<String, String>>(){});
			} catch (IOException e) {
				result.put("result", "Not processed successfully");
				e.printStackTrace();
			}
			logger.debug("result : "+result);
		}
		else {
			result.put("result", "Not processed successfully");
		}
		return result;
	}


	public static String getServerURL() {
		ResourceBundle resourceBundle = ResourceBundle.getBundle("appServer", Locale.getDefault());
		String serverURL = resourceBundle.getString("etlServerHost")+ "/";
		return serverURL;
	}


}