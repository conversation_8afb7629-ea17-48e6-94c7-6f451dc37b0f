package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class FinanceMPCLEAR {
	
	private static Logger logger = LogManager.getLogger(FinanceMPCLEAR.class.getName());
	
	private static final String MPCLEAR_INTERNAL_RECONCILED_RECON = "MPCLEAR_INTERNAL_RECONCILED_RECON";
	private static final String MPCLEAR_EXTERNAL_RECONCILED_RECON = "MPCLEAR_EXTERNAL_RECONCILED_RECON";
	private static final String MPCLEAR_INTERNAL_UNRECONCILED_RECON = "MPCLEAR_INTERNAL_UNRECONCILED_RECON";
	private static final String MPCLEAR_EXTERNAL_UNRECONCILED_RECON = "MPCLEAR_EXTERNAL_UNRECONCILED_RECON";
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> MpClearInternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE", rset.getString(1));
				map.put("TRANSACTION AMOUNT", rset.getString(2));
				map.put("TRANSACTION DATE", rset.getString(3));
				map.put("BD STATUS", rset.getString(4));
				map.put("CUSTOMER GSM NUM", rset.getString(5));
				map.put("SENDER BANK", rset.getString(6));
				map.put("DEBIT ACCOUNT NUMBER", rset.getString(7));
				map.put("DEBIT ACCOUNT NAME", rset.getString(8));
				map.put("CREDIT ACCOUTN NUMBER", rset.getString(9));
				map.put("CREDIT ACCOUNT NAME", rset.getString(10));
				map.put("STAFF FLAG", rset.getString(11));
				map.put("PAYMENT TYPE", rset.getString(12));
				map.put("REVERSED FLAG", rset.getString(13));
				map.put("TRANSACTION ID", rset.getString(14));
				map.put("VALUE DATE", rset.getString(15));
				map.put("TRAN POST FLG", rset.getString(16));
				map.put("WALLET TRANSFER WITHIN BD", rset.getString(17));

				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> MpClearExternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE", rset.getString(1));
				map.put("TRANSACTION AMOUNT", rset.getString(2));
				map.put("TRANSACTION DATE", rset.getString(3));
				map.put("SESSION SEQ", rset.getString(4));
				map.put("CURRENCY", rset.getString(5));
				map.put("PARTICIPANT", rset.getString(6));
				map.put("SETTLEMENT RETRY", rset.getString(7));
				map.put("TYPE", rset.getString(8));
				map.put("STATE", rset.getString(9));
				map.put("REASON", rset.getString(10));
				map.put("ADDITIONAL INFO", rset.getString(11));

				list.add(map);
				//logger.debug("MpClearExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> MpClearInternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE", rset.getString(1));
				map.put("TRANSACTION AMOUNT", rset.getString(2));
				map.put("TRANSACTION DATE", rset.getString(3));
				map.put("BD STATUS", rset.getString(4));
				map.put("CUSTOMER GSM NUM", rset.getString(5));
				map.put("SENDER BANK", rset.getString(6));
				map.put("DEBIT ACCOUNT NUMBER", rset.getString(7));
				map.put("DEBIT ACCOUNT NAME", rset.getString(8));
				map.put("CREDIT ACCOUTN NUMBER", rset.getString(9));
				map.put("CREDIT ACCOUNT NAME", rset.getString(10));
				map.put("STAFF FLAG", rset.getString(11));
				map.put("PAYMENT TYPE", rset.getString(12));
				map.put("REVERSED FLAG", rset.getString(13));
				map.put("TRANSACTION ID", rset.getString(14));
				map.put("VALUE DATE", rset.getString(15));
				map.put("TRAN POST FLG", rset.getString(16));
				map.put("WALLET TRANSFER WITHIN BD", rset.getString(17));

				list.add(map);
				//logger.debug("MpClearInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> MpClearExternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching MpClearExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(MPCLEAR_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE", rset.getString(1));
				map.put("TRANSACTION AMOUNT", rset.getString(2));
				map.put("TRANSACTION DATE", rset.getString(3));
				map.put("SESSION SEQ", rset.getString(4));
				map.put("CURRENCY", rset.getString(5));
				map.put("PARTICIPANT", rset.getString(6));
				map.put("SETTLEMENT RETRY", rset.getString(7));
				map.put("TYPE", rset.getString(8));
				map.put("STATE", rset.getString(9));
				map.put("REASON", rset.getString(10));
				map.put("ADDITIONAL INFO", rset.getString(11));

				list.add(map);
				//logger.debug("MpClearExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static void main(String[] args) {
		FinanceMPCLEAR c = new FinanceMPCLEAR();
		c.MpClearInternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.MpClearExternalReconsiledMethod("2018-01-01", "2018-10-01");
	}

}
