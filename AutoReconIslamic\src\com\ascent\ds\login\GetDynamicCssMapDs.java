package com.ascent.ds.login;

import java.util.Map;

import javax.servlet.http.HttpSession;

import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetDynamicCssMapDs extends BasicDataSource {

	/**
	 * Use of this  class is to provide the PRIVILAGES to users based on LOGIN .
	 *  getting the values from session  and passing to frontEND 
	 * 
	 */
	private static final long serialVersionUID = 8094677495308253428L;
	 public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		HttpSession httpSession= request.getHttpServletRequest().getSession();
		
@SuppressWarnings("unchecked")
Map<String,Object> cssMap=(Map<String, Object>) httpSession.getAttribute("cssMap");
	 
		 DSResponse dsResponse= new DSResponse();
		 dsResponse.setData(cssMap);
		 return dsResponse;
		
	}
	
	

}
