package com.ascent.ds.operations;

import java.io.File;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.mailschedular.MailApp;
import com.ascent.util.AscentAutoReconConstants;
import com.sun.mail.util.MailSSLSocketFactory;

public class OperationMail {

	
	String folderPath;
	File folder;
	String onUsemial;
	String issuerEmail;
	String file1;
	String file2;
	Message message;
	String onus;
	String issueracquire;
	String from;
	String userName;
	String password;
	String smptpHost;
	String smptpPort;
	String cronExpression;

	public static final String MAIL_SUBJECT = "Bulk File";

	// Properties properties;
	public OperationMail(String fromMail,String ToMail) throws IOException {
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		System.out.println(etlMetaInstance);
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
		folderPath = (String) appProps
				.get(AscentAutoReconConstants.GENRATE_GL_ENTRY);
		onUsemial = (String) appProps
				.get(AscentAutoReconConstants.OnUsBulkMail);
		issuerEmail = (String) appProps
				.get(AscentAutoReconConstants.Issuer_Acc_BulkMail);
		from = (String) (String) appProps.get(AscentAutoReconConstants.from);
		userName = (String) appProps.get(AscentAutoReconConstants.username);
		password = (String) appProps.get(AscentAutoReconConstants.password);
		smptpHost = (String) appProps.get(AscentAutoReconConstants.SMTPHost);
		smptpPort = (String) appProps.get(AscentAutoReconConstants.SMTPPort);
		cronExpression = (String) appProps
				.get(AscentAutoReconConstants.cronExpression);
		folder = new File(folderPath);

		// here i define file pattern
		issueracquire = "ISS_ACQ_bulk_";
		onus = "ATM_MODULE_bulk_";

		// here i formate the current date into yyyymmdd format
		Date date = new Date();
		String modifiedDate = new SimpleDateFormat("yyyyMMdd").format(date);

		// here i append file pattren with current date and extension of both
		// file

		issueracquire = issueracquire + modifiedDate + ".dat";
		onus = onus + modifiedDate + ".dat";

		// here i set whole path where this file is available
		file1 = folderPath + "\\" + onus;
		file2 = folderPath + "\\" + issueracquire;
		System.out.println(onUsemial + "  " + issuerEmail + "" + file1 + " "
				+ file2);
	}

	// public void sendMail(){
	// sendMail(file1, onUsemial);
	// secondUserMail(file2, issuerEmail);
	// }
	public static void main(String as[]) throws IOException, MessagingException {
		MailApp m = new MailApp();

		m.sendMail();
	}

	public void sendMail( String fromUser,String toUser) throws MessagingException {

		// here i am calling createSession() method to create the session for
		// mail
		System.out.println("sender" + from + "" + userName + " " + password);
		from=fromUser;
		message = this.createSession();
		
		this.onUsemial=toUser;
		// here i am calling sentMail(---) which have logic to send mail
		//this.sentMail(onUsemial, file1, file2);
		//this.sentMail(issuerEmail, file1, file2, message);

	}

	public Message createSession() {

		
		String from = this.from;
		
		final String username = this.userName;
		
		final String password = this.password;// change accordingly
		System.out.println(from + username + password);
		
		Properties props = new Properties();

		props.put("mail.smtp.auth", "true");
		//props.put("mail.smtp.starttls.enable", "true");
		props.put("mail.smtp.socketFactory.class",
				"javax.net.ssl.SSLSocketFactory");
		props.put("mail.smtp.host", smptpHost);
		props.put("mail.smtp.port", smptpPort);
		MailSSLSocketFactory sf = null;
		try {
			sf = new MailSSLSocketFactory();
			sf.setTrustAllHosts(true);
		} catch (GeneralSecurityException e1) {

			e1.printStackTrace();
		}
		props.put("mail.smtp.ssl.socketFactory", sf);

		// Get the Session object.
		Session session = Session.getInstance(props,
				new javax.mail.Authenticator() {
					protected PasswordAuthentication getPasswordAuthentication() {
						return new PasswordAuthentication(username, password);
					}
				});

		try {
			// Create a default MimeMessage object.
			message = new MimeMessage(session);
			message.setFrom(new InternetAddress(from));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return message;
	}

	public void sentMail(String toUser, String reconName, List<Map<String,Object>> recordList,
			String message) throws MessagingException {
		this.message.setFrom(new InternetAddress(from));
		this.message.setRecipients(Message.RecipientType.TO,
				InternetAddress.parse(toUser));
		//String file_one_name = chnageFileName(fileName);
		//String file_two_name = chnageFileName(fileName2);

		String content_of_mail = "</br><h5> 1. " + reconName+ "</h5></br><br/> "
				+ "<body> TRXN WHICH THE USER PERFORM OPERATION <BR/><BR/>"+recordList+"</body>";
				
		

		this.message.setSubject(message);
		//Multipart multipart = new MimeMultipart();
		//BodyPart messageBodyPart1 = new MimeBodyPart();

		this.message.setContent(content_of_mail, "text/html");
		//multipart.addBodyPart(messageBodyPart1);

	//	addAttachment(multipart, fileName);
		//addAttachment(multipart, fileName2);

		//this.message.setText(content_of_mail);

		// Send message
		try{
			System.out.println("mail started!!!");
		Transport.send(this.message);
		System.out.println("mail sent!!!");
		}catch(Exception e){
			System.err.println(e+" mail not sent!!!");
			e.printStackTrace();
		}
	}

	/*private static void addAttachment(Multipart multipart, String filename)
			throws MessagingException {

		DataSource source = new FileDataSource(filename);

		BodyPart messageBodyPart = new MimeBodyPart();
		messageBodyPart.setDataHandler(new DataHandler(source));

		filename = chnageFileName(filename);

		messageBodyPart.setFileName(filename);

		multipart.addBodyPart(messageBodyPart);
	}

	private static String chnageFileName(String fileName) {
		int index = fileName.lastIndexOf("BULK_FILES");
		// System.out.println(index+10);
		fileName = fileName.substring(index + 11);
		return fileName;
	}*/
}
