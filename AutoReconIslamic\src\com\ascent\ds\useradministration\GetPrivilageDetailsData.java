package com.ascent.ds.useradministration;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.administration.AuthorizationBean;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class GetPrivilageDetailsData extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = 8956711001550516188L;
	public static final String SELECTED_ROLE = "selectedRole";
	public static final String SUBMITTED_FOR_APPROVAL_SUCESSFULLY = "submitted for approval sucessfully";
	public static final String DATA_SUBMITTED_SUCCESSFULLY = "Privilages Data submitted successfully..!!";
	private static final String FETCHING_ROLEWISE_PRIVILLAGE_DETAILS = "FETCHING_ROLEWISE_PRIVILLAGE_DETAILS";
	private static final String ADDING_NEW_PRIVILLAGE = "ADDING_NEW_PRIVILLAGE";
	private static final String PERSIST_PRIVILAGE_DETAILS = "PERSIST_PRIVILAGE_DETAILS";
	public static final String GET_MAX_PRIVILAGE_ID = "GET_MAX_PRIVILAGE_ID";
	public static final String DELETE_ACTION = "DELETE";
	public static final String originalRecord = "originalRecord";

	List<Map<String, Object>> loadPrivilageDetailsRoleWise = null;

	public DSResponse executeFetch(DSRequest dsRequest) {

		DSResponse dsResponse = new DSResponse();

		Map<String, Object> criteriaMap = dsRequest.getValues();
		HttpSession httpSession = dsRequest.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		String userSelectedBusinessArea = (String) httpSession.getAttribute("user_selected_business_area");
		String userSelectedRecon = (String) httpSession.getAttribute("user_selected_recon");
		String action = (String) criteriaMap.get("action");
		if (action.equalsIgnoreCase(FETCHING_ROLEWISE_PRIVILLAGE_DETAILS)) {

			loadPrivilageDetailsRoleWise = getPrivilageDetail(criteriaMap);
			dsResponse.setData(loadPrivilageDetailsRoleWise);

		} else if (action.equalsIgnoreCase(ADDING_NEW_PRIVILLAGE)) {
			// loadPrivilageDetailsRoleWise = getPrivilageDetail(criteriaMap);

			loadPrivilageDetailsRoleWise = showPrivilageDetailsToGrid(criteriaMap);
			System.out.println("loadPrivilageDetailsRoleWise-->> " + loadPrivilageDetailsRoleWise);
			dsResponse.setData(loadPrivilageDetailsRoleWise);

		} else if (action.equalsIgnoreCase(PERSIST_PRIVILAGE_DETAILS)) {

			Map<String, Object> processPrivilageData = processPrivilageData(criteriaMap, user, userSelectedBusinessArea,
					userSelectedRecon);
			System.out.println(processPrivilageData);
			String selectedRole = (String) criteriaMap.get(SELECTED_ROLE);
			String currentUsersRole = user.getSystemRole();

			if (currentUsersRole.contains("Admin") || currentUsersRole.contains("Super Admin")
					|| currentUsersRole.contains("Approver1") || currentUsersRole.contains("Approver2")
					|| currentUsersRole.contains("Approver3")) {
				processPrivilageData.put("STATUS", DATA_SUBMITTED_SUCCESSFULLY);
				dsResponse.setData(processPrivilageData);
			} else {

				processPrivilageData.put("STATUS", SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				dsResponse.setData(processPrivilageData);
			}

			return dsResponse;

		} else if (action.equalsIgnoreCase("DELETE")) {

			loadPrivilageDetailsRoleWise = deletePrivilageDetails(criteriaMap);
			dsResponse.setData(loadPrivilageDetailsRoleWise);

		} else {

			loadPrivilageDetailsRoleWise = new ArrayList<Map<String, Object>>();
			dsResponse.setData(loadPrivilageDetailsRoleWise);
			return dsResponse;

		}
		
		return dsResponse;
	}

	public List<Map<String, Object>> getPrivilageDetail(Map<String, Object> critriaMap) {

		String selectedRole = (String) critriaMap.get(SELECTED_ROLE);
		List<Map<String, Object>> loadPrivilageDetailsRoleWise = null;
		CustomerDao customerDao = new CustomerDao();
		try {

			loadPrivilageDetailsRoleWise = customerDao.loadPrivilageDetailsRoleWise(selectedRole);

		} catch (Exception e) {

			e.printStackTrace();
		}

		if (loadPrivilageDetailsRoleWise == null) {
			loadPrivilageDetailsRoleWise = new ArrayList<Map<String, Object>>();
		}
		return loadPrivilageDetailsRoleWise;
	}

	List<Map<String, Object>> returnList = new ArrayList<Map<String, Object>>();

	
	public List<Map<String, Object>> showPrivilageDetailsToGrid(Map<String, Object> criteriaMap) {
		loadPrivilageDetailsRoleWise.clear();
		returnList.clear();
		String selectedRole = (String) criteriaMap.get(SELECTED_ROLE);

		List<Map<String, Object>> listObj = new ArrayList<Map<String, Object>>();

		Map<String, Object> maptemp = new HashMap<String, Object>();
		Map<String, Object> recordMap = (Map<String, Object>) criteriaMap.get("commonData");
		System.out.println("record Map:----> " + recordMap);
		returnList = getPrivilageDetail(criteriaMap);
		System.out.println("OLD DATA____"+returnList);
		
		Map map=null;
		List<Map> objectList =new ArrayList<Map>();
		if(!returnList.isEmpty()){
		        map=returnList.get(0);
		        objectList =(List<Map>) map.get(selectedRole);
		        objectList.add(recordMap);
		}
		
		if(returnList.isEmpty()){
			objectList.add(recordMap);
			maptemp.put(selectedRole, objectList);
			returnList.add(maptemp);
			
			System.out.println("RETURN LIST IS EMPTY ---> ADDED NEW DATA ");
		}
	
		
		/*listObj.add(recordMap);
		System.out.println("listObj" + listObj);
		maptemp.put(selectedRole, listObj);*/
		//returnList.add(maptemp);
		/**/
		
		
		return returnList;
	}

	/**
	 * PERSIST_PRIVILAGE_DETAILS
	 */

	public Map<String, Object> processPrivilageData(Map<String, Object> criteriaMap, User user,
			String userSelectedBusinessArea, String userSelectedRecon) {
		Map<String, Object> responseMap = new HashMap<String, Object>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		AuthorizationBean authorizationBean = new AuthorizationBean();
		// selectedRole
		String selectedRole = (String) criteriaMap.get(SELECTED_ROLE);
		String dsName = (String) criteriaMap.get("dsName");
		List<Map<String, Object>> record = (List<Map<String, Object>>) criteriaMap.get("selectedRecords");
		resultMap.put(SELECTED_RECORDS, record);
		resultMap.put("selectedRole", selectedRole);
		resultMap.put(DS_NAME, dsName);
		Integer pID = getMaxPrivilageId();
		String PID = String.valueOf(pID);
		resultMap.put("privilegeId", PID);

		authorizationBean.savePrevilegeDetails(resultMap, user, userSelectedBusinessArea, userSelectedRecon);

		return responseMap;
	}

	public Integer getMaxPrivilageId() {
		Integer newPrivId = 0;
		Map<String, Object> paramValues = new HashMap<String, Object>();
		CustomerDao cDao = new CustomerDao();
		Map<String, Object> IdMap = null;
		try {
			IdMap = cDao.getDataBaseMap(GET_MAX_PRIVILAGE_ID, paramValues);
		} catch (SQLException e) {
			e.printStackTrace();
		}

		Integer integer = new Integer(0);
		if (IdMap.get("maxId") != null) {
			integer = Integer.parseInt((String) IdMap.get("maxId"));
		}
		int existingId = integer;
		newPrivId = existingId + 1;
		return newPrivId;
	}

	public List<Map<String, Object>> deletePrivilageDetails(Map<String, Object> criteriaMap) {
		Map<String, Object> recordList = (Map<String, Object>) criteriaMap.get(SELECTED_RECORDS);
		List<Map<String, Object>> originalRecords = (List<Map<String, Object>>) criteriaMap.get(originalRecord);

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		list.add(recordList);

		List<Map<String, Object>> originalRecordsTemp = new ArrayList<Map<String, Object>>(originalRecords);
		for (int i = 0; i < originalRecords.size(); i++) {
			for (Map<String, Object> map : list) {

				if ((originalRecords.contains(map))) {

					originalRecordsTemp.remove(map);
				}
			}

		}

		return originalRecordsTemp;
	}

}
