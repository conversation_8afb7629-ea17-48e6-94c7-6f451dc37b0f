<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
	
	<query id="17">
        <name>ATM_MASTERCARD_ACQ_INSERT_QRY</name>
		<targetTables>CARDS_ATM_MASTERCARD_ACQ_RECON</targetTables>
        <queryString>
				
		INSERT INTO CARDS_ATM_MASTERCARD_ACQ_RECON
           (SID,RECON_SIDE,TRA_AMT,TRA_DATE,REF_NUM,WORKFLOW_STATUS,DEB_CRE_IND,CARD_NUM,SOURCE_TARGET
           ,MAIN_REV_IND,RECON_ID,VERSION,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON
           ,CREATED_ON,COMMENTS,RULE_NAME,ACTIVITY_STATUS,OPERATION,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS
           ,ID)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		   ?,?,?,?,?,?,?,?,?,?,
		   ?,?,?,?,?)
  
	</queryString>
		<queryParam>
SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,REF_NUM@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		   DEB_CRE_IND@VARCHAR,CARD_NUM@VARCHAR,SOURCE_TARGET@VARCHAR
           ,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@INTEGER,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,
		   UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,
		   OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,ID@BIGINT
		</queryParam>
    </query>
     	
	<query id="18">
        <name>ATM_MASTERCARD_ACQ_RECON_UPSTREAM_QRY</name>
		<targetTables>CARD_ATM_MC_CBS_STG,CARD_ATM_MC_ACQ_STG</targetTables>
        <queryString>
				
			SELECT * FROM(
					select 'CBS' AS RECON_SIDE,SID, TRAN_DATE AS TRA_DATE,TRAN_AMOUNT AS TRA_AMT ,ACTUAL_REF_NUM as REF_NUM 
					,DRCR_IND AS DEB_CRE_IND,'CARD_ATM_MC_CBS_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,CARD_NUM as CARD_NUM FROM CARD_ATM_MC_CBS_STG WHERE (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
						UNION ALL
					SELECT 'ACQ' AS RECON_SIDE,SID, TRN_DATE AS  TRA_DATE,REQ_AMT_TRN_LOCAL AS TRA_AMT ,REF_NUMBER AS REF_NUM,
					COMPTD_AMT_TRN_LOCAL_DR_CR_IND AS DEB_CRE_IND,'CARD_ATM_MC_ACQ_STG' AS SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION,WORKFLOW_STATUS,
					BUSINESS_AREA,PRIMARY_ACC_NUM as CARD_NUM FROM CARD_ATM_MC_ACQ_STG WHERE  (RECON_ID IS NULL OR RECON_STATUS='AU') and ACTIVE_INDEX = 'Y' and WORKFLOW_STATUS='N'
              ) AS A 
 				ORDER BY CARD_NUM,TRA_AMT,TRA_DATE,REF_NUM 
		</queryString>
		<queryParam>
				
		</queryParam>
    </query>
    	


<query id="2">
        <name>CARD_ATM_MC_CBS_STG_AUDIT_INSERT_QRY</name>
		<targetTables>CARD_ATM_MC_CBS_STG_AUDIT</targetTables>
        <queryString>
		
INSERT INTO CARD_ATM_MC_CBS_STG_AUDIT
           (SID,CARD_NUM,ACTUAL_REF_NUM,TRAN_DATE,VALUE_DATE,TXN_REF_NUM,TRAN_PARTICULAR,TRAN_AMOUNT,DRCR_IND,TRAN_RMKS
           ,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS
           ,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA)
     VALUES
           (?,?,?,?,?,?,?,?,?,?,
		    ?,?,?,?,?,?,?,?,?,?,
			?,?,?)	
			 </queryString>
		<queryParam>
			
			SID@BIGINT,CARD_NUM@VARCHAR,ACTUAL_REF_NUM@VARCHAR,TRAN_DATE@DATE,VALUE_DATE@DATE,TXN_REF_NUM@VARCHAR,
		   TRAN_PARTICULAR@VARCHAR,TRAN_AMOUNT@DECIMAL,DRCR_IND@VARCHAR,TRAN_RMKS@VARCHAR
           ,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		   UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR
           ,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR
		</queryParam>
    </query>
    
    
   <query id="2">
        <name>CARD_ATM_MC_ACQ_STG_AUDIT_INSERT_QRY</name>
		<targetTables>CARD_ATM_MC_ACQ_STG_AUDIT</targetTables>
        <queryString>
						INSERT INTO CARD_ATM_MC_ACQ_STG_AUDIT
				           (SID,MSG_TYPE_IND,SWITCH_SER_NUM,PROCESSOR_ACQ,PROCESSOR_ID,TRN_DATE,TRN_TIME,PAN_LENGTH,PRIMARY_ACC_NUM,PROCESSING_CODE
				           ,TRACE_NUM1,MERCHANT_TYPE,POS_ENTRY,REF_NUMBER,ACQ_INST_ID,TERMINAL_ID,BRAND,ADVICE_REASON_CODE,INTRACUR_AGRMNT_CODE
				           ,AUTHORIZATION_ID,CUR_CODE_TRN,IMP_DEC_TRN,COMPTD_AMT_TRN_LOCAL,COMPTD_AMT_TRN_LOCAL_DR_CR_IND,CASH_BACK_AMT_LOCAL
				           ,CASH_BACK_AMT_LOCAL_DR_CR_IND,ACCESS_FEE_LOCAL,ACCESS_FEE_LOCAL_DR_CR_IND,CUR_CODE_STMNT,IMPLIED_DEC_STMNT
				           ,CONVERSION_RATE_STMNT,COMPTD_AMT_STMNT,COMPTD_AMOUNT_STMNT_DR_CR_IND,INTRCHNG_FEE
				           ,INTRCHNG_FEE_DR_CR_IND,SER_LEVEL_IND,RESP_CODE2,FILLER1,POSITIVE_ID_IND,ATM_SURCHRGE_FREE_PRGM_ID
				           ,CROSS_BORDER_IND,CROSS_BORDER_CUR_IND,VISA_INTR_SER_ASS_IND,REQ_AMT_TRN_LOCAL,FILLER2,TRACE_NUM_ADJ_TRNS
				           ,FILLER3,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS
				           ,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,BUSINESS_AREA,RESP_CODE1)
				     VALUES
				           (
						   ?,?,?,?,?,?,?,?,?,?,
						   ?,?,?,?,?,?,?,?,?,?,
						   ?,?,?,?,?,?,?,?,?,?,
						   ?,?,?,?,?,?,?,?,?,?,
						   ?,?,?,?,?,?,?,?,?,?,
						   ?,?,?,?,?,?,?,?,?,?,
						   ?)
			 </queryString>
		<queryParam>
					SID@BIGINT,MSG_TYPE_IND@VARCHAR,SWITCH_SER_NUM@VARCHAR,PROCESSOR_ACQ@VARCHAR,PROCESSOR_ID@VARCHAR,TRN_DATE@DATE,
				   TRN_TIME@VARCHAR,PAN_LENGTH@INTEGER,PRIMARY_ACC_NUM@VARCHAR,PROCESSING_CODE@VARCHAR
		           ,TRACE_NUM1@VARCHAR,MERCHANT_TYPE@VARCHAR,POS_ENTRY@VARCHAR,REF_NUMBER@VARCHAR,ACQ_INST_ID@VARCHAR,TERMINAL_ID@VARCHAR,
				   BRAND@VARCHAR,ADVICE_REASON_CODE@VARCHAR,INTRACUR_AGRMNT_CODE@VARCHAR
		           ,AUTHORIZATION_ID@VARCHAR,CUR_CODE_TRN@VARCHAR,IMP_DEC_TRN@VARCHAR,COMPTD_AMT_TRN_LOCAL@DECIMAL,COMPTD_AMT_TRN_LOCAL_DR_CR_IND@VARCHAR,
				   CASH_BACK_AMT_LOCAL@DECIMAL
		           ,CASH_BACK_AMT_LOCAL_DR_CR_IND@VARCHAR,ACCESS_FEE_LOCAL@VARCHAR,ACCESS_FEE_LOCAL_DR_CR_IND@VARCHAR,
				   CUR_CODE_STMNT@VARCHAR,IMPLIED_DEC_STMNT@VARCHAR
		           ,CONVERSION_RATE_STMNT@VARCHAR,COMPTD_AMT_STMNT@VARCHAR,COMPTD_AMOUNT_STMNT_DR_CR_IND@VARCHAR,INTRCHNG_FEE@VARCHAR
		           ,INTRCHNG_FEE_DR_CR_IND@VARCHAR,SER_LEVEL_IND@VARCHAR,RESP_CODE2@VARCHAR,FILLER1@VARCHAR,POSITIVE_ID_IND@VARCHAR,
				   ATM_SURCHRGE_FREE_PRGM_ID@VARCHAR
		           ,CROSS_BORDER_IND@VARCHAR,CROSS_BORDER_CUR_IND@VARCHAR,VISA_INTR_SER_ASS_IND@VARCHAR,REQ_AMT_TRN_LOCAL@DECIMAL,FILLER2@VARCHAR,
				   TRACE_NUM_ADJ_TRNS@VARCHAR
		           ,FILLER3@VARCHAR,COMMENTS@VARCHAR,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,
				   CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR
		           ,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,BUSINESS_AREA@VARCHAR,RESP_CODE1@VARCHAR
		</queryParam>
    </query>
		
    

	<query id="17">
        <name>PAYMENT_ORDER_RECON_UPDATE_QRY</name>
		<targetTables>PAYMENT_ORDER_RECON</targetTables>
        <queryString>
				UPDATE ONUS_ATM_DEBIT_RECON SET
					ID=?,TRA_AMT=?,TRA_DATE=?,DEB_CRE_IND=?,TRA_CUR=?,CHECK_NO=?,WORKFLOW_STATUS=?,SOURCE_TARGET=?,
					MAIN_REV_IND=?,RECON_ID=?,VERSION=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,CREATED_ON=?,COMMENTS=?,
					SUPPORTING_DOC_ID=?,RULE_NAME=?,ACTIVITY_STATUS=?,OPERATION=?,STATUS=?,BUSINESS_AREA=?,ACTIVITY_COMMENTS=?
				WHERE SID=? AND RECON_SIDE=?
		
  
	</queryString>
		<queryParam>
				ID@BIGINT,SID@BIGINT,RECON_SIDE@VARCHAR,TRA_AMT@DECIMAL,TRA_DATE@DATE,DEB_CRE_IND@VARCHAR,TRA_CUR@VARCHAR,
		CHECK_NO@VARCHAR,WORKFLOW_STATUS@VARCHAR,SOURCE_TARGET@VARCHAR,MAIN_REV_IND@VARCHAR,RECON_ID@BIGINT,VERSION@VARCHAR,
		MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,COMMENTS@VARCHAR,
		SUPPORTING_DOC_ID@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,OPERATION@VARCHAR,STATUS@VARCHAR,BUSINESS_AREA@VARCHAR,
		ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
    </query>

</queries>