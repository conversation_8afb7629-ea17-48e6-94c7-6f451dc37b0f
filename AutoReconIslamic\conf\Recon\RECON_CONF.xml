<Recons>

<!-- ISLAMIC OR CONVENTIONAL  START-->

	<Recon id="102" enable="true">
		<Name>FINANCE_ONS_RECON</Name>
		<QueryConName>ONS</QueryConName>
		<SequenceName>ONS</SequenceName>
		<ColumnNamesString>
			ID,SID,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,TRA_CUR,REF_NUM,WORKFLOW_STATUS,SOURCE_TARGET,
					MAIN_REV_IND,RECON_ID,VERSION,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON,CREATED_ON,COMMENTS,
					SUPPORTING_DOC_ID,RULE_NAME,ACTIVITY_STATUS,OPERATION,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS,
					PAN_CARD,ACCOUNT_NUMBER
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ONS_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>FINANCE_ONS_RECON_UPSTREAM_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>FINANCE_ONS_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.FinanceONSRecon</Plugin>		
	 </Recon>


<Recon id="102" enable="true">
		<Name>FINANCE_MP_CLEAR_RECON</Name>
		<QueryConName>MP_CLEAR</QueryConName>
		<SequenceName>MP_CLEAR</SequenceName>
		<ColumnNamesString>
			SID,RECON_SIDE,TRA_AMT,TRA_DATE,DEB_CRE_IND,REFERENCE,WORKFLOW_STATUS,SOURCE_TARGET,MAIN_REV_IND,RECON_ID,VERSION
		           ,MATCH_TYPE,ACTIVE_INDEX,USER_ID,UPDATED_ON,CREATED_ON,COMMENTS,RULE_NAME,ACTIVITY_STATUS,OPERATION
		           ,STATUS,BUSINESS_AREA,ACTIVITY_COMMENTS,ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>MP_CLEAR_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>FINANCE_MP_CLEAR_RECON_UPSTREAM_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>FINANCE_MP_CLEAR_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.FinanceMpClear</Plugin>		
	 </Recon>
	 
	 
<Recon id="102" enable="true">
		<Name>CDM_RECON</Name>
		<QueryConName>CDM</QueryConName>
		<SequenceName>CDM</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>CDM_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>CDM_RECON_UPSTREAM_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>CDM_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.CentralOpeCDM</Plugin>
		
	 </Recon>



<Recon id="102" enable="true">
		<Name>ATM_MASTERCARD_ACQ_RECON</Name>
		<QueryConName>ATM_MASTERCARD_ACQ</QueryConName>
		<SequenceName>ATM_MASTERCARD_ACQ</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ATM_MASTERCARD_ACQ_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ATM_MASTERCARD_ACQ_RECON_UPSTREAM_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ATM_MASTERCARD_ACQ_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.AtmMasterCardAcqRecon</Plugin>
		
	 </Recon>


<!-- ISLAMIC OR CONVENTIONAL END -->


<Recon id="102" enable="true">
		<Name>TRADE_DEAL_GL</Name>
		<QueryConName>TFA_RECON</QueryConName>
		<SequenceName>TFA_RECON</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>TRADE_DEAL_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>TRADE_DEAL_GL_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>TRADE_DEAL_GL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.TradeDealGlRecon</Plugin>
		
	 </Recon>
<Recon id="102" enable="true">
		<Name>MASTER_CARD_RECON</Name>
		<QueryConName>MASTER_CARD_RECON</QueryConName>
		<SequenceName>MASTER_CARD_RECON</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>MASTER_CARD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>MASTER_CARD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.MasterCardReconPlugin</Plugin>
		
</Recon>
<Recon id="123" enable="true">
		<Name>ECC</Name>
		<QueryConName>ecc</QueryConName>
		<SequenceName>ecc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ECC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ECC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.EccRecon</Plugin>
		
	 </Recon> 


   <Recon id="1" enable="true">
		<Name>CC</Name>
		<QueryConName>cc</QueryConName>
		<SequenceName>cc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>CC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>CC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.CcAtmRecon</Plugin>
		
	 </Recon> 


//TRADE MARGIN LC
 <Recon id="1" enable="true">
		<Name>TRADE_MARGIN_LC</Name>
		<QueryConName>trade_margin</QueryConName>
		<SequenceName>trade_margin</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>TRADE_MARGIN_LC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>TRADE_MARGIN_LC_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>TRADE_MARGIN_LC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.TradeMarginLcRecon</Plugin>
		
	 </Recon> 
//TRADE SUSPENS RECON 
<Recon id="1" enable="true">
		<Name>TRADE_SUSPENS_GL</Name>
		<QueryConName>suspens_margin</QueryConName>
		<SequenceName>suspens_margin</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>TRADE_SUSPENS_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>TRADE_SUSPENS_GL_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>TRADE_SUSPENS_GL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.TradeSuspensGlRecon</Plugin>
		
	 </Recon>
 
//TRADE MARGIN RECON 
<Recon id="1" enable="true">
		<Name>TRADE_MARGIN_LG</Name>
		<QueryConName>trade_margin</QueryConName>
		<SequenceName>trade_margin</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>TRADE_MARGIN_LG_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>TRADE_MARGIN_LG_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>TRADE_MARGIN_LG_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.TradeMarginLgRecon</Plugin>
		
	 </Recon> 
	//CBO_FT_QRY_CONF
	  <Recon id="1" enable="true">
		<Name>CBO_FT</Name>
		<QueryConName>cbo_ft</QueryConName>
		<SequenceName>cbo_ft</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>CBO_FT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>CBO_FT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.CboFtRtgsRecon</Plugin>
		
	 </Recon>   
	 
	 
	 
	 <Recon id="1" enable="true">
		<Name>CBO_FT_ACH</Name>
		<QueryConName>cbo_ft</QueryConName>
		<SequenceName>cbo_ft</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>CBO_FT_ACH_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>CBO_FT_ACH_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>CBO_FT_ACH_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.CboFtAchRecon</Plugin>
		
	 </Recon>  
	 
	
      <Recon id="1" enable="true">
		<Name>ONUS_ATM_DC</Name>
		<QueryConName>onus_atm_dc</QueryConName>
		<SequenceName>onus_atm_dc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ONUS_ATM_DEBIT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ONUS_ATM_DEBIT_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ONUS_ATM_DEBIT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.OnusAtmDebitRecon</Plugin>
		
	 </Recon>   
	 
	 
	 
	 
	     <Recon id="1" enable="true">
		<Name>PAYMENT_ORDER</Name>
		<QueryConName>payment_order</QueryConName>
		<SequenceName>payment_order</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>PAYMENT_ORDER_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>PAYMENT_ORDER_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>PAYMENT_ORDER_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.PaymentOrderRecon</Plugin>
		
	 </Recon>  
	   <Recon id="1" enable="true">
		<Name>ISSUER_IMAL</Name>
		<QueryConName>issuer_imal</QueryConName>
		<SequenceName>issuer_imal</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ISSUER_ONS_IMAL_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ISS_IMAL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.IssuerOnsImalRecon</Plugin>
		
	 </Recon> 
	  
	 
	   <!--   <Recon id="1" enable="true">
		<Name>PAYMENT_ORDER</Name>
		<QueryConName>payment_order</QueryConName>
		<SequenceName>payment_order</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>PAYMENT_ORDER_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>PAYMENT_ORDER_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>PAYMENT_ORDER_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.PaymentOrderRecon</Plugin>
		
	 </Recon>  --> 
	 
	 
	 
	 
	    <Recon id="1" enable="true">
		<Name>RETAIL_ASSET</Name>
		<QueryConName>retail_asset</QueryConName>
		<SequenceName>retail_asset</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>RETAIL_ASSET_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>Dummy</ReconUpdateQueryName>
		<ReconUpstreamQueryName>RETAIL_ASSET_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.RetailAssetsRecon</Plugin>
		
	 </Recon> 
	 
	 
	 <Recon id="1" enable="true">
		<Name>UTILITY_PAYMENTS</Name>
		<QueryConName>utility_payments</QueryConName>
		<SequenceName>utility_payments</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>UTILITY_PAYMENTS_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>Dummy</ReconUpdateQueryName>
		<ReconUpstreamQueryName>UTILITY_PAYMENTS_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.UtilityPaymentsRecon</Plugin>
		
	 </Recon>  
	 
	 
	 <!-- SUSP GL -->
	 	  
	 	 
	     <Recon id="1" enable="true">
		<Name>SUSP_GL</Name>
		<QueryConName>payment_order</QueryConName>
		<SequenceName>payment_order</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>SUSP_GL_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>PAYMENT_ORDER_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>SUSP_GL_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.SuspGlRecon</Plugin>
		
	 </Recon>  
	 
	 
	  <Recon id="1" enable="true">
		<Name>TRADE_MARGIN_GL</Name>
		<QueryConName>trade_margin</QueryConName>
		<SequenceName>trade_margin</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>TRADE_MARGIN_GL_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>Dummy</ReconUpdateQueryName>
		<ReconUpstreamQueryName>TRADE_MARGIN_GL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.MarginGLRecon</Plugin>
		</Recon>
		
		 <Recon id="1" enable="true">
		<Name>OFF_BS</Name>
		<QueryConName>trade_margin</QueryConName>
		<SequenceName>trade_margin</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
	<ReconInsertQueryName>OFF_BS_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>Dummy</ReconUpdateQueryName>
		<ReconUpstreamQueryName>OFF_BS_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.banknizwa.recon.plugin.OffBSRecon</Plugin>
		</Recon>
	 
	 
	   <!-- <Recon id="1" enable="true">
		<Name>HRDF_LEAVY</Name>
		<QueryConName>hrdf_leavy</QueryConName>
		<SequenceName>hrdf_leavy</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>LEAVY_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>LEAVY_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.LeavyRecon</Plugin>
	 </Recon>   
	 
    <Recon id="2" enable="true">
		<Name>HRDF_LEAVY1</Name>
		<QueryConName>hrdf_leavy1</QueryConName>
		<SequenceName>hrdf_leavy1</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>LEAVY_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>LEAVY_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.LeavyRecon1</Plugin>
	 </Recon>  
	 
	    <Recon id="3" enable="true">
		<Name>HRDF_LEAVY2</Name>
		<QueryConName>hrdf_leavy2</QueryConName>
		<SequenceName>hrdf_leavy2</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>LEAVY_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>LEAVY_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.LeavyRecon2</Plugin>
	 </Recon>   
	  -->
	 
	<!--  <Recon id="1" enable="true">
		<Name>ONUS_ATM_DEPOSIT</Name>
		<QueryConName>onus_atm_deposit</QueryConName>
		<SequenceName>onus_atm_deposit</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ONUS_ATM_DEPOSIT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ONUS_ATM_DEPOSIT_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ONUS_ATM_DEPOSIT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.OnusAtmDepositRecon</Plugin>
		
	</Recon>   
	
 	
<Recon id="1" enable="true">
		<Name>ONUS_ATM_CC</Name>
		<QueryConName>onus_atm_cc</QueryConName>
		<SequenceName>onus_atm_cc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ONUS_ATM_CREDIT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ONUS_ATM_CREDIT_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ONUS_ATM_CREDIT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.OnusAtmCreditRecon</Plugin>
		
	</Recon>


	<Recon id="1" enable="true">
		<Name>ONUS_ATM_PC</Name>
		<QueryConName>onus_atm_pc</QueryConName>
		<SequenceName>onus_atm_pc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ONUS_ATM_PAYROL_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ONUS_ATM_PAYROL_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ONUS_ATM_PAYROL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.OnusAtmPayrolRecon</Plugin>
		
	</Recon>  
	  
	 
	========================================================================
		ISSUER RECONS
	========================================================================
		 
	
	 <Recon id="1" enable="true">
		<Name>ISSUER_VATM_DC</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>iss_atm_dc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ISSUER_VATM_DC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ISSUER_VATM_DC_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ISSUER_VATM_DC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.IssuerVisaAtmDebitRecon</Plugin>
		
	</Recon>  
 
 
	  <Recon id="1" enable="true">
		<Name>ISSUER_NATM_DC</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>naps_atm_seq</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ISS_NATM_DEBIT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ISS_NATM_DEBIT_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ISS_NATM_DEBIT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.IssuerNapsAtmDebitRecon</Plugin>
		
	</Recon>  


<Recon id="1" enable="true">
		<Name>ISSUER_NPOS_DC</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>naps_pos_seq</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ISSUER_NPOS_DC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ISSUER_NPOS_DC_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ISSUER_NPOS_DC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.IssuerNapsPosDebitRecon</Plugin>
		
	</Recon>  
	
	
	
<Recon id="1" enable="true">
		<Name>ISSUER_VPOS_DC</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>iss_vpos_dc</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ISSUER_VPOS_DC_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ISSUER_VPOS_DC_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ISSUER_VPOS_DC_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.IssuerVisaPosDebitRecon</Plugin>
		
	</Recon>

	=======================================================================
		ACQUIRER RECONS
	=======================================================================
  
	
	
	 
	<Recon id="1" enable="true">
		<Name>ACQUIRER_VATM_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>acq_vatm_crds</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_VATM_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_VATM_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_VATM_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqAtmVisaCardRecon</Plugin>
		
	</Recon>  
	
	<Recon id="1" enable="true">
		<Name>ACQUIRER_NATM_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		
		<SequenceName>acq_natm_crds</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_NATM_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_NATM_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_NATM_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqAtmNapsCardRecon</Plugin>
		
	</Recon> 
		
<Recon id="1" enable="true">
		<Name>ACQUIRER_NAPS2_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		
		<SequenceName>acq_naps2_crd</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_NAPS2_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_NAPS2_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_NAPS2_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqAtmNaps2CardRecon</Plugin>
		
	</Recon>  

	
	<Recon id="1" enable="true">
		<Name>ACQUIRER_MATM_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>acq_mtam_crds</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_MATM_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_MATM_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_MATM_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqAtmMasterCardRecon</Plugin>
		
	</Recon>    
		   
	 
	<Recon id="1" enable="true">
		<Name>ACQUIRER_UATM_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>acq_uatm_crds</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_UATM_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_UATM_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_UATM_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqAtmUnionPayCardRecon</Plugin>
		
	</Recon>  
	 
	
	<Recon id="1" enable="true">
		<Name>ACQUIRER_UPOS_CRD</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>acq_upos_crd</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>ACQUIRER_UPOS_CRD_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>ACQUIRER_UPOS_CRD_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>ACQUIRER_UPOS_CRD_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.AcqPosUnionPayCardRecon</Plugin> 
		
	</Recon>    
	
 
	<Recon id="1" enable="true">
		<Name>PAYROLL</Name>
		<QueryConName>aaaaa</QueryConName>
		<SequenceName>switch_iris_seq</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>PAYROL_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>PAYROLL_RECON_UPDATE_QRY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>PAYROLL_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>com.ascent.recon.plugin.PayrolRecon</Plugin>
	</Recon>

-->


  <!-- <Recon id="1" enable="true">
		<Name>HRDF_GRANT_RULE1A</Name>
		<QueryConName>grant_leavy</QueryConName>
		<SequenceName>grant_leavy</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>GRANT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>GRANT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.GrantRecon1A</Plugin>
	 </Recon> -->
	 
	 <!--
	 <Recon id="2" enable="true">
		<Name>HRDF_GRANT1B</Name>
		<QueryConName>grant_leavy</QueryConName>
		<SequenceName>grant_leavy</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>GRANT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>GRANT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.GrantRecon1B</Plugin>
	 </Recon>
	 
	 	 <Recon id="3" enable="true">
		<Name>HRDF_GRANT1C</Name>
		<QueryConName>grant_leavy</QueryConName>
		<SequenceName>grant_leavy</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>GRANT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>GRANT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.GrantRecon1C</Plugin>
	 </Recon>
	 <Recon id="4" enable="true">
		<Name>HRDF_GRANT2</Name>
		<QueryConName>grant_leavy</QueryConName>
		<SequenceName>grant_leavy</SequenceName>
		<ColumnNamesString>
			PAN,PROC_CODE,AMT_TRAN,AMT_SETT,AMT_C_HLDR_BILL,SYS_TRACE_AUDIT_NO,DATE_LOC_TRAN,TIME_LOC_TRAN,ACQ_INST_CTRY_CODE,
			CURR_CODE_TRAN,CURR_CODE_SETT,ACQ_INST_ID_CODE,RETR_REF_NO,MERCHANT_ID,ACCT_ID_1,RESP_CODE,C_ACCEP_TERM_ID,AUTHORIZER,
			CHANNEL_ID,ACQUIRING_CHANNEL_ID,AMT_TRAN_BASE,INSTITUTION_ID
		</ColumnNamesString>
		<ReconCreateQueryName>Dummy</ReconCreateQueryName>
		<ReconInsertQueryName>GRANT_RECON_INSERT_QRY</ReconInsertQueryName>
		<ReconUpdateQueryName>DUMMY</ReconUpdateQueryName>
		<ReconUpstreamQueryName>GRANT_RECON_UPSTREAM_QRY</ReconUpstreamQueryName>
		<ReconMatchHandler>DUMMY</ReconMatchHandler>
		<Plugin>poc.hrdf.GrantRecon2</Plugin>
	 </Recon>  -->
 </Recons>
