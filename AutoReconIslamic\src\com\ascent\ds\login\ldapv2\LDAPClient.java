package com.ascent.ds.login.ldapv2;
import java.util.Hashtable;
import java.util.Properties;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import javax.naming.ldap.LdapContext;
import javax.security.auth.Subject;
import javax.security.auth.login.LoginContext;
import javax.security.auth.login.LoginException;

import org.apache.log4j.Logger;
import org.ietf.jgss.GSSContext;
import org.ietf.jgss.GSSException;
import org.ietf.jgss.GSSManager;
import org.ietf.jgss.GSSName;
import org.ietf.jgss.Oid;

import com.itextpdf.text.log.SysoCounter;

/**
 * <p>
 * Client logs in to a Key Distribution Center (KDC) using JAAS and then
 * requests a service ticket for the server, base 64 encodes it and writes it to
 * the file <i>service-ticket.txt</i>.
 * </p>
 * <p>
 * This class, in combination with the <i>Server</i> class illustrates the use
 * of the JAAS and GSS APIs for initiating a security context using the Kerberos
 * protocol.
 * </p>
 * <p>
 * This requires a KDC/domain controller such as Active Directory or Apache
 * Directory. The KDC configuration details are stored in the
 * <i>client.properties</i> file, while the JAAS details are stored in the file
 * <i>jaas.conf</i>.
 * </p>
 * 
 * <AUTHOR> Kavali 
 * @modified By Ankush Gulhane 27/6/2017
 */

@SuppressWarnings("unchecked")
public class LDAPClient {
   // private static Properties prop=null;
    
    
	public LDAPClient() {
		super();
	}
/*	static{
		try {
			FileInputStream fis= new FileInputStream(new File("src/conf/Client.properties"));
			prop= new Properties();
			prop.load(fis);
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}*/
	public static Logger logger = Logger.getLogger(LDAPClient.class);
	private static Oid krb5Oid;
	private static Subject subject;
	private byte[] serviceTicket;
	private static LdapContext ldapContext;

	
	// Authenticate against the KDC using JAAS.
	private static void login(String username, String password) throws LoginException {
		
		LoginContext loginCtx = null;
		// "Client" references the JAAS configuration in the jaas.conf file.
		loginCtx = new LoginContext("Client", new LoginCallbackHandler(username, password));
		loginCtx.login();
		subject = loginCtx.getSubject();
		
				
	}
	
	public static void main(String[] args) {
		LDAPClient a= new LDAPClient();
		a.validateLDapUser("tejaswi","7731813394",null);
 
	}
	// Begin the initiation of a security context with the target service.
	private static LdapContext initiateSecurityContext(String servicePrincipalName,Properties ldapProp) throws GSSException {
		GSSManager manager = GSSManager.getInstance();
		GSSName serverName = manager.createName(servicePrincipalName, GSSName.NT_HOSTBASED_SERVICE);
		final GSSContext context = manager.createContext(serverName, krb5Oid, null, GSSContext.DEFAULT_LIFETIME);
		logger.info("The GSS context initiation has to be performed as a privileged");
		return ldapContext =  (LdapContext) Subject.doAs(subject, new JndiAction(ldapProp));

	}
	// Base64 encode the raw ticket and write it to the given file.
	/*private static void encodeAndWriteTicketToDisk(byte[] ticket, String filepath) throws IOException {
		BASE64Encoder encoder = new BASE64Encoder();
		FileWriter writer = new FileWriter(new File(filepath));
		String encodedToken = encoder.encode(ticket);
		writer.write(encodedToken);
		writer.close();
	}
*/
	
	
	// Authenticate against the KDC using JAAS.
	public boolean kerberosAuthentication(String username,String password, Properties ldapProp) {
		
		boolean authenticated=false;
		try {
			logger.info("Setup up the Kerberos properties");
			
			String realm = ldapProp.getProperty("realm"); 
			String kdc =ldapProp.getProperty("kdc"); 
			String servicePrincipalName = ldapProp.getProperty("service.principal.name") ;

			
			System.setProperty("sun.security.krb5.debug", "true");
			System.setProperty("java.security.krb5.realm", realm); 
			System.setProperty("java.security.krb5.kdc", kdc); 
			//System.setProperty("file.encoding", "ASCII");
			//String relativeWebPath = "/resources/props/jaas.conf";
			//ServletContext servletContext = (ServletContext) FacesContext.getCurrentInstance().getExternalContext().getContext();
			//String absoluteDiskPath = servletContext.getRealPath(relativeWebPath);		
			//System.setProperty("java.security.auth.login.config", absoluteDiskPath);
			///HRDFAutoRecon/
			System.setProperty("java.security.auth.login.config", ldapProp.getProperty("jassPath"));
			System.setProperty("javax.security.auth.useSubjectCredsOnly", "true");	
			//Map<Object, Object> map=System.getProperties();
			//System.out.println(map);
			logger.info("Oid mechanism = use Kerberos V5 as the security mechanism.");
			krb5Oid = new Oid("1.2.840.113554.1.2.2");
			logger.info("Login to the KDC.");			
			LDAPClient.login(username, password);
			logger.info("Request the service ticket.");
			LdapContext ldapContext=LDAPClient.initiateSecurityContext(servicePrincipalName,ldapProp);
			if(ldapContext!=null){
				authenticated=true;
				logger.info("Authenticated Successfully for "+username);
			}
		
		} catch (LoginException e) {
			authenticated=false;
			logger.info("There was an error during the JAAS login for "+username);
			logger.error("There was an error during the JAAS login for "+username);

		} catch (GSSException e) {
			authenticated=false;
			logger.info("There was an error during the security context initiation"+username);
			logger.error("There was an error during the security context initiation",e);
			
		} catch (Exception e) {
			authenticated=false;
			logger.info("Unable to authenticate for "+username);
			logger.error("There was an error during the security context initiation",e);
			e.printStackTrace();
		}
		
		return authenticated;
		
	}
	
	/** This is simple method for ldap auth
	 * 
	 * @param userName
	 * @param password
	 * @param url
	 * @return
	 */
	public boolean validateLDapUser(String userName, String password,Properties ldapProp){
		boolean isvalid=false;
		String url =ldapProp.getProperty("host");
		String distinctName= ldapProp.getProperty("dn");
		String securitPrincipal=userName+"@"+distinctName;
		//String securitPrincipal=distinctName+"\\"+userName;
		
		Hashtable<String, String> env = new Hashtable<String, String>();
		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.REFERRAL, "follow");
		env.put(Context.PROVIDER_URL, url);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		env.put(Context.SECURITY_PRINCIPAL, securitPrincipal);
		env.put(Context.SECURITY_CREDENTIALS, password);

		try {
			//DirContext ctx = new InitialDirContext(env);
			Context ctx = new InitialDirContext(env);
			if(ctx!=null){
				isvalid=true;
			}
			ctx.close();

		} catch (Exception e) {
			isvalid=false;
			logger.info("There was an error during the Ldap connection or User authentication or paswwrord is invalid " +e.getMessage());
			e.printStackTrace();

		}
		return isvalid;
	}
	
	/*public boolean validateLDapUser(String userName, String password, Properties ldapProp) {
		boolean isvalid = true;
		try {
			System.out.println("Before Authentication....");
			Hashtable<String, String> env = new Hashtable<String, String>(11);
			env.put(Context.INITIAL_CONTEXT_FACTORY,"com.sun.jndi.ldap.LdapCtxFactory");
			env.put(Context.REFERRAL, "follow");
			String providerUrl = "ldap://*********:389";
			env.put(Context.PROVIDER_URL, providerUrl);
			env.put(Context.SECURITY_AUTHENTICATION, "simple");
			env.put(Context.SECURITY_PRINCIPAL, userName);
			env.put(Context.SECURITY_CREDENTIALS, password);
			Context context = new InitialContext(env);
			System.out.println("Succesful Authenticated....");
		} catch (Exception e) {
			e.printStackTrace();
			  System.out.println("SOMETHING WENT  WRONG, TRY AGAIN");
			  isvalid=false;
		}
		return isvalid;
		}*/
	
}
