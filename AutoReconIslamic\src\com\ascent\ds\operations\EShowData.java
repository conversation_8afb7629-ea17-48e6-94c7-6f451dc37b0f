package com.ascent.ds.operations;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.integration.util.DbUtil;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

/**
 * Kaushal
 */
public class EShowData extends BasicDataSource implements PagesConstants {
	public static final String ShowExternalDataDS = "ShowExternalDataDS";
	public static final String ManualEntries = "ManualEntries";
	private static final String INSERT_ONS_INTERNAL_MANUAL_ENTRIES = "INSERT_ONS_INTERNAL_MANUAL_ENTRIES1";
	static AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public DSResponse executeFetch(final DSRequest request) throws Exception {

		DSResponse dsResponse = new DSResponse();
		Map requestParams = request.getCriteria();
		String filename = (String) requestParams.get("fileName");
		String extension = filename.substring(filename.lastIndexOf(".") + 1, filename.length());
		String excel = "xlsx";
		/*
		 * if (extension != excel) { JOptionPane.showMessageDialog(null,
		 * "Choose an excel file!"); }
		 */
		/* String reconName = (String) requestParams.get("data"); */
		String action = (String) requestParams.get("data");
		byte fileByte[] = Base64.getDecoder().decode(action);
		File pathFile = new File(System.getProperty("java.io.tmpdir")+"\\MANUAL_ENTRY\\");
		if (!pathFile.exists())
			pathFile.mkdir();
		File creditFile = new File(pathFile + File.separator + filename);

		if (!creditFile.exists())
			creditFile.createNewFile();
		else {
			creditFile.delete();
			creditFile.createNewFile();
		}
		FileOutputStream fos = new FileOutputStream(creditFile);
		fos.write(fileByte);
		fos.close();

		String reconTableName = (String) requestParams.get("reconTableName");
		String integrationName = (String) requestParams.get("integrationName");
		// String centrifugalAmountField = "";
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");

		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		Map<String, Object> paramsMap = new HashMap<String, Object>();

		paramsMap.put(USER_ID, user);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(MODULE, ManualEntries);
		paramsMap.put(DS_NAME, ShowExternalDataDS);

		try {
			FileInputStream fileIn = null;
			FileOutputStream fileOut = null;
			XSSFWorkbook wb = null;

			fileIn = new FileInputStream(System.getProperty("java.io.tmpdir")+"\\MANUAL_ENTRY\\"+filename);
			wb = new XSSFWorkbook(fileIn);
			XSSFSheet sheet = wb.getSheetAt(0);
			Iterator<Row> rowIterator = sheet.iterator();

			List<Map<String, Object>> recordList = new ArrayList<Map<String, Object>>();
			String columnArray[] = { "TRAN_REF_NUM", "INTERNAL_REF_NUM", "ACCOUNT", "TRAN_DATE", "VALUE_DATE", "AMOUNT",
					"DRCR", "CURRENCY", "PAN_NUMBER", "ORIGINATOR_BID", "DESTINATION_BID", "ACQUIRING_INSTITUTION_ID",
					"CARD_ACCEPTOR_NAME", "MERCHANT_CATEGORY_CODE", "TRANSACTION_TYPE" };
			int rowCount = 0;
			while (rowIterator.hasNext()) {
				Row row = rowIterator.next();
				if (rowCount != 0) {
					Map<String, Object> record = new HashMap<String, Object>();

					Iterator<Cell> cellIterator = row.cellIterator();
					int count = 0;
					while (cellIterator.hasNext()) {

						Cell cell = cellIterator.next();
						String value = "";
						switch (cell.getCellType()) {
						case Cell.CELL_TYPE_STRING:
							value = cell.getStringCellValue();
							break;
						case Cell.CELL_TYPE_NUMERIC:
							if (DateUtil.isCellDateFormatted(cell)) {
								SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
								value = dateFormat.format(cell.getDateCellValue());
							} else {
								value = NumberToTextConverter.toText(cell.getNumericCellValue());
							}
							break;
						case Cell.CELL_TYPE_BOOLEAN:
							value = ((Boolean) cell.getBooleanCellValue()).toString();
							break;
						}

						record.put(columnArray[count], value);
						count++;
					}
					recordList.add(record);

				}
				rowCount++;
			}
			System.out.println("Internal Manual Count : "+recordList.size());
			// new ManualDao().OnsInternalMethod(recordList);
			paramsMap.put(SELECTED_RECORDS, recordList);
			// new ManualEntriesPlugin().process(recordList);
			// createManualEntriesActivity(paramMap);
			dsResponse.setData(recordList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dsResponse;

	}

	public static void createManualEntriesActivity(Map<String, Object> paramsMap) {
		Connection connection = null;
		try {
			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			// userAdminManager.getUsercontroller().getUsers().getUser(userId);
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			connection = DbUtil.getConnection();
			String activityStatus = PENDING_APPROVAL;
			paramsMap.put(PERSIST_CLASS, MANUALENTRIES_PLUGIN_CLASS_NAME);

			User user = (User) paramsMap.get(USER_ID);
			paramsMap.remove(USER_ID);
			activityDataMap.put("activity_data", paramsMap);
			userAdminManager.createActivity(connection, user, paramsMap.get(BUSINES_AREA).toString(),
					paramsMap.get(RECON_NAME).toString(), "", INTERNAL_MANUAL_OPERATION, activityDataMap,
					activityStatus, paramsMap.get(MODULE).toString());

			// persist(activityDataMap, APPROVED, connection);

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

}
