package com.ascent.integration.sources.visa;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.query.Query;
import com.ascent.integration.enrichment.AscentEnrichmentPlugin;
import com.ascent.integration.persistance.AscentPersistanceIntf;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.integration.validation.AscentValidationPlugin;

public class VisaPersistancePlugin implements AscentPersistanceIntf {

	private static Logger logger = LogManager.getLogger(VisaPersistancePlugin.class.getName());

	@Override
	public Map<String, Object> persist(com.ascent.custumize.integration.Integration integration,
			com.ascent.custumize.query.Queries queries, Connection connection, List<Map<String, Object>> txnList,
			Boolean exFound) throws Exception {

		Map<String, Object> result = new HashMap<String, Object>();

		AscentEnrichmentPlugin ascentEnrichmentPlugin = null;
		AscentValidationPlugin ascentPreEnrichValidationPlugin = null;
		AscentValidationPlugin ascentPostEnrichValidationPlugin = null;

		PreparedStatement stagingInsertPstmt = null;
		PreparedStatement stagingExInsertPstmt = null;
		PreparedStatement stagingExUpdatePstmt = null;
		PreparedStatement seqNoPstmt = null;
		Map<String, Object> tempTxn = null;
		try {
			String enrichClass = integration.getEnrichmentPlugin();
			String preValidationClass = integration.getPreEnrichValidationPlugin();
			String postValidationClass = integration.getPostEnrichValidationPlugin();

			ascentEnrichmentPlugin = (AscentEnrichmentPlugin) ((Class.forName(enrichClass)).newInstance());
			ascentPreEnrichValidationPlugin = (AscentValidationPlugin) ((Class.forName(preValidationClass))
					.newInstance());
			ascentPostEnrichValidationPlugin = (AscentValidationPlugin) ((Class.forName(postValidationClass))
					.newInstance());

			String seqName = integration.getSequenceName();
			String seqNumQry = "SELECT NEXT VALUE FOR " + seqName + " as sno";
			Query stgInsertQry = queries.getQueryConf(integration.getStagingInsertQueryName());
			Query stgExInsertQry = queries.getQueryConf(integration.getStagingExInsertQueryName());
			Query stgExUpdateQry = queries.getQueryConf(integration.getStagingExUpdateQueryName());
			stagingExUpdatePstmt = connection.prepareStatement(stgExUpdateQry.getQueryString());
			stagingInsertPstmt = connection.prepareStatement(stgInsertQry.getQueryString());
			stagingExInsertPstmt = connection.prepareStatement(stgExInsertQry.getQueryString());
			seqNoPstmt = connection.prepareStatement(seqNumQry);
			for (Map<String, Object> txn : txnList) {
				if (exFound) {

					Long exid = (Long) txn.get("SID");

					txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));

					txn.put("COMMENTS", "");
					txn.put("WORKFLOW_STATUS", "No");
					txn.put("OPERATION", "Re-Process");

					txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
					txn.put("CREATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
					tempTxn.putAll(txn);
					boolean flag = ascentPreEnrichValidationPlugin.validate(txn);

					if (flag) {

						ascentEnrichmentPlugin.enrich(txn);
						flag = ascentPostEnrichValidationPlugin.validate(txn);

						if (flag) {

							int version = (int) tempTxn.get("VERSION");
					//		txn.put("VERSION", ++version);
							try {
								PersistanceUtil.persistTxn(stagingInsertPstmt, stgInsertQry, txn);
							} catch (Exception e) {
								System.out.println(e.getMessage());

								tempTxn.put("COMMENTS", "Recard Already Exist");
								PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
							}
							//PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
							exFound = false;

						} else {
							int version = (int) tempTxn.get("VERSION");
					//		tempTxn.put("VERSION", ++version);
							tempTxn.put("COMMENTS", txn.get("COMMENTS"));
							PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
							//PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
						}

					} else {
						int version = (int) tempTxn.get("VERSION");
					//	tempTxn.put("VERSION", ++version);
						tempTxn.put("COMMENTS", txn.get("COMMENTS"));
						PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
						//PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
					}

				}

				else {
				long uid = PersistanceUtil.generateSeqNo(connection, seqNoPstmt, seqName);
				txn.put("SID", uid);
				txn.put("VERSION", 1);
				txn.put("ACTIVE_INDEX", "Y");
				txn.put("STATUS", "new");
				txn.put("COMMENTS", "Staging");
				txn.put("WORKFLOW_STATUS", "No");
				txn.put("ATTACHMENT_ID", 0);
				txn.put("USER_ID", "system");
				txn.put("OPERATION", "ETL");
				// Date temp = new Date();
				txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));

				tempTxn = new HashMap<String, Object>();
				tempTxn.putAll(txn);
				ascentEnrichmentPlugin.enrich(txn);
				tempTxn.put("COMMENTS", txn.get("COMMENTS"));
				try {
					PersistanceUtil.persistTxn(stagingInsertPstmt, stgInsertQry, tempTxn);
				} catch (Exception e) {
					e.printStackTrace();
				}
				}
			}

		} catch (Exception e) {
			System.out.println("/////////////////////////////////////////////////");
			System.out.println(tempTxn);
			System.out.println("/////////////////////////////////////////////////");
			e.printStackTrace();
		} finally {
			DbUtil.closePreparedStatement(stagingExInsertPstmt);
			DbUtil.closePreparedStatement(stagingInsertPstmt);
		}

		return result;

	}

}
