package com.ascent.integration.sources.visa;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;
import com.ascent.integration.persistance.AscentPersistanceIntf;
import com.ascent.integration.util.DbUtil;

public class VisaSmsParser {

	private static Logger logger = LogManager.getLogger(VisaSmsParser.class.getName());
	DbUtil dbUtil = new DbUtil();
	Queries queries = null;
	Integration integration = null;
	String integrationName;
	AscentPersistanceIntf ascentPersistancePlugin = null;
	EtlMetaInstance integrationMetaInstance = null;
	Properties appProps = null;
	public static long totalTxnRecs = 0l;
	public static long totalTxnRecsTemp = 0l;
	public static long insertCount = 0l;
	public static long insertCountEx = 0l;
	public static long start = 0l;

	public VisaSmsParser(String integrationName) throws Exception {
		this.integrationMetaInstance = EtlMetaInstance.getInstance();
		this.integrationName = integrationName;
		this.integration = this.integrationMetaInstance.getEtlConf(this.integrationName);
		this.queries = this.integrationMetaInstance.getEtlQueryConfs();
		this.appProps = this.integrationMetaInstance.getApplicationProperties();
	}

	public static void main(String args[]) throws Exception {

		VisaSmsParser visaParser = new VisaSmsParser("visa");

	}

	public static String[] COLUMN_NAMES = { "CA ID", "FPI", "CD SQ", "ATC", "TR ID", "ACI", "DG CD", "VC", "FEE JURIS",
			"ROUTING", "FEE LEVEL", "CI" };
	Map<String, Object> dataHeaderMap = new HashMap<String, Object>();

	public static boolean isEmpty(Object value) {
		if (value instanceof List) {
			return (value != null && !((List) value).isEmpty() && ((List) value).size() > 0) ? false : true;
		}
		return (value == null || value.toString().trim().isEmpty()) ? true : false;
	}

	public static boolean getData(String data, String token) {
		StringBuilder result = new StringBuilder();

		if (data != null) {
			Pattern pattern = Pattern.compile(token);
			Matcher matcher = pattern.matcher(data);
			while (matcher.find()) {
				result.append(matcher.group().toString());
				return true;
			}
		}

		return false;
	}

	public static String getDataString(String data, String token) {
		StringBuilder result = new StringBuilder();

		if (data != null) {
			Pattern pattern = Pattern.compile(token);
			Matcher matcher = pattern.matcher(data);

			while (matcher.find()) {
				result.append(matcher.group().toString());

			}
		}

		String retStr = result.toString();
		return (retStr.isEmpty()) ? null : retStr;
	}

	public void processFile(File folder) {

		File[] listOfFiles = folder.listFiles();

		for (int i = 0; i < listOfFiles.length; i++) {
			if (listOfFiles[i].isFile()) {
				System.out.println("File " + listOfFiles[i].getName());
				File file = listOfFiles[i];
				String fileName = file.getName();

				Pattern pattern = Pattern.compile(integration.getFileNamePattern());

				Matcher matcher = pattern.matcher(fileName);
				boolean matchFound = false;
				while (matcher.find()) {
					matchFound = true;
				}
				if (matchFound) {
					process(file);

				}

			} else if (listOfFiles[i].isDirectory()) {
				processFile(listOfFiles[i]);
				System.out.println("Directory " + listOfFiles[i].getName());
			}
		}

	}

	private void process(File file) {
		BufferedReader br = null;
		List<String> list = new ArrayList<String>();
		FileInputStream fis = null;

		try {

			fis = new FileInputStream(file);
			br = new BufferedReader(new InputStreamReader(fis));

			String line = null;

			while ((line = br.readLine()) != null) {

				list.add(line);

			}

			Map<String, Object> params = new HashMap<String, Object>();
			params.put("BLOCK_START_POSITION", 0);
			params.put("BLOCK_END_POSITION", list.size());
			params.put("BLOCK_DATA", list);
			process(params);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				br.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}

	public Map<String, Object> process(Map<String, Object> args) {

		try {
			Connection connection = null;
			connection = DbUtil.getConnection();
			ascentPersistancePlugin = (AscentPersistanceIntf) ((AscentPersistanceIntf) ((Class
					.forName((integration.getPersistancePlugin()).trim())).newInstance()));
			Map<String, Object> tempArgs = new HashMap<String, Object>();
			tempArgs.putAll(args);
			args.clear();
			int position = (Integer) tempArgs.get("BLOCK_START_POSITION");
			int blockEnd = (Integer) tempArgs.get("BLOCK_END_POSITION");
			List<String> unStructuredTextDataRow = (List<String>) tempArgs.get("BLOCK_DATA");
			boolean exceptionFlag = false;
			String exceptionMsg = "";

			List<Map<String, Object>> listData = new ArrayList<Map<String, Object>>();

			int numIndex = 0;
			int dateIndex = 0;
			int timeIndex = 0;
			int cardnoIndex = 0;
			int refnoIndex = 0;
			int numberIndex = 0;
			int trmnlIndex = 0;
			int typeIndex = 0;
			int prosscodeIndex = 0;
			int modIndex = 0;
			int codeIndex = 0;
			int stpIndex = 0;
			int cdIndex = 0;
			int transamtIndex = 0;
			int setamtIndex = 0;
			int i = position;

			boolean indexesIdentified = false;
			Map<String, Object> dateMap = null;
			try {
				while (i < blockEnd) {

					String result = null;
					String dataString = null;

					dataString = unStructuredTextDataRow.get(i);

					if (dataString.contains("REPORT DATE")) {
						dateMap = new HashMap<String, Object>();
						String report_date = dataString.split(":")[2].trim();
						SimpleDateFormat sdf = new SimpleDateFormat("ddMMMyy");
						Date date = sdf.parse(report_date);
						dateMap.put("report_date", report_date);
						i++;
						continue;
					}

					result = getDataString(dataString,
							"\\d{2}\\s+\\d{2}[A-Z]{3}\\s+\\d{2}(:)\\d{2}(:)\\d{2}\\s+\\d{4}[([#]{8}\\d{4})|(\\d{13})]");
					if (result != null && indexesIdentified) {

						if (listData.size() > 0 && (listData.size() % 100) == 0) {

							List<Map<String, Object>> recordsList = new ArrayList<Map<String, Object>>();
							recordsList.addAll(listData);
							tempArgs.remove("BLOCK_START_POSITION");
							tempArgs.remove("BLOCK_END_POSITION");
							tempArgs.remove("BLOCK_DATA");
							// System.out.println(recordsList);

							ascentPersistancePlugin.persist(integration, queries, connection, recordsList, false);
							listData.clear();
						}

						Map<String, Object> record = new HashMap<String, Object>();
						processData(numIndex, dateIndex, timeIndex, cardnoIndex, refnoIndex, numberIndex, trmnlIndex,
								typeIndex, prosscodeIndex, modIndex, codeIndex, stpIndex, cdIndex, transamtIndex,
								setamtIndex, dataString, record);
						record.put("report_date", dateMap.get("report_date"));
						listData.add(record);

						i++;
						continue;

					}
					// For 2nd Line
					if (dataString.contains("CA ID:") || dataString.contains("FPI")) {
						Map<String, Object> inProgressRecord = listData.get(listData.size() - 1);
						processLine(inProgressRecord, dataString);
						String ca_id = ((String) inProgressRecord.get("ca_id")).trim();
						String terminal_id = ca_id.substring(0, 8);

						inProgressRecord.put("terminal_id",
								(terminal_id == null || terminal_id.isEmpty()) ? 0 : terminal_id.trim());

						i++;
						continue;
					}
					// for 4th Line
					if (dataString.contains("TR ID:") || dataString.contains("ACI:") || dataString.contains("DG CD:")
							|| dataString.contains("VC:")) {
						Map<String, Object> inProgressRecord = listData.get(listData.size() - 1);
						processLine(inProgressRecord, dataString);

						i++;
						continue;
					}
					// For 3rd Line
					if (dataString.contains("ATC:") || dataString.contains("CD SQ:") || dataString.contains("CI:")) {
						Map<String, Object> inProgressRecord = listData.get(listData.size() - 1);
						processLine(inProgressRecord, dataString.trim());

						i++;
						continue;
					}
					// for 5th Line
					if (dataString.contains("ROUTING:") || dataString.contains("FEE JURIS:")
							|| dataString.contains("FEE LEVEL:")) {
						Map<String, Object> inProgressRecord = listData.get(listData.size() - 1);
						processLine(inProgressRecord, dataString);

						i++;
						continue;
					}

					if (dataString.contains("NUM")) {
						result = getDataString(dataString,
								"[ ]*(NUM)\\s+(DATE)\\s+(TIME)\\s+(CARD NUMBER)\\s+(REF NUMBER)\\s+(NUMBER)\\s+(TRMNL/NAME)\\s+(TYPE)\\s+(CODE)\\s+(MOD)\\s+(CODE)\\s+(STP)\\s+(CD)\\s+(AMOUNT CUR)\\s+(AMOUNT \\WUSD\\W)\\s*");

						if (result != null) {

							numIndex = dataString.indexOf("NUM");
							dateIndex = dataString.indexOf("DATE");
							timeIndex = dataString.indexOf("TIME");
							cardnoIndex = dataString.indexOf("CARD NUMBER");
							refnoIndex = dataString.indexOf("REF NUMBER");
							numberIndex = dataString.lastIndexOf("NUMBER");
							trmnlIndex = dataString.indexOf("TRMNL/NAME");
							typeIndex = dataString.indexOf("TYPE");
							prosscodeIndex = dataString.indexOf("CODE");
							modIndex = dataString.indexOf("MOD");
							codeIndex = dataString.lastIndexOf("CODE");
							stpIndex = dataString.indexOf("STP");
							cdIndex = dataString.indexOf("CD");
							transamtIndex = dataString.indexOf("AMOUNT CUR") - 3;
							setamtIndex = dataString.indexOf("AMOUNT (USD)");
							indexesIdentified = true;
							i++;
							continue;
						}
					}

					i++;

				}

				if (listData.size() > 0) {

					List<Map<String, Object>> recordsList = new ArrayList<Map<String, Object>>();
					recordsList.addAll(listData);
					tempArgs.remove("BLOCK_START_POSITION");
					tempArgs.remove("BLOCK_END_POSITION");
					tempArgs.remove("BLOCK_DATA");
					// System.out.println(recordsList);

					ascentPersistancePlugin.persist(integration, queries, connection, recordsList, false);
					listData.clear();
				}
			} catch (Exception e) {
				e.printStackTrace();
				exceptionFlag = true;
				exceptionMsg = e.getMessage();
			} finally {
				String subject = null;
				if (exceptionFlag) {
					subject = "Error";

					// send error to mail
				}

			}
			args.put("isException", exceptionFlag);
			args.put("DATA", new HashMap<String, Object>());

		} catch (Exception e) {
			e.printStackTrace();
		}
		return args;

	}

	private void processLine(Map<String, Object> inProgressRecord, String dataString) {

		String[] tempArray = dataString.split(":");
		if (tempArray != null && tempArray.length > 1) {

			String columnName = tempArray[0].trim();

			for (int index = 1; index < tempArray.length; index++) {
				String token = tempArray[index];
				String tempColumnName = (columnName.toLowerCase()).replace(" ", "_");
				boolean valueFound = false;
				for (String colname : COLUMN_NAMES) {
					if (token.contains(colname)) {
						String colValue = token.substring(0, token.indexOf(colname));
						inProgressRecord.put(tempColumnName, colValue.trim());
						columnName = colname;
						valueFound = true;
						break;
					}
				}
				if (!valueFound) {
					inProgressRecord.put(tempColumnName, token);

				}

			}

		}
	}

	private void processData(int numIndex, int dateIndex, int timeIndex, int cardnoIndex, int refnoIndex,
			int numberIndex, int trmnlIndex, int typeIndex, int prosscodeIndex, int modIndex, int codeIndex,
			int stpIndex, int cdIndex, int transamtIndex, int setamtIndex, String result, Map<String, Object> data)
					throws ParseException {
		// System.out.println(result);

		String numValue = result.substring(numIndex, dateIndex);
		String dateValue = result.substring(dateIndex, timeIndex);
		String timeValue = result.substring(timeIndex, cardnoIndex);
		String cardnoValue = result.substring(cardnoIndex, refnoIndex);
		String refnoValue = result.substring(refnoIndex, numberIndex);
		String numberValue = result.substring(numberIndex, trmnlIndex);
		String trmnlValue = result.substring(trmnlIndex, typeIndex);
		String typeValue = result.substring(typeIndex, prosscodeIndex);
		String processcodeValue = result.substring(prosscodeIndex, modIndex);
		String modValue = result.substring(modIndex, codeIndex);
		String codeValue = result.substring(codeIndex, stpIndex);
		String stpValue = result.substring(stpIndex, cdIndex);
		String cdValue = result.substring(cdIndex, transamtIndex);
		String transamtValue = result.substring(transamtIndex, setamtIndex);
		String setlmntamtValue = result.substring(setamtIndex, 131);

		data.put("bat_num", (numValue == null || numValue.isEmpty()) ? 0 : numValue.trim());
		data.put("xmit_date", (dateValue == null || dateValue.isEmpty()) ? 0 : dateValue.trim());
		data.put("local_time", (timeValue == null || timeValue.isEmpty()) ? 0 : timeValue.trim());
		String local_time = (String) data.get("local_time");
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
		Date time = sdf.parse(local_time);
		data.put("time", local_time);
		data.put("card_number", (cardnoValue == null || cardnoValue.isEmpty()) ? 0 : cardnoValue.trim());
		String card_number = (String) data.get("card_number");
		if (card_number != null && card_number.length() > 11) {
			card_number = card_number.substring(0, 6) + "******" + card_number.substring(card_number.length() - 4);

			data.put("card_number", card_number);

		}
		data.put("retrieval_ref_number", (refnoValue == null || refnoValue.isEmpty()) ? 0 : refnoValue.trim());
		data.put("trace_number", (numberValue == null || numberValue.isEmpty()) ? 0 : numberValue.trim());
		data.put("issuer_trmnl", (trmnlValue == null || trmnlValue.isEmpty()) ? 0 : trmnlValue.trim());
		data.put("tran_type", (typeValue == null || typeValue.isEmpty()) ? 0 : typeValue.trim());
		data.put("procss_code", (processcodeValue == null || processcodeValue.isEmpty()) ? 0 : processcodeValue.trim());
		data.put("ent_mod", (modValue == null || modValue.isEmpty()) ? 0 : modValue.trim());
		data.put("reas_code", (codeValue == null || codeValue.isEmpty()) ? 0 : codeValue.trim());
		data.put("cn_stp", (stpValue == null || stpValue.isEmpty()) ? 0 : stpValue.trim());
		data.put("rsp_cd", (cdValue == null || cdValue.isEmpty()) ? 0 : cdValue.trim());

		data.put("transaction_amount", (transamtValue == null || transamtValue.isEmpty()) ? 0 : transamtValue.trim());
		data.put("settlement_amount",
				(setlmntamtValue == null || setlmntamtValue.isEmpty()) ? 0 : setlmntamtValue.trim());
		String transaction_amount = (String) data.get("transaction_amount");

		Matcher m = Pattern.compile("\\p{Lu}+").matcher(transaction_amount);
		if (m.find()) {
			String cr_currency = m.group();
			data.put("kes_currency", cr_currency);
			transaction_amount = transaction_amount.replace(cr_currency, "");
			data.put("transaction_amount", transaction_amount.replace(",", ""));
		}

		String settlement_amount = (String) data.get("settlement_amount");
		Matcher m1 = Pattern.compile("\\p{Lu}+").matcher(settlement_amount);
		if (m1.find()) {
			String cr_currency = m1.group();
			data.put("cr_currency", cr_currency);
			settlement_amount = settlement_amount.replace(cr_currency, "");
			data.put("settlement_amount", settlement_amount.replace(",", ""));

		}
		if (typeValue != null && !typeValue.isEmpty()) {
			if ("0200".equalsIgnoreCase(typeValue.trim())) {
				data.put("MAIN_REV_IND", "M");
			}
			if ("0420".equalsIgnoreCase(typeValue.trim())) {
				data.put("MAIN_REV_IND", "R");
			}

		}

	}

}
