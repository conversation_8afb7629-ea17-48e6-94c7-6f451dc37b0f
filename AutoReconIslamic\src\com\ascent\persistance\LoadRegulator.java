package com.ascent.persistance;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;

public class LoadRegulator implements DbRegulatorConstants, Serializable {

	private static final long serialVersionUID = 1882053126044571170L;
	private static Logger logger = LogManager.getLogger(LoadRegulator.class.getName());

	public LoadRegulator() {

	}

	public List<Map<String, Object>> loadCompleteData(Map<String, Object> dataMap, Query queryConf)
			throws ClassNotFoundException, SQLException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		try {
			connection = DbUtil.getConnection();
			preparedStatement = connection.prepareStatement(queryConf.getQueryString());

			List<Map<String, Object>> tempList = loadCompleteData(dataMap, preparedStatement,
					queryConf.getQueryParam());

			list.addAll(tempList);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			RegulatorUtil.closePreparedStatement(preparedStatement);
			RegulatorUtil.closeConnection(connection);
		}
		return list;

	}

	public List<Map<String, Object>> loadCompleteData(Map<String, Object> dataMap, PreparedStatement preparedStatement,
			String queryParamStringWithType) throws ClassNotFoundException, SQLException {

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		try {

			DbCursor dbCursor = load(preparedStatement, dataMap, queryParamStringWithType);

			List<Map<String, Object>> tempRecords = dbCursor.getNextBatch();

			while (tempRecords.size() > 0) {
				list.addAll(tempRecords);
				tempRecords.clear();
				tempRecords = dbCursor.getNextBatch();

			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {

		}
		return list;

	}

	/*
	 * * this method gives the result set object
	 */
	public DbCursor load(Connection connection, Query queryConf, Map<String, Object> args) {
		DbCursor cursor = null;
		PreparedStatement loadPstmt = null;

		try {
			String loadQry = queryConf.getQueryString();
			String loadQryParams = queryConf.getQueryParam();

			loadPstmt = connection.prepareStatement(loadQry);

			cursor = load(loadPstmt, args, loadQryParams);

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
		}

		return cursor;

	}

	public DbCursor load(PreparedStatement loadPstmt, Map<String, Object> args, String queryParamStringWithType) {

		DbCursor ascentResultSet = null;
		ResultSet resultSet = null;
		Map<String, Integer> paramTypeMap = null;
		List<String> paramList = null;
		try {
			Map<String, Object> paramValuemap = null;
			Integer batchSize = 25;

			if (args != null) {
				batchSize = (Integer) args.get(BATCH_SIZE);
				paramValuemap = (Map<String, Object>) args.get(PARAM_VALUE_MAP);
			}
			if (args != null && queryParamStringWithType != null && !queryParamStringWithType.isEmpty()) {

				if (paramValuemap == null) {
					paramValuemap = args;
				}
				if (queryParamStringWithType != null && !(queryParamStringWithType.trim()).isEmpty()) {
					String[] paramArrayWithType = queryParamStringWithType.split(",");
					paramTypeMap = Query.getParamTypeMap(paramArrayWithType);
					paramList = Query.getParamList(paramArrayWithType);
				}

				int index = 1;

				if (paramList != null && !paramList.isEmpty()) {
					for (String param : paramList) {

						loadPstmt.setObject(index, paramValuemap.get(param), paramTypeMap.get(param));
						index++;
					}
				}

			}
			resultSet = loadPstmt.executeQuery();

			ResultSetMetaData resultSetMetaData = resultSet.getMetaData();
			int noOfColumns = resultSetMetaData.getColumnCount();

			List<String> columnNames = new ArrayList<String>();

			for (int i = 1; i <= noOfColumns; i++) {

 				String columnName = resultSetMetaData.getColumnName(i);
				columnNames.add(columnName);
			}
			if (batchSize != null) {
				ascentResultSet = new DbCursor(noOfColumns, batchSize, loadPstmt, resultSet, resultSetMetaData,
						columnNames);
			} else {
				ascentResultSet = new DbCursor(noOfColumns, loadPstmt, resultSet, resultSetMetaData, columnNames);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {

		}

		return ascentResultSet;

	}

	public static void main(String[] args) {
		LoadRegulator dbCurdRUC = new LoadRegulator();
		Connection connection = null;

		Map<String, Object> params = new HashMap<String, Object>();

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
		try {

			Query query = queries.getQueryConf("name");
			connection = DbUtil.getConnection();

			DbCursor ascentResultSet = dbCurdRUC.load(connection, query, params);

			List<Map<String, Object>> records = ascentResultSet.getNextBatch();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	// To Generate Next Sequence Number
	public synchronized Long generateLazySeqNo(String seqName) throws Exception {

		Connection connectionforSeq = null;
		PreparedStatement seqNoPstmt = null;
		ResultSet txnRs = null;
		String seqNumQry = null;
		try {
			seqNumQry = "SELECT NEXT VALUE FOR " + seqName + "  as sno";

			connectionforSeq = DbUtil.getConnection();
			seqNoPstmt = connectionforSeq.prepareStatement(seqNumQry);

			txnRs = seqNoPstmt.executeQuery();
			if (txnRs.next()) {
				return (Long) txnRs.getLong(1);
			}
		} catch (SQLException e) {
			Connection connection = null;
			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;

			try {
				connection = DbUtil.getConnection();
				createSeqNumQry = "CREATE  SEQUENCE " + seqName + "   AS BIGINT START WITH 1  INCREMENT BY 1 ";

				createSeqPstmt = connection.prepareStatement(createSeqNumQry);
				createSeqPstmt.executeUpdate();

				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

				ResultSet rs = seqNumPstmtTemp.executeQuery();
				if (rs.next()) {
					return (Long) rs.getLong(1);
				}

			} catch (Exception e1) {
				logger.error("Unable to create ssequence : " + seqName + ". error:" + e1);
				throw e1;

			} finally {

				RegulatorUtil.closePreparedStatement(createSeqPstmt);
				RegulatorUtil.closePreparedStatement(seqNumPstmtTemp);
				RegulatorUtil.closeConnection(connection);
			}
		}
		return (Long) 0l;

	}

	// To Generate next sequence number
	public synchronized Long generateSeqNo(PreparedStatement seqNoPstmt, String seqName) throws Exception {

		try {
			ResultSet txnRs = seqNoPstmt.executeQuery();
			if (txnRs.next()) {
				return txnRs.getLong(1);
			}
		} catch (SQLException e) {
			Connection connection = null;
			PreparedStatement createSeqPstmt = null;
			PreparedStatement seqNumPstmtTemp = null;
			String createSeqNumQry = null;
			String seqNumQry = null;
			try {
				connection = DbUtil.getConnection();
				createSeqNumQry = "CREATE  SEQUENCE " + seqName + "   AS BIGINT START WITH 1  INCREMENT BY 1 ";
				seqNumQry = "SELECT NEXT VALUE FOR " + seqName + "  as sno";

				createSeqPstmt = connection.prepareStatement(createSeqNumQry);
				createSeqPstmt.executeUpdate();

				seqNumPstmtTemp = connection.prepareStatement(seqNumQry);

				ResultSet rs = seqNumPstmtTemp.executeQuery();
				if (rs.next()) {
					return rs.getLong(1);
				}

			} catch (Exception e1) {
				logger.trace("unable to create sequence: " + seqName + ". error" + e1);
				e1.printStackTrace();
				throw e1;

			} finally {

				RegulatorUtil.closePreparedStatement(createSeqPstmt);
				RegulatorUtil.closePreparedStatement(seqNumPstmtTemp);
				RegulatorUtil.closeConnection(connection);
			}
		}
		return 0l;

	}

}
