package com.ascent.mailschedular;

import java.io.IOException;

import javax.mail.MessagingException;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.ascent.ds.operations.GenerateGl;

public class TriggerMail implements Job {

	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		try {
			System.out.println("hello");
			try {
				new GenerateGl().bulkFileGeneration();
				new MailApp().sendMail();
				
				System.out.println("BULK FILE GENRATED ____ AND MAIL HAS BEEN SENT ...");
			} catch (MessagingException e) {

				e.printStackTrace();
			}
		} catch (IOException e) {

			e.printStackTrace();
		}

	}

}
