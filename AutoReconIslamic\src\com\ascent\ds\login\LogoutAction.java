package com.ascent.ds.login;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.SessionUtility;

/**
 * Servlet implementation class LogoutAction
 */
@WebServlet("/LogoutAction")
public class LogoutAction extends HttpServlet {
	private static final long serialVersionUID = 1L;
	private static final String ACTION = "LOGOUT";

	/**
	 * @see HttpServlet#HttpServlet()
	 */
	public LogoutAction() {
		super();
		// TODO Auto-generated constructor stub
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		// TODO Auto-generated method stub
		doPost(request, response);

	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {

		String path = request.getServletContext().getContextPath();

		HttpSession httpSession = request.getSession();
		User user = (User) httpSession.getAttribute("userId");
		Map<String, Object> userAuditLogsMap = new HashMap<String, Object>();
		Map<String, Object> userParamValueMap = new HashMap<String, Object>();

		if (user != null) {
			//
			Map<String, Object> login_status = new HashMap<String, Object>();
			login_status.put("user_id", user.getUserId());
			login_status.put("login_flag", "F");
			login_status.put("session",httpSession.toString());
			login_status.put("session_created", httpSession.getCreationTime());
			login_status.put("session_last_accesed", httpSession.getLastAccessedTime());

			LoginDetails details = new LoginDetails();

			String user_selected_business_area = (String) httpSession.getAttribute("user_selected_business_area");
			String user_selected_recon = (String) httpSession.getAttribute("user_selected_recon");
			String usersRole = (String) httpSession.getAttribute("usersRole");
			userParamValueMap.put("user_id", user.getUserId());
			userParamValueMap.put("action", ACTION);
			userParamValueMap.put("date_time", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			userParamValueMap.put("bussiness_area", user_selected_business_area);
			userParamValueMap.put("recon_name", user_selected_recon);
			userParamValueMap.put("user_role", usersRole);

			userAuditLogsMap.put("PARAM_VALUE_MAP", userParamValueMap);

			int userAuditLogs = 0;
			try {
				userAuditLogs = details.userAuditLogs(userAuditLogsMap);
				CustomerDao customerDao = new CustomerDao();
				int login_flag_update= customerDao.setLoginStatus(login_status);
				System.out.println("login_flag_update: "+login_flag_update);

			} catch (Exception e) {
				
				e.printStackTrace();
			}
			
			
			
			response.sendRedirect(path + "/logout.jsp");
			httpSession.removeAttribute("userId");
			httpSession.invalidate();

		} else {
			response.sendRedirect(path + "/logout.jsp");
		}

		System.out.println("invalidated the session  sending to login page");

	}

}
