package com.ascent.boot.recon;

import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.recon.Recon;
import com.ascent.custumize.recon.Recons;
import com.ascent.recon.AscentAutoReconPlugin;
import com.ascent.util.PagesConstants;

public class ReconProcessController implements PagesConstants {
	/**
	 * 
	 */
	private static final long serialVersionUID = -7842397441606884847L;
	private static Logger logger = LogManager
			.getLogger(ReconProcessController.class);

	/*
	 * public static void main(String args[]) {
	 * 
	 * ReconProcessController bootIntegration = new ReconProcessController();
	 * bootIntegration.boot(); }
	 */

	public void boot() {

		ReconMetaInstance reconMetaInstance = ReconMetaInstance.getInstance();

		// meta data test
		Properties bootProps = reconMetaInstance.getBootProperties();
		Properties dbProps = reconMetaInstance.getDbProperties();
		Properties appProps = reconMetaInstance.getApplicationProperties();
		Recons recons = reconMetaInstance.getReconConfs();
		logger.trace("Properties Loaded");
		if (recons.getRecon() != null && recons.getRecon().size() > 0) {
			for (Recon rec : recons.getRecon()) {

				try {
					AscentAutoReconPlugin ascentAutoReconPlugin = (AscentAutoReconPlugin) Class
							.forName(rec.getPlugin()).newInstance();

					/*if ("ONUS_ATM_DC".equalsIgnoreCase(rec.getName())
							) {
						ascentAutoReconPlugin.process(rec);
						
					}
					*/
					
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}

	}

	public void boot(String userSelectedReconName) {

		ReconMetaInstance reconMetaInstance = ReconMetaInstance.getInstance();

		// meta data test
		Properties bootProps = reconMetaInstance.getBootProperties();
		Properties dbProps = reconMetaInstance.getDbProperties();
		Properties appProps = reconMetaInstance.getApplicationProperties();
		Recons recons = reconMetaInstance.getReconConfs();
		logger.trace("Properties Loaded");
		if (recons.getRecon() != null && recons.getRecon().size() > 0) {
			for (Recon rec : recons.getRecon()) {

				try {
					AscentAutoReconPlugin ascentAutoReconPlugin = (AscentAutoReconPlugin) Class
							.forName(rec.getPlugin()).newInstance();

					/**
					 * ON US ATM,POS, PAYROLL
					 */

					if ("ONUS_ATM_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_ATM_DEBIT_CARD)) {
							//ascentAutoReconPlugin.process(rec);
						System.out.println(" *************** "+userSelectedReconName);
						
					} else if ("ONUS_ATM_DEPOSIT".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ONUS_ATM_DEPOSITS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ONUS_POS_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_POS_DEBIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ONUS_POS_PC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_POS_PAYROLL_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ONUS_POS_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_POS_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ONUS_ATM_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_ATM_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ONUS_ATM_PC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ONUS_ATM_PAYROLL_CARD)) {
						ascentAutoReconPlugin.process(rec);
					}// ISSUER RECON PROCESS
					else if ("ISSUER_NATM_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_ATM_NAPS_DEBIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_VATM_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_ATM_VISA_DEBIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_VATM_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_ATM_VISA_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_MATM_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_ATM_MASTER_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_NPOS_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_POS_NAPS_DEBIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_VPOS_DC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_POS_VISA_DEBIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_MPOS_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_POS_MASTER_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ISSUER_VPOS_CC".equalsIgnoreCase(rec.getName())
							&& userSelectedReconName
									.equals(ISSUER_POS_VISA_CREDIT_CARD)) {
						ascentAutoReconPlugin.process(rec);
					}// ACQUIRER RECON PROCESS
					else if ("ACQUIRER_NATM_CRD"
							.equalsIgnoreCase(rec.getName())
							&& userSelectedReconName.equals(ACQUIRER_ATM_NAPS_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					}/*
					 * else if (" ".equalsIgnoreCase(rec.getName()) &&
					 * userSelectedReconName.equals(ACQUIRER_ATM_NAPS2_CARDS)) {
					 * ascentAutoReconPlugin.process(rec); //this is for
					 * acquirer  }
					 */else if ("ACQUIRER_VATM_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_ATM_VISA_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_MATM_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_ATM_MASTER_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_UATM_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_ATM_UP_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_NPOS_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_POS_NAPS_CARD)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_VPOS_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_POS_VISA_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_MPOS_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_POS_MASTER_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_UPOS_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_POS_UP_CARDS)) {
						ascentAutoReconPlugin.process(rec);
					} else if ("ACQUIRER_QPOS_CRD".equalsIgnoreCase(rec
							.getName())
							&& userSelectedReconName.equals(ACQUIRER_POS_QPAY_CARDS)) {
							ascentAutoReconPlugin.process(rec);
					} else {
						logger.trace("Not Availabe...");
					}

				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}

	}
}