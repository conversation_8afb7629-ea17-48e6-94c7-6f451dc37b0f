#Microsoft Active Directory Server

#Domain
#domain=testdomain
superadmindomain=example
bankdomain=example
merchantdomain=example
# Host: ldap://***********:636
# Local Port : 389 SSL Port: 636
NON SSL
#host=ldap://***********:389
#SSL
host=ldap://*************:389

#SearchBase
#searchbase=DC=testdomain,DC=com
searchbase=dc=maxcrc,dc=com
#Organisation 

domain=dc=maxcrc,dc=com
o=AutoRecon



#Admin User & Password

diruser=cn=Manager	
dirpassword=secret


#SSL - Port should be 636
ssl=false

keystorepath=C:\Program Files\Java\jdk1.8.0_45\jre\lib\security\\cacerts

#LDAP Server 
LDAP_SERVER_TYPE=AD
LDAP_AUTH_FLAG=N
LDAP=N
