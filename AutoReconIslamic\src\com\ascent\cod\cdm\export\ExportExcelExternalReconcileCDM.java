package com.ascent.cod.cdm.export;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExportExcelExternalReconcileCDM {
	
	//private static Logger logger = LogManager.getLogger(ExportExcelExternalSuppress.class.getName());
	
	public static void main(String[] args) throws IOException {
	//	exportExel();
	}

	public static String exportExcel4(List<Map<String, Object>> externalrecondataList) throws IOException {
	/*public static String exportExcel1(List<Map<String, Object>> unmatchList,String department) throws IOException {*/
		Date date = new Date();
		//String fileRec=recon.replace("/", "_");
		/*String fileRec=department.replace("/", "_");*/
		String fileName = String.format("CDMExternalReconcileData.xlsx", date);
		/*String fileName = String.format("kaushal_"+fileRec+".xlsx", department, date);
	*/
		String fileNameWithPath =  "D:/Dofar/COD/CDM/INTERNAL_EXTERNAL_RECONCILE/"+fileName;
		
		
		  List<Map<String,Object>> externalrecondataList1 = new ArrayList<Map<String,Object>>();
			for(Map map11 : externalrecondataList){
			Map<String, Object> dataMapList = new LinkedHashMap<String, Object>();
			
			
		    dataMapList.put("TXNMESSAGES ID",map11.get("TXNMESSAGES ID"));
		    dataMapList.put("CREATEDDATE",map11.get("CREATEDDATE"));
		    dataMapList.put("TXNDATETIME",map11.get("TXNDATETIME"));
		    dataMapList.put("TXNDATE",map11.get("TXNDATE"));
		    dataMapList.put("TXNTIME",map11.get("TXNTIME"));
		    dataMapList.put("TERMINALID",map11.get("TERMINALID"));
		    dataMapList.put("SEQUENCENUMBER",map11.get("SEQUENCENUMBER"));
			dataMapList.put("TXNTYPE",map11.get("TXNTYPE"));
			dataMapList.put("CARDNUMBER",map11.get("CARDNUMBER"));
			dataMapList.put("ACCOUNTNO1",map11.get("ACCOUNTNO1"));
			dataMapList.put("AMOUNT",map11.get("AMOUNT"));
			dataMapList.put("RECON STATUS",map11.get("RECON STATUS"));
			externalrecondataList1.add(dataMapList);
		   }
	
	   
	    Set<String> columnNamesSet = externalrecondataList1.get(0).keySet();

		String[] columnNames = columnNamesSet.stream().toArray(String[] ::new);
		
		/*System.out.println("Before: "+columnNamesSet);
		
		ArrayList<String> myList = new ArrayList<String>(columnNamesSet);
		myList.remove("PERSON");
		myList.remove("business_area");
		
		
		columnNames = myList.toArray(new String[0]);
		
		System.out.println("After: "+Arrays.toString(columnNames));
		System.out.println(myList);
		*/
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Contacts");

		Font headerFont = workbook.createFont();
		CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);
		
		/*Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.RED.getIndex());

*/
		// Create a Row
		Row headerRow = sheet.createRow(0);
		
		
		for (int i = 0; i < columnNames.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columnNames[i]); 
			cell.setCellStyle(headerCellStyle);
		}
	
		// Create Other rows and cells with contacts data
		int rowNum = 1;

		// for (Contact contact : contacts) {
		for (int i = 0; i < externalrecondataList1.size(); i++) {
			Map<String, Object> map = externalrecondataList1.get(i);
			
			int count = 0;
			Row row = sheet.createRow(rowNum++);

			for (Map.Entry<String, Object> entry : map.entrySet()) {
				
			row.createCell(count++).setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
			}
		}
	
		// Resize all columns to fit the content size
		for (int i = 0; i < columnNames.length; i++) {
			sheet.autoSizeColumn(i);
		}

		// Write the output to a file
		FileOutputStream fileOut = new FileOutputStream(fileNameWithPath);
		workbook.write(fileOut);
		fileOut.close();
	    System.out.println("export done...");
		//logger.debug("export done...");

		return fileNameWithPath;
	
		}


}
