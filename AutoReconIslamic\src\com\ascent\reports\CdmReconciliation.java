package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;
import javax.servlet.http.HttpServletRequest;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

/**
 * <AUTHOR>
 *
 */
public class CdmReconciliation {

	private static final String CDM_INTERNAL_RECONCILE_REPORT = "CDM_INTERNAL_RECONCILE_REPORT";
	private static final String CDM_INTERNAL_UNRECONCILE_REPORT = "CDM_INTERNAL_UNRECONCILE_REPORT";

	private static final String CDM_EXTERNAL_RECONCILE_REPORT = "CDM_EXTERNAL_RECONCILE_REPORT";
	private static final String CDM_EXTERNAL_UNRECONCILE_REPORT = "CDM_EXTERNAL_UNRECONCILE_REPORT";

	private static final String CDM_SUPPRESS_INTERNAL_REPORT = "CDM_SUPPRESS_INTERNAL_REPORT";
	private static final String CDM_SUPPRESS_EXTERNAL_REPORT = "CDM_SUPPRESS_EXTERNAL_REPORT";
	private static final String CDM_AGING_REPORT = "CDM_AGING_REPORT";
	
	private static final String CDM_INTERNAL_DRCR = "CDM_INTERNAL_DRCR";
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> cdmInternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_INTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CDM ID", rset.getString(2));
				map.put("CDM BRANCH", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("CDM ACCOUNT", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> cdmInternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_INTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CDM ID", rset.getString(2));
				map.put("CDM BRANCH", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("CDM ACCOUNT", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				map.put("AGE", rset.getString(30));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> cdmExternalReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_EXTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("TXNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CARDNUMBER", rset.getString(11));
				map.put("ACCOUNTNO1", rset.getString(12));
				map.put("ACCOUNTNAME", rset.getString(13));
				map.put("AMOUNT", rset.getString(14));
				map.put("NOTEDETAILS", rset.getString(15));
				map.put("CARDTAKEN", rset.getString(16));
				map.put("CARDCAPTURE", rset.getString(17));
				map.put("NOTESENCASHED", rset.getString(18));
				map.put("CASHRETRACT", rset.getString(19));
				map.put("RESPONSECODE", rset.getString(20));
				map.put("RESPONSEDESC", rset.getString(21));
				map.put("HARDWARESTATUS", rset.getString(22));
				map.put("COMMENTS", rset.getString(23));
				map.put("VERSION", rset.getString(24));
				map.put("ACTIVE INDEX", rset.getString(25));
				map.put("WORKFLOW STATUS", rset.getString(26));
				map.put("UPDATED ON", rset.getString(27));
				map.put("CREATED ON", rset.getString(28));
				map.put("RECON STATUS", rset.getString(29));
				map.put("RECON ID", rset.getString(30));
				map.put("ACTIVITY COMMENTS", rset.getString(31));
				map.put("MAIN REV IND", rset.getString(32));
				map.put("OPERATION", rset.getString(33));
				map.put("FILE NAME", rset.getString(34));
				map.put("BUSINESS AREA", rset.getString(35));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> cdmExternalUnReconcile(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_EXTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("TXNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CARDNUMBER", rset.getString(11));
				map.put("ACCOUNTNO1", rset.getString(12));
				map.put("ACCOUNTNAME", rset.getString(13));
				map.put("AMOUNT", rset.getString(14));
				map.put("NOTEDETAILS", rset.getString(15));
				map.put("CARDTAKEN", rset.getString(16));
				map.put("CARDCAPTURE", rset.getString(17));
				map.put("NOTESENCASHED", rset.getString(18));
				map.put("CASHRETRACT", rset.getString(19));
				map.put("RESPONSECODE", rset.getString(20));
				map.put("RESPONSEDESC", rset.getString(21));
				map.put("HARDWARESTATUS", rset.getString(22));
				map.put("COMMENTS", rset.getString(23));
				map.put("VERSION", rset.getString(24));
				map.put("ACTIVE INDEX", rset.getString(25));
				map.put("WORKFLOW STATUS", rset.getString(26));
				map.put("UPDATED ON", rset.getString(27));
				map.put("CREATED ON", rset.getString(28));
				if(rset.getString(29)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(29));
				map.put("RECON ID", rset.getString(30));
				map.put("ACTIVITY COMMENTS", rset.getString(31));
				map.put("MAIN REV IND", rset.getString(32));
				map.put("OPERATION'", rset.getString(33));
				map.put("FILE NAME", rset.getString(34));
				map.put("BUSINESS AREA", rset.getString(35));
				map.put("AGE", rset.getString(36));
		
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> cdmInternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_SUPPRESS_INTERNAL_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("CDM ID", rset.getString(2));
				map.put("CDM BRANCH", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("CDM ACCOUNT", rset.getString(8));
				map.put("DRCR", rset.getString(9));
				map.put("AMOUNT", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("REFERENCE NUMBER", rset.getString(12));
				map.put("TRAN REMARKS", rset.getString(13));
				map.put("TRAN CRNCY CODE", rset.getString(14));
				map.put("REF CRNCY CODE", rset.getString(15));
				map.put("REF AMT", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> cdmExternalSuppress(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_SUPPRESS_EXTERNAL_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TXNMESSAGES ID", rset.getString(2));
				map.put("CREATEDDATE", rset.getString(3));
				map.put("TXNDATETIME", rset.getString(4));
				map.put("TXNDATE", rset.getString(5));
				map.put("TXNTIME", rset.getString(6));
				map.put("TERMINALID", rset.getString(7));
				map.put("SEQUENCENUMBER", rset.getString(8));
				map.put("TXNTYPE ID", rset.getString(9));
				map.put("TXNTYPE", rset.getString(10));
				map.put("CARDNUMBER", rset.getString(11));
				map.put("ACCOUNTNO1", rset.getString(12));
				map.put("ACCOUNTNAME", rset.getString(13));
				map.put("AMOUNT", rset.getString(14));
				map.put("NOTEDETAILS", rset.getString(15));
				map.put("CARDTAKEN", rset.getString(16));
				map.put("CARDCAPTURE", rset.getString(17));
				map.put("NOTESENCASHED", rset.getString(18));
				map.put("CASHRETRACT", rset.getString(19));
				map.put("RESPONSECODE", rset.getString(20));
				map.put("RESPONSEDESC", rset.getString(21));
				map.put("HARDWARESTATUS", rset.getString(22));
				map.put("COMMENTS", rset.getString(23));
				map.put("VERSION", rset.getString(24));
				map.put("ACTIVE INDEX", rset.getString(25));
				map.put("WORKFLOW STATUS", rset.getString(26));
				map.put("UPDATED ON", rset.getString(27));
				map.put("CREATED ON", rset.getString(28));
				map.put("RECON STATUS", rset.getString(29));
				map.put("RECON ID", rset.getString(30));
				map.put("ACTIVITY COMMENTS", rset.getString(31));
				map.put("MAIN REV IND", rset.getString(32));
				map.put("OPERATION'", rset.getString(33));
				map.put("FILE NAME", rset.getString(34));
				map.put("BUSINESS AREA", rset.getString(35));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return list;
	}
	public List<Map<String, Object>> CdmAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_AGING_REPORT);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> cdmInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CDM_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	

	public static void main(String[] args) {
		CdmReconciliation c = new CdmReconciliation();
		c.cdmInternalReconcile("2018-01-01", "2018-10-01");
		c.cdmInternalUnReconcile("2018-01-01", "2018-10-01");
		c.cdmExternalReconcile("2018-01-01", "2018-10-01");
		c.cdmExternalUnReconcile("2018-01-01", "2018-10-01");
		c.cdmInternalSuppress("2018-01-01", "2018-10-01");
		c.cdmExternalSuppress("2018-01-01", "2018-10-01");
		c.cdmInternalDrcr("2018-01-01", "2018-10-01");
		c.CdmAgingMethod();
	}

}
