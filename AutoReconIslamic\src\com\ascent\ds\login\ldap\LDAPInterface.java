package com.ascent.ds.login.ldap;


import java.util.Map;

/*******************************************************************************
 * This class is created for authenticating the Directory Server using LDAP
 * protocol
 * 
 * <AUTHOR>
 * 
 */
public interface LDAPInterface {

	public boolean authenticateUser(String userName, String password);

	public boolean authenticateUser(String userName, String password,
			String domain);

	@SuppressWarnings("unchecked")
	public Map getUsersList(String groupName);

	@SuppressWarnings("unchecked")
	public Map getUsersList(String domain, String groupName);

	public boolean userModifyAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName, String domain);

	public boolean modifyUser(String userName, String mailID,
			String phoneNumber, String domain);

	public boolean checkUserExist(String userName, String domain);
	
	public boolean createUser(String userName, String mailID, String phoneNumber);

	public boolean createUser(String userName, String mailID,
			String phoneNumber, String domain);

	public boolean userCreateAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName);

	public boolean userCreateAddGroup(String userName, String password,
			String mailID, String phoneNumber, String groupName, String domain);

	public boolean userCreateWithPassword(String userName, String mailID,
			String password, String phoneNumber);

	public boolean userCreateWithPassword(String userName, String mailID,
			String password, String phoneNumber, String domain);
	
	public boolean removeUser(String userName, String domain);

	public boolean removeUser(String userName);

	
	
	public boolean createGroup(String groupName);

	public boolean createGroup(String groupName, String domain);

	public boolean removeGroup(String groupName, String domain);

	public boolean removeGroup(String groupName);

	

	public boolean changePassword(String userName, String oldPassword,
			String newPassword);

	public boolean changePassword(String userName, String oldPassword,
			String newPassword, String domain);
	
	public boolean changePassword(String userName, String newPassword);
	
	public boolean forcechangePassword(String userName, 
			String newPassword, String domain);

	
}
