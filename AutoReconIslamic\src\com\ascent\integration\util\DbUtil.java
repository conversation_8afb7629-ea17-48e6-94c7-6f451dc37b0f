package com.ascent.integration.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.banknizwa.metainstance.EtlMetaInstance;



public class DbUtil {
	private static Logger logger = LogManager.getLogger(DbUtil.class.getName());

	public static void closeConnection(Connection connection) {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			logger.error("Connection Close Error", e);
		}
	}

	public static void closePreparedStatement(PreparedStatement preparedStatement) {
		try {
			if (preparedStatement != null && !preparedStatement.isClosed()) {
				preparedStatement.close();
			}
		} catch (Exception e) {
			logger.error("PreparedStatement Close Error", e);
		}
	}

	public static void closeResultSet(ResultSet resultSet) {
		try {
			if (resultSet != null && !resultSet.isClosed()) {
				resultSet.close();
			}
		} catch (Exception e) {
			logger.error("ResultSet Close Error", e);
		} catch (Throwable e) {
			logger.error("ResultSet Close Error", e);
		}
	}

	public static Connection getConnection() {
		Connection connection = null;
		try {
			Properties properties = EtlMetaInstance.getInstance().getDbProperties();

			Class.forName((String) properties.getProperty("driver"));
			String url = (String) properties.getProperty("url");
			/*String user = (String) properties.getProperty("username");
			String pwd = (String) properties.getProperty("password");*/
			
			connection = DriverManager.getConnection(url);
			//logger.trace("SQL AutoRecon Islamic Database Connected Successfully...");

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("ERROR", e);
		}

		return connection;
	}

	public static String getMsSqlDateString(String dateString, String orgformat) {
		Date temp = null;
		String tempStr = null;
		try {
			temp = (new SimpleDateFormat(orgformat)).parse(dateString);
			tempStr = (new SimpleDateFormat("yyyy-MM-dd")).format(temp);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage(),e);
		}

		return tempStr;
	}

	public static String getMsSqlDateTimeString(String dateString, String orgformat) {
		Date temp = null;
		String tempStr = null;
		try {
			temp = (new SimpleDateFormat(orgformat)).parse(dateString);
			tempStr = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")).format(temp);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			logger.error(e.getMessage(),e);
		}

		return tempStr;
	}
}
