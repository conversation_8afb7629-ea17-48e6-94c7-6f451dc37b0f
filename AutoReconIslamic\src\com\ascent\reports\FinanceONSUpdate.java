package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class FinanceONSUpdate {

	//private static Logger logger = LogManager.getLogger(FinanceONSUpdate.class.getName());

	private static final String ONS_INTERNAL_RECON = "ONS_INTERNAL_RECON";
	private static final String ONS_EXTERNAL_RECON = "ONS_EXTERNAL_RECON";

	private static final String ONS_INTERNAL_UNRECON = "ONS_INTERNAL_UNRECON";
	private static final String ONS_EXTERNAL_UNRECON = "ONS_EXTERNAL_UNRECON";

	private static final String ONS_INTERNAL_SUPPRESS_UPDATE_RECON = "ONS_INTERNAL_SUPPRESS_UPDATE_RECON";
	private static final String ONS_EXTERNAL_SUPPRESS_UPDATE_RECON = "ONS_EXTERNAL_SUPPRESS_UPDATE_RECON";

	private static final String ONS_AGING_UPDATE_RECON = "ONS_AGING_UPDATE_RECON";

	private static final String ONS_INTERNAL_DRCR = "ONS_INTERNAL_DRCR";
	private static final String ONS_EXTERNAL_DRCR = "ONS_EXTERNAL_DRCR";

	private static final String ONS_INTERNAL_ESCALATION = "ONS_INTERNAL_ESCALATION";
	private static final String ONS_EXTERNAL_ESCALATION = "ONS_EXTERNAL_ESCALATION";

	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs) throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		while (rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));
			}
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}

	public List<Map<String, Object>> OnsInternalReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("INTERNAL REF NUM", rset.getString(2));
				map.put("ACNT", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("AMOUNT", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("CURRENCY", rset.getString(8));
				map.put("CARD NUMBER", rset.getString(9));
				map.put("ACCT BRANCH ID", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN ENTRY USER", rset.getString(13));
				map.put("TRAN POSTED USER", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE INDEX", rset.getString(17));
				map.put("WORKFLOW STATUS", rset.getString(18));
				map.put("UPDATED ON", rset.getString(19));
				map.put("CREATED ON", rset.getString(20));
				map.put("RECON STATUS", rset.getString(21));
				map.put("RECON ID", rset.getString(22));
				map.put("ACTIVITY COMMENTS", rset.getString(23));
				map.put("MAIN REV IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE NAME", rset.getString(26));
				map.put("BUSINESS AREA", rset.getString(27));
				map.put("TRAN REF NUM", rset.getString(28));
				map.put("CASE ID", rset.getString(29));
				map.put("STATUS", rset.getString(30));
				map.put("ENTRY_TYPE", rset.getString(31));
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> OnsExternalReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TRAN REF NUM", rset.getString(2));
				map.put("INTERNAL REF NUM", rset.getString(3));
				map.put("ACCOUNT", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("AMOUNT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("CURRENCY", rset.getString(9));
				map.put("PAN NUMBER", rset.getString(10));
				map.put("ORIGINATOR BID", rset.getString(11));
				map.put("DESTINATION BID", rset.getString(12));
				map.put("ACQUIRING INSTITUTION ID", rset.getString(13));
				map.put("CARD ACCEPTOR NAME", rset.getString(14));
				map.put("MERCHANT CATEGORY CODE", rset.getString(15));
				map.put("TRANSACTION TYPE", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				map.put("CASE ID", rset.getString(30));
				map.put("STATUS", rset.getString(31));
				map.put("ENTRY_TYPE", rset.getString(32));
				list.add(map);
				// logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> OnsInternalUnReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_UNRECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("INTERNAL REF NUM", rset.getString(2));
				map.put("ACNT", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("AMOUNT", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("CURRENCY", rset.getString(8));
				map.put("CARD NUMBER", rset.getString(9));
				map.put("ACCT BRANCH ID", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN ENTRY USER", rset.getString(13));
				map.put("TRAN POSTED USER", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE INDEX", rset.getString(17));
				map.put("WORKFLOW STATUS", rset.getString(18));
				map.put("UPDATED ON", rset.getString(19));
				map.put("CREATED ON", rset.getString(20));
				map.put("RECON STATUS", rset.getString(21));
				map.put("RECON ID", rset.getString(22));
				map.put("ACTIVITY COMMENTS", rset.getString(23));
				map.put("MAIN REV IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE NAME", rset.getString(26));
				map.put("BUSINESS AREA", rset.getString(27));
				map.put("TRAN REF NUM", rset.getString(28));
				map.put("AGE", rset.getString(29));
				map.put("CASE ID", rset.getString(30));
				map.put("STATUS", rset.getString(31));
				map.put("ENTRY_TYPE", rset.getString(32));
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> OnsExternalUnReconsiledUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_UNRECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TRAN REF NUM", rset.getString(2));
				map.put("INTERNAL REF NUM", rset.getString(3));
				map.put("ACCOUNT", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("AMOUNT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("CURRENCY", rset.getString(9));
				map.put("PAN NUMBER", rset.getString(10));
				map.put("ORIGINATOR BID", rset.getString(11));
				map.put("DESTINATION BID", rset.getString(12));
				map.put("ACQUIRING INSTITUTION ID", rset.getString(13));
				map.put("CARD ACCEPTOR NAME", rset.getString(14));
				map.put("MERCHANT CATEGORY CODE", rset.getString(15));
				map.put("TRANSACTION TYPE", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				if(rset.getString(23)==null)
					map.put("RECON STATUS", "AU");
				else
					map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));
				map.put("AGE", rset.getString(30));
				map.put("CASE ID", rset.getString(31));
				map.put("STATUS", rset.getString(32));
				map.put("ENTRY_TYPE", rset.getString(33));
				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> OnsInternalSuppressUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_SUPPRESS_UPDATE_RECON );
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("INTERNAL REF NUM", rset.getString(2));
				map.put("ACNT", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("AMOUNT", rset.getString(6));
				map.put("DRCR", rset.getString(7));
				map.put("CURRENCY", rset.getString(8));
				map.put("CARD NUMBER", rset.getString(9));
				map.put("ACCT BRANCH ID", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN ENTRY USER", rset.getString(13));
				map.put("TRAN POSTED USER", rset.getString(14));
				map.put("COMMENTS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("ACTIVE INDEX", rset.getString(17));
				map.put("WORKFLOW STATUS", rset.getString(18));
				map.put("UPDATED ON", rset.getString(19));
				map.put("CREATED ON", rset.getString(20));
				map.put("RECON STATUS", rset.getString(21));
				map.put("RECON ID", rset.getString(22));
				map.put("ACTIVITY COMMENTS", rset.getString(23));
				map.put("MAIN REV IND", rset.getString(24));
				map.put("OPERATION", rset.getString(25));
				map.put("FILE NAME", rset.getString(26));
				map.put("BUSINESS AREA", rset.getString(27));
				map.put("TRAN REF NUM", rset.getString(28));
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> OnsExternalSuppressUpdateMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_SUPPRESS_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("TRAN REF NUM", rset.getString(2));
				map.put("INTERNAL REF NUM", rset.getString(3));
				map.put("ACCOUNT", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("AMOUNT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("CURRENCY", rset.getString(9));
				map.put("PAN NUMBER", rset.getString(10));
				map.put("ORIGINATOR BID", rset.getString(11));
				map.put("DESTINATION BID", rset.getString(12));
				map.put("ACQUIRING INSTITUTION ID", rset.getString(13));
				map.put("CARD ACCEPTOR NAME", rset.getString(14));
				map.put("MERCHANT CATEGORY CODE", rset.getString(15));
				map.put("TRANSACTION TYPE", rset.getString(16));
				map.put("COMMENTS", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVITY COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPERATION", rset.getString(27));
				map.put("FILE NAME", rset.getString(28));
				map.put("BUSINESS AREA", rset.getString(29));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> OnsAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_AGING_UPDATE_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);

			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();

				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				if(rset.getString(3)==null)
					map.put("TOTAL AMOUNT", 0);
				else
					map.put("TOTAL AMOUNT", rset.getString(3));
				map.put("TOTAL_TRANS_0_3", rset.getString(4));

				if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

				map.put("TOTAL_TRANS_4_6", rset.getString(6));

				if(rset.getString(7)==null)
					map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));

				map.put("TOTAL_TRANS_11_15", rset.getString(8));

				if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));

				map.put("TOTAL_TRANS_16_30", rset.getString(10));

				if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));

				map.put("TOTAL_TRANS_31_60", rset.getString(12));

				if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				else
					map.put("TOTAL_AMOUNT_31_60", rset.getString(13));

				map.put("TOTAL_TRANS_61_90", rset.getString(14));

				if(rset.getString(15)==null)
					map.put("TOTAL_AMOUNT_61_90", 0);
				else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));

				map.put("TOTAL_TRANS_181_365", rset.getString(16));

				if(rset.getString(17)==null)
					map.put("TOTAL_AMOUNT_181_365", 0);
				else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> onsInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {


				Map<String, Object> map = new HashMap<String, Object>();

				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				if(rset.getString(3)==null)
					map.put("AMOUNT", 0);
				else
					map.put("AMOUNT", rset.getString(3));
				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> onsExternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {


				Map<String, Object> map = new HashMap<String, Object>();

				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				if(rset.getString(3)==null)
					map.put("AMOUNT", 0);
				else
					map.put("AMOUNT", rset.getString(3));
				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> onsIntranalEscalation() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection connection = DbUtil.getConnection();
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_ESCALATION );
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}

	public List<Map<String, Object>> onsExternalEscaltion() { 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Connection connection = DbUtil.getConnection();
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_ESCALATION);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			ResultSet rset = pstmt.executeQuery();
			list = getRsNextBatch(rset);
		} catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			DbUtil.closeConnection(connection);
		}

		return list;
	}


	public static void main(String[] args) {
		FinanceONSUpdate c = new FinanceONSUpdate();
		c.OnsInternalReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.OnsInternalUnReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.OnsExternalReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.OnsExternalUnReconsiledUpdateMethod("2018-01-01", "2018-10-01");
		c.onsInternalDrcr("2018-01-01", "2018-10-01");
		c.onsExternalDrcr("2018-01-01", "2018-10-01");
		c.OnsAgingMethod();




	}

}
