package com.ascent.ds.operations;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;

import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 *
 */

public class CaseMailTrigger {

	private static Logger logger = LogManager.getLogger(CaseMailTrigger.class.getName());

	public static void main(String[] args) {

		try {
			Map<String, String> map = new HashMap<String, String>();
			map.put("subject", "");map.put("content", "case created");map.put("toMail", "<EMAIL>;<EMAIL>");
			new CaseMailTrigger().sendMail(map);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean sendMail(Map<String, String> map) throws Exception {
		boolean flag = false;
		Properties prop = new Properties();
		Properties properties = System.getProperties();
		
		String propFileName = "mail.properties";
		InputStream fileextraction = getClass().getClassLoader().getResourceAsStream(propFileName);
		prop.load(fileextraction);

		String username = prop.getProperty("fromUserName");
		String password = prop.getProperty("fromUserPassword");
		String SMTPHost = prop.getProperty("SMTPHost");
		String SMTPPort = prop.getProperty("SMTPPort");
		
		// Setup mail server
		properties.setProperty("mail.smtp.host", SMTPHost);
		properties.setProperty("mail.smtp.auth", "true");
		//properties.setProperty("mail.smtp.starttls.enable", "true");
		//properties.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");
		properties.setProperty("mail.smtp.port", SMTPPort);

		Session session = Session.getInstance(properties, new javax.mail.Authenticator() {
			protected PasswordAuthentication getPasswordAuthentication() {
				return new PasswordAuthentication(username, password);
			}
		});
		
		try {
			// Create a default MimeMessage object.
			MimeMessage message = new MimeMessage(session);

			// Set From: header field of the header.
			message.setFrom(new InternetAddress(username));
			logger.debug("case from : "+username);
			//System.out.println("from  :  " + from);

			// Set To: header field of the header.
			message.addRecipients(Message.RecipientType.TO, InternetAddress.parse(map.get("toMail").replace(";", ",")));
			
			message.addRecipients(Message.RecipientType.CC, InternetAddress.parse(map.get("ccMail")));

			message.setSubject(map.get("subject"));
			message.setContent("modal.html","text/html" );
			message.setText("Case Created.....!");
			message.setContent(map.get("content"), "text/html");

			
			// Send message
			Transport.send(message);
			flag = true;

			//System.out.println("MESSAGE SEND successfully....");
			logger.debug("Mail Send successfully....");
		} catch (MessagingException mex) {
			flag = false;
			logger.info(" mailForCaseRecordsFOLLOWUP()   WHILE SENDING MAIL GIVING PROBLEM    " + mex);
			mex.printStackTrace();
		}
		return flag;
	}
	
	public void setContentMailTrigger(List<Map<String, Object>> selectedRecords,Map<String, String> mailContent){
		
		String content = mailContent.get("content");
		content += "<div><table border='1' style='border-collapse: collapse;font-size: 14px;'> <tr><thead>";
		String value = "<tr>";
		
		for (Map<String, Object> map : selectedRecords) {
			
			removeUnnecessaryFields(map);
		
			for (Entry<String, Object> entry : map.entrySet()) {

				content = content + "<th align='center'><b>"+entry.getKey()+"</b></th>";
				value = value + "<td align='center'>"+entry.getValue()+"</td>";
						
			}
		}
		
		content += "</thead></tr>";
		content += value+"</tr>";
		content += "</table></div></center><br/><br>"+"<b>Comments : </b>"+mailContent.get("comment")+"<br><br>Regards,<br>AutoRecon";
		
		mailContent.put("content", content);
		
		try {
			sendMail(mailContent);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private void removeUnnecessaryFields(Map<String, Object> map) {

		map.remove("SID");
		map.remove("COMMENTS");
		map.remove("VERSION");
		map.remove("ACTIVE_INDEX");
		map.remove("WORKFLOW_STATUS");
		map.remove("UPDATED_ON");
		map.remove("CREATED_ON");
		map.remove("RECON_STATUS");
		map.remove("RECON_ID");
		map.remove("ACTIVITY_COMMENTS");
		map.remove("MAIN_REV_IND");
		map.remove("OPERATION");
		map.remove("FILE_NAME");
		map.remove("MATCH_TYPE");
		map.remove("rowID");
		map.remove("_embeddedComponents_reconSummaryListGrid");
		
		if(map.containsKey("TRA_DATE")) {
			map.put("TRAN_DATE", new SimpleDateFormat("dd-MM-yyyy").format(map.get("TRA_DATE")));
			map.remove("TRA_DATE");
		}
		if(map.containsKey("TRA_AMT")) {
			map.put("TRAN_AMT", map.get("TRA_AMT"));
			map.remove("TRA_AMT");
		}
		if(map.containsKey("TRA_CUR")) {
			map.put("TRAN_CUR", map.get("TRA_CUR"));
			map.remove("TRA_CUR");
		}
		if(map.containsKey("DEB_CRE_IND")) {
			map.put("DR_CR_IND", map.get("DEB_CRE_IND"));
			map.remove("DEB_CRE_IND");
		}
		if(map.containsKey("INTERNAL_REF_NUM")) {
			map.put("INT_REF_NUM", map.get("INTERNAL_REF_NUM"));
			map.remove("INTERNAL_REF_NUM");
		}		
		
		Set<String> set = new HashSet<String>();
		for (String key : map.keySet()) {
			if(key.contains("_selection"))
				set.add(key);
			else if(key.contains("_embedded"))
				set.add(key);
		}
		map.keySet().removeAll(set);
		
	}

}
