package com.ascent.integration.validation;

import java.util.Map;

public class AscentPreEnrichValidationPlugin implements AscentValidationPlugin {

	public AscentPreEnrichValidationPlugin() {

	}

	@Override
	public Boolean validate(Map<String, Object> args) {
		boolean flag = true;
		args.put("ex", false);
		StringBuilder comments = new StringBuilder();
		if (!"Stagging".equalsIgnoreCase((String) args.get("COMMENTS"))) {
			comments.append((String) args.get("COMMENTS"));
		}

		args.put("COMMENTS", comments.toString());
		return flag;
	}

}
