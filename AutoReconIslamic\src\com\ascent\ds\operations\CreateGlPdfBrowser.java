package com.ascent.ds.operations;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.servlet.http.HttpServletResponse;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.util.AscentAutoReconConstants;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.log.Logger;
import com.isomorphic.rpc.RPCManager;

public class CreateGlPdfBrowser {
	private static Logger log = new Logger(DSRequest.class.getName());
	 static String extPath;
	
	public CreateGlPdfBrowser() {
	System.out.println("CreateGlPdfBrowser class called");
	/*	EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
	  extPath = (String) appProps.get(AscentAutoReconConstants.GENRATE_GL_ENTRY_PDF);*/
		//String img_path = (String)appProps.get(AscentAutoReconConstants.DOHA_LOGO);
	}
	static {
		
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
	  extPath = (String) appProps.get(AscentAutoReconConstants.GENRATE_GL_ENTRY_PDF);
	} 
	public static void customExport(RPCManager rpc, HttpServletResponse response, DSRequest request)throws Exception
	{
		System.out.println("custom export method");
		Map dataMap=request.getCriteria();
		System.out.println("data map : "+dataMap);
		String fileName=(String) dataMap.get("fileName");
		System.out.println("file name : "+fileName);
		try{
			rpc.doCustomResponse();
			response.setHeader("content-disposition", "attachment; filename="+fileName );
			response.setContentType("application/pdf");
			System.out.println(extPath+"******************  ");
			File f = new File(extPath+"\\"+fileName);
			System.out.println(f);
			OutputStream os = response.getOutputStream();
	        byte[] buf = new byte[8192];
	        InputStream is = new FileInputStream(f);
	        int c = 0;
	        while ((c = is.read(buf, 0, buf.length)) > 0) {
	            os.write(buf, 0, c);
	            os.flush();
	        }
	        os.close();
	        is.close();
			
		}catch(Exception e)
		{
			e.printStackTrace();
		}
		return;
	}
}
