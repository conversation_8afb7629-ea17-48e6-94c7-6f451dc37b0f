package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

/**
 * Kaushal 
 */
public class ManualExternalEntries  extends BasicDataSource implements PagesConstants{
	
	private static final long serialVersionUID = 1L;
	public static final String ShowExternalDataDS = "ShowExternalDataDS";
	// public static final String ManualEntriesDS = "ManualEntriesDS";
	public static final String ManualEntries = "ManualEntries";
	private static final String FIN_ONS_CBO_STG_INSERT = "FIN_ONS_CBO_STG_INSERT";
	static AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();
	
	public DSResponse executeFetch(final DSRequest request) throws Exception {

		DSResponse dsResponse = new DSResponse();
		Map requestParams = request.getCriteria();
		
		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) requestParams.get("data");
		
		String reconTableName = (String) requestParams.get("reconTableName");
		String integrationName = (String) requestParams.get("integrationName");
		String action = (String) requestParams.get("action");
		String comments = (String) requestParams.get("comments");
	
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");

		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");
       
		Map<String, Object> paramsMap = new HashMap<String, Object>();

		paramsMap.put(USER_ID, user);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(ACTION, action);
		paramsMap.put(COMMENTS, comments);
		//paramsMap.put(MODULE, ManualEntries);
		paramsMap.put(DS_NAME, ShowExternalDataDS);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		createManualEntriesActivity(paramsMap);
		return dsResponse;
		
	}


	public static void createManualEntriesActivity(Map<String, Object> paramsMap) {
		Connection connection = null;
		try {
			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			// userAdminManager.getUsercontroller().getUsers().getUser(userId);
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			connection = DbUtil.getConnection();
			String activityStatus = PENDING_APPROVAL;
			 paramsMap.put(PERSIST_CLASS, EXTERNAL_MANUALENTRIES_PLUGIN_CLASS_NAME);

			User user = (User) paramsMap.get(USER_ID);
			paramsMap.remove(USER_ID);
			activityDataMap.put("activity_data", paramsMap);
			userAdminManager.createActivity(connection, user, paramsMap.get(BUSINES_AREA).toString(),
					paramsMap.get(RECON_NAME).toString(),"", EXTERNAL_MANUAL_OPERATION, activityDataMap,
					activityStatus, paramsMap.get(COMMENTS).toString() );

			persist(activityDataMap, APPROVED, connection);

		} catch (Exception e) {
			e.printStackTrace();
		}

	}
	public static List<Map<String, Object>> persist(Map<String, Object> activityDataMap, String Status,
			Connection connection) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection1 = DbUtil.getConnection();
		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator = new LoadRegulator();
		InsertRegulator insertRegulator = new InsertRegulator();
		Map activityRecordsMap = (Map) activityDataMap.get("activity_data");
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		try {
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

			Queries queries = ascentWebMetaInstance.getWebQueryConfs();
			Query queryConf = queries.getQueryConf(FIN_ONS_CBO_STG_INSERT);
			 //Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf("MANUAL_INSERT_QUERY");
			String query = queryConf.getQueryString();

			for (Map<String, Object> record : records) {
				PreparedStatement pstmt = connection1.prepareStatement(query);

				Map<String, Object> args = new HashMap<String, Object>();
				// SimpleDateFormat formatter= new SimpleDateFormat("yyyy-MM-dd
				// 'at' HH:mm:ss z");
				String trnDate = (String) record.get("TRAN_DATE");
				String valDate = (String) record.get("VALUE_DATE");
				@SuppressWarnings("deprecation")
				Date txnDate = new Date(trnDate);
				@SuppressWarnings("deprecation")
				Date valueDate = new Date(trnDate);
				record.put("TRAN_DATE", txnDate);
				record.put("VALUE_DATE", valueDate);

				  Long numberOfRows=0L;
				PreparedStatement pstmt12 = connection1.prepareStatement("select max(sid) from FIN_ONS_CBO_STG ");
				 ResultSet rs = pstmt12.executeQuery();
			      if (rs.next()) {
			         numberOfRows = rs.getLong(1);
			      } else {
			      }
				
				// aditional columns
				record.put("SID", numberOfRows+1);
				record.put("FREE_FIELD_2", null);
				record.put("FREE_FIELD_3", null);
				record.put("FREE_FIELD_4", null);
				record.put("FREE_DATE_1", null);
				record.put("FREE_DATE_2", null);
				record.put("COMMENTS", "Staging");
				record.put("VERSION", 1);
				record.put("ACTIVE_INDEX", "Y");
				record.put("WORKFLOW_STATUS", "N");
				record.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				record.put("CREATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
				record.put("RECON_STATUS", null);
				record.put("RECON_ID", null);
				record.put("ACTIVITY_COMMENTS", null);
				record.put("MAIN_REV_IND", "MANUAL");
				record.put("OPERATION", null);
				record.put("FILE_NAME", "MANUAL_ENTRY");
				record.put("BUSINESS_AREA", null);
				record.put("FREE_FIELD_1", null);
				record.put("FREE_FIELD_2", null);
				record.put("FREE_FIELD_3", null);
				record.put("FREE_FIELD_4", null);
				record.put("FREE_FIELD_5", null);
				record.put("FREE_FIELD_6", null);
				record.put("FREE_FIELD_7", null);
				record.put("FREE_FIELD_8", null);
				record.put("FREE_FIELD_9", null);
				record.put("FREE_FIELD_10", null);
				record.put("FREE_CODE_1", null);
				record.put("FREE_CODE_2", null);
				record.put("FREE_CODE_3", null);
				record.put("FREE_CODE_4", null);
				record.put("FREE_CODE_5", null);
				record.put("FREE_DATE_1", null);
				record.put("FREE_DATE_2", null);
				record.put("FREE_DATE_3", null);
				record.put("FREE_DATE_4", null);
				record.put("FREE_DATE_5", null);
				// END

				args.put("INSERT_QRY", query);
				args.put("INSERT_QRY_PARAMS", queryConf.getQueryParam());
				args.put("PARAM_VALUE_MAP", record);

				// Activity

				new InsertRegulator().insert(pstmt, args, queryConf.getQueryParam());

			}
		} catch (Exception e) {
			e.printStackTrace();

		} finally {
			try {
				connection1.close();
			} catch (Exception e2) {
				e2.printStackTrace();
				// TODO: handle exception
			}
		}

		return list;
	}



}
