package com.ascent.boot.etl;

import java.io.File;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.integration.Integrations;
import com.ascent.custumize.query.Queries;
import com.ascent.util.AscentAutoReconConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ETLProcessController extends BasicDataSource{
	private static Logger logger = LogManager.getLogger(ETLProcessController.class);

	public DSResponse executeFetch(final DSRequest request)
	{
		DSResponse response=new DSResponse();
		System.out.println("method is called");
		Map requestMap=request.getValues();
		String integrationName=(String) requestMap.get("integrationName");
		System.out.println("integrationName : "+integrationName);
		boot(integrationName);
		return response;
	}

	public void boot(String integrationName) {

		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();

		// meta data test
		Properties bootProps = etlMetaInstance.getBootProperties();
		Properties dbProps = etlMetaInstance.getDbProperties();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		Integrations integrations = etlMetaInstance.getEtlConfs();
		Queries etlQueryConfs = etlMetaInstance.getEtlQueryConfs();

		/////
		System.out.println("Properties Loaded");
		logger.trace("Properties Loaded");
		String extPath = (String) appProps.get(AscentAutoReconConstants.AUTO_RECON_HOME)
				+ (String) appProps.get(AscentAutoReconConstants.SFTP_FOLDER_NAME);

		try {

			File sftpFolder = new File(extPath);
			if(integrationName.equalsIgnoreCase("IRIS"))
			{
				/*IrisIntegration irisIntegration = new IrisIntegration(integrationName);
				irisIntegration.processFile(sftpFolder); */
			}else if(integrationName.equalsIgnoreCase("AUTH_ACQUIRER"))
			{
				
			}else if(integrationName.equalsIgnoreCase("BATCHES"))
			{
				
				
			}else if(integrationName.equalsIgnoreCase("MTXNS"))
			{
				
			}else if(integrationName.equalsIgnoreCase("MISO"))
			{
				
			}else if(integrationName.equalsIgnoreCase("QPAY"))
			{
				
			}else if(integrationName.equalsIgnoreCase("CTXNS"))
			{
				
			}else if(integrationName.equalsIgnoreCase("CISO"))
			{
				
			}else if(integrationName.equalsIgnoreCase("VISA"))
			{
				
			}else if(integrationName.equalsIgnoreCase("MASTER"))
			{
				
			}else if(integrationName.equalsIgnoreCase("GL_1002"))
			{
				
			}
			
			
			
			/*BatchesIntegration batchesUpdateIntegration = new BatchesIntegration("BATCHES");
			batchesUpdateIntegration.processFile(sftpFolder);
			CTxnsIntegration ctxn = new CTxnsIntegration("CTXNS");
			ctxn.processFile(sftpFolder);
		

			CisoIntegration cisoParse = new CisoIntegration("CISO");
			cisoParse.processFile(sftpFolder);
					
			UnionPayIntegration unionPay = new UnionPayIntegration("UnionPay");
			unionPay.processFile(sftpFolder);
							
			
			 
			 GLIntegration glParser2279 = new GLIntegration("GL_2279");
			glParser2279.processFile(sftpFolder);
			  
			  GLIntegration glParser2247 = new GLIntegration("GL_2247");
			glParser2247.processFile(sftpFolder);
			  
			
			 GLIntegration glParser1002 = new GLIntegration("GL_1002");
				glParser1002.processFile(sftpFolder);
					
			 // GL_1006
			GLIntegration glParser1006 = new GLIntegration("GL_1006");
			glParser1006.processFile(sftpFolder);
			 
	
			MisoIntegration misoParser = new MisoIntegration("MISO");
			misoParser.processFile(sftpFolder);

			

			GLIntegration glParser1016 = new GLIntegration("GL_1016");
			glParser1016.processFile(sftpFolder);

			
			GLIntegration glParser1472 = new GLIntegration("GL_1472");
			glParser1472.processFile(sftpFolder); 

		GLIntegration glParser1482 = new GLIntegration("GL_1482");
			glParser1482.processFile(sftpFolder);

			
			GLIntegration glParser1015 = new GLIntegration("GL_1015");
			glParser1015.processFile(sftpFolder);
			

			QpayIntegration qpayIntegration = new QpayIntegration("QPAY");
			qpayIntegration.processFile(sftpFolder);

				
		MasterCardIntegration masterIntegration = new MasterCardIntegration("MASTER");
			masterIntegration.processFile(sftpFolder);	
			

			MTxnsIntegration mtxnParser = new MTxnsIntegration("MTXNS");
			mtxnParser.processFile(sftpFolder);

			AuthAcqIntegration authAcqIntegration = new AuthAcqIntegration("AUTH_ACQUIRER");
			authAcqIntegration.processFile(sftpFolder);
			
			QcbIntegration qcbIntegration = new QcbIntegration("QCB");
			qcbIntegration.processFile(sftpFolder);

			
			
			AuthIssIntegration authIssIntegration = new AuthIssIntegration("AUTH_ISSUER");
			authIssIntegration.processFile(sftpFolder);	
			*/
			/*VisaSmsParser visa = new VisaSmsParser("VISA");
			visa.processFile(sftpFolder);
			
			*/
			
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
}