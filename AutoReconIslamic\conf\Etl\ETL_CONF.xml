
<Integrations>
	<Integration id="1">
		<name>ATM</name>
		<fileType>csv</fileType>
		<fileNamePattern>(ATM GL.csv)|(CDM GL.csv)</fileNamePattern>
		<sequenceName>switch_iris_seq</sequenceName>
		<columnNamesString>
		BRANCH_CODE,CURRENCY_CODE,GL_CODE,CIF_SUB_NO,SL_NO,OP_NO,
			LINE_NO,TRANSACTION_DATE,VALUE_DATE,DESCRIPTION,AMOUNT,DECIMAL_POINTS,BRIEF_NAME_ENG,
			SHORT_NAME_ENG,YTD_FC_BALANCE,YTD_CV_BALANCE,CV_AVAIL_BALANCE,FC_AVAIL_BALANCE,CURRENCY_NAME
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>IRIS_INSERT_STG</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>IRIS_INSERT_STG_EX</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		 <enrichmentPlugin>com.ascent.integration.sources.iris.SwitchIrisEnrichment</enrichmentPlugin> 
		 <preEnrichValidationPlugin>com.ascent.integration.sources.iris.SwitchIrisPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.iris.SwitchIrisPostEnrichValidationPlugin</postEnrichValidationPlugin> 
		<persistancePlugin>com.ascent.integration.sources.iris.SwitchPersistancePlugin</persistancePlugin>
	</Integration>


<Integration id="2">
		<name>EJ</name>
		<fileType>txt</fileType>
		<fileNamePattern>(EJ______cdm.361)</fileNamePattern>
		<sequenceName>EJ_seq</sequenceName>
		<columnNamesString>
		SID,CURRENCY,TRA_TIME,STAN,TXN_TYPE,TRA_DATE,ATM_ID,RRN,RM1,RM5,RM10,RM20,RM50,SEQ,PAN,AMOUNT,STATUS,COMMENTS,
VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>EJ_INSERT_STG</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>EJ_INSERT_STG_EX</StagingExInsertQueryName>
		<StagingExUpdateQueryName>EJ_EXCEPTION_UPDATE_QUERY</StagingExUpdateQueryName>
		 <enrichmentPlugin>com.ascent.banknizwa.source.ej.EJEnrichment</enrichmentPlugin> 
		 <preEnrichValidationPlugin>com.ascent.banknizwa.source.ej.EJEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.banknizwa.source.ej.EJPostEnrichValidationPlugin</postEnrichValidationPlugin> 
		<persistancePlugin>com.ascent.banknizwa.source.ej.EJPersistancePlugin</persistancePlugin>
	</Integration>

	<!-- <Integration id="2">
		<name>GL_1002</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1002.csv)</fileNamePattern>
		<sequenceName>GL_1002_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
	<StagingCreateQueryName>Dummy</StagingCreateQueryName>
	<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
	<StagingInsertQueryName>GL_1002_STG_INSERT_QRY</StagingInsertQueryName>
	<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
	<StagingExInsertQueryName>GL_1002_STG_EX_INSERT_QRY</StagingExInsertQueryName>
	<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
	<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
	<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>



	<Integration id="3">
		<name>GL_1472</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1472.csv)</fileNamePattern>
		<sequenceName>GL_1472_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_1472_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_1472_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
			<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>
	
	
	
	
	<Integration id="3">
		<name>GL_1015</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1015.csv)</fileNamePattern>
		<sequenceName>GL_1015_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_1015_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_1015_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
			<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>

	
	<Integration id="3">
		<name>GL_1016</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1016.csv)</fileNamePattern>
		<sequenceName>GL_1016_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_1016_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_1016_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
			<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>
	
	
	<Integration id="2">
		<name>GL_1006</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1006.csv)</fileNamePattern>
		<sequenceName>GL_1006_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_1006_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_1006_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>	
	
	<Integration id="2">
		<name>GL_2247</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_2247.csv)</fileNamePattern>
		<sequenceName>GL_2247_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_2247_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_2247_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>	
	

<Integration id="2">
		<name>GL_1482</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_1482.csv)</fileNamePattern>
		<sequenceName>GL_1482_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_1482_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_1482_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>	
	
	
	<Integration id="2">
		<name>GL_2279</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_2279.csv)</fileNamePattern>
		<sequenceName>GL_2279_SEQ</sequenceName>
		<columnNamesString>
		TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,CARD_NUM
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>GL_2279_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>GL_2279_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.gl.GLEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.gl.GLPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.gl.GLPersistancePlugin</persistancePlugin>
	</Integration>	
	
	<Integration id="36">
		<name>BATCHES</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_batch.csv)</fileNamePattern>
		<sequenceName>BATCHES_UPDATE_SEQ</sequenceName>
		<columnNamesString>
			INSTITUTION_ID,SERNO,EODSERNO,EODCYCLESERNO,CHECKSUM,BATCHDATE,LOADTIME,LOADUSER,LASTMODUSER,LASTMODDATE,FILENAME,FILEDATE,FILESIZE,
			FILESOURCE,FILEDESTINATION,DBAMOUNT,DBCURRENCY,CRAMOUNT,CRCURRENCY,DIRECTION,CHANNELSERNO,FILEID,PRODUCT,ENTEREDAMT,ENTEREDTRXNS,
			EXPECTEDTRXNS,MERSERNO,EXPECTEDAMT,EXPECTEDTIPAMT,FILESEQNO,BATCHNO,BATCHID,DBACTION,DBCLIENTINFO
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>BATCHES_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>BATCHES_EX_STG_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.batches.BatchesEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.batches.BatchesPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.batches.BatchesPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.batches.BatchesPersistancePlugin</persistancePlugin>
	</Integration>
	
	
	<Integration id="8">
		<name>CISO</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_ciso.csv)</fileNamePattern>
		<sequenceName>CISO_SEQ</sequenceName>
		<columnNamesString>
	INSTITUTION_ID,SERNO,PARTITIONKEY,I011_TRACE_NUM,I012_TRXN_TIME,I015_SETTLE_DATE,I016_CONVERS_DATE,I018_MERCH_TYPE,I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,I037_RET_REF_NUM,I038_AUTH_ID,I039_RESP_CD,I041_POS_ID,I042_MERCH_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I059_POS_GEO_DATA,I060_POS_CAP,I062V2_TRANS_ID,I062M_INF_DATA,CASHBACKAMOUNT,PSVATAMOUNT,TRXNQUALIFIER,SUMMCOMCODE,PAYMENTMODE,AUTHTRXNTYPE,AUTHSERNO,AUTHREASONCODE,ACQCOUNTRYCODE,ACQREGION,LINEITEMIND,CONVERTED,TRXNMSGIDENTIFIER
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>CISO_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>CISO_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.ciso.CisoEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.ciso.CisoPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.ciso.CisoPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.ciso.CisoPersistancePlugin</persistancePlugin>
	</Integration>
	
	
	<Integration id="9">
		<name>CTXNS</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_ctrans.csv)</fileNamePattern>
		<sequenceName>CTXNS_SEQ</sequenceName>
		<columnNamesString>
				INSTITUTION_ID,SERNO,PARTITIONKEY,CACCSERNO,CARDSERNO,DEF_CACCSERNO,PRODUCT,BATCHSERNO,TYPESERNO_ALLOC,TYPESERNO_FEES,TYPESERNO_REPORTS,TYPESERNO_REWARDS,
				TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_NOPOST,MSGCLASS,MSGTYPE,TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,
				I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I008_BILLING_FEE,I013_TRXN_DATE,I044_REASON_CODE,
				I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,INSTALMENTTYPE,INSTALMENTINDEPFLAG,INSTALMENTSNUMBER,INSTALMENTSEQ,
				INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTTOTALAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,
				INSTALMENTSERNO,INSTALMENTPARTITIONKEY,SINGLE_MSG_FLAG,AUTHACCOUNTTYPE,ORIGINATOR,ORIGINATORREASONCODE,PROXYCARDNUMBER,
				INVOICENUMBER,AMOUNT,EMBEDDEDFEE,TOTALPOINTS,TAXFLAG,VALUEDATE,STARTOFINTEREST,MINDUEVALUEDATE,POSTDATE,POSTTIMESTAMP,DESCSTRINGID,STGENERAL,LOGACTION,CONVERTED
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>CTXNS_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>CTXNS_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.ctxn.CTxnsEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.ctxn.CTxnsPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.ctxn.CTxnsPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.ctxn.CTxnsPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="9">
		<name>MISO</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_miso.csv)</fileNamePattern>
		<sequenceName>MISO_SEQ</sequenceName>
		<columnNamesString>
	INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,OUTBATCHSERNO,PSNAME,TYPESERNO_CHANNEL,CHANNELSERNO,ONUS,TRXNENTRY,TRXNPRESENCE,TRXNBATCHNUMBER,I011_TRACE_NUM,I014_EXPIRY_DATE,I015_SETTLE_DATE,I016_CONVERS_DATE,I018_MERCH_TYPE,I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,I037_RET_REF_NUM,I038_AUTH_ID,I039_RESP_CD,I041_POS_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I043D_ADDRESS1,I043E_ADDRESS2,I043F_ZIP,I043G_REGION_CODE,I059_POS_GEO_DATA,I060_POS_CAP,I062M_INF_DATA,I062V2_TRANS_ID,I126V8_TRAN_ID,CASHBACKAMOUNT,AUTHTRXNTYPE,AUTHACCOUNTTYPE,ISSCOUNTRYCODE,CARDTYPE,FEEPROGRAMIND,INTERCHANGEREGION,INTERCHANGEDESCRIPTOR,INTERCHANGEFEEIND,INTERCHANGERATE,INTERCHANGEFEEAMOUNT,INTERCHANGEFEEAMOUNTTRXN,INTERFACELOGSERNO,INTERFACEINVOICENUMBER,INTERFACEOTHERDATA,PRODUCTCODE,FUELACCEPTANCEMODE,UNITS,UNITOFMEASURE,UNITPRICE,FLEETNUMBER,FLEETBRAND,LINEITEMIND,EXTERNALFILEID,EXTERNALTRXNID,INTERCHANGEFEEAMTCNTCUR,POICURRCONV,POICURRCODE,DCCSTATUS,DCCSCONVRATE,TRXNMSGIDENTIFIER	
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>MISO_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>MISO_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.miso.MisoEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.miso.MisoPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.miso.MisoPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.miso.MisoPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="8">
		<name>MTXNS</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_mtrans.csv)</fileNamePattern>
		<sequenceName>MTXNS_seq</sequenceName>
		<columnNamesString>
INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,MERSERNO,MERACCSERNO,BALANCESERNO,BALANCEPARTITIONKEY,BALANCESUBPARTITIONKEY,PRODUCT,INBATCHSERNO,TYPESERNO_COM,TYPESERNO_REPORTS,TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_FEES,TYPESERNO_VAT,MSGCLASS,MSGTYPE,TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I012_TRXN_TIME,I013_TRXN_DATE,I042_MERCH_ID,I044_REASON_CODE,I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,EXRATETRXNTOSETTLEMENT,CENTERCURRENCY,CENTERAMOUNT,INSTALMENTTYPE,INSTALMENTSNUMBER,INSTALMENTSEQ,INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,INSTALMENTSERNO,INSTALMENTPARTITIONKEY,INSTALMENTSUBPARTITIONKEY,SINGLE_MSG_FLAG,ORIGINATOR,ORIGINATORREASONCODE,AMOUNT,TIPAMOUNT,BILLTIPAMOUNT,COMAMOUNT,ORIGCOMAMOUNT,ISCOMEMBEDDED,EMBEDDEDFEE,VATAMOUNT,VATSETTLEMENTAMOUNT,VATBILLINGAMOUNT,ISVATEMBEDDED,VATCOMMISSIONAMOUNT,ISCOMVATEMBEDDED,POSTDATE,POSTTIMESTAMP,PAYDATE,STGENERAL,LOGACTION
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>MTXNS_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>MTXNS_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.mtxn.MTxnsEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.mtxn.MTxnsPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.mtxn.MTxnsPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.mtxn.MTxnsPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="6">
		<name>AUTH_ISSUER</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_auth.csv)</fileNamePattern>
		<sequenceName>AUTH_ISSUER_seq</sequenceName>
		<columnNamesString>
I007_TRANS_DT,CARDBIN,I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,I006_AMT_BILL,SERNO,LTIMESTAMP,I000_MSG_TYPE,SOURCE,I039_RSP_CD,REASONCODE,I003_PROC_CODE,I025_POS_COND,I002_NUMBER,I042_MERCH_ID,I043A_MERCH_NAME,I032_ACQUIRER_ID,I041_POS_ID,I019_ACQ_COUNTRY,I018_MERCH_TYPE,I037_RET_REF_NUM,I004_AMT_TRXN,AMT_CENTER,I038_AUTH_ID
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>AUTH_ISSUER_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>AUTH_ISS_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.authiss.AuthIssEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.authiss.AuthIssPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.authiss.AuthIssPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.authiss.AuthIssPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="10">
		<name>QCB</name>
		<fileType>csv</fileType>
		<fileNamePattern>nps_rep_</fileNamePattern>
		<sequenceName>QCB_SEQ</sequenceName>
		<columnNamesString>
	CHANNEL,ACQ_BANK_CODE,ISS_BANK_CODE,TERMINAL_ID,CARD_NUMBER,TRANS_AMOUNT,CURRENCY_CODE,TRAN_DATE,TRAN_TIME,SETTL_DATE,RETR_REF_NO,TRAN_TYPE,ACC_TYPE,RESP_CODE,RETAIL_ID,REF_NO</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>QCB_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>QCB_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.qcb.QcbEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.qcb.QcbPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.qcb.QcbPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.qcb.QcbPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="4">
		<name>AUTH_ACQUIRER</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_acq.csv)</fileNamePattern>
		<sequenceName>AUTH_ACQUIRER_seq</sequenceName>
		<columnNamesString>
		PROCESS_NAME,EDCFLAG,I007_TRANS_DT,CARDBIN,I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,I006_AMT_BILL,SERNO,LTIMESTAMP,I000_MSG_TYPE,SOURCE,I039_RESP_CD,REASONCODE,I003_PROC_CODE,I025_POS_COND,I002_NUMBER,I042_MERCH_ID,I043A_MERCH_NAME,I032_ACQUIRER_ID,I041_POS_ID,I019_ACQ_COUNTRY,I018_MERCH_TYPE,I037_RET_REF_NUM,I004_AMT_TRXN,CENTERAMT,I038_AUTH_ID,
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>AUTH_ACQUIRER_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>AUTH_ACQUIRER_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.authacq.AuthAcqEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.authacq.AuthAcqPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.authacq.AuthAcqPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.authacq.AuthAcqPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="12">
		<name>QPAY</name>
		<fileType>csv</fileType>
		<fileNamePattern>DHBNAPS</fileNamePattern>
		<sequenceName>QPAY_SEQ</sequenceName>
		<columnNamesString>
BACKEND_ID,TRAN_DATE,AMOUNT,CURRENCY,DATE,TXN_CODE,CARD_NUMBER,ACQ_REF_NO,PAYMENT_UNQ_NO,SOURCE_TYPE,SOURCE_ID,SOURCE_MXP_ID,
SOURCE_BANK_ACC,SOURCE_ACC_CURRENCY,SOURCE_AMOUNT,DEST_TYPE,DEST_ID,DES_MXP_ACC,DES_BANK_ACC,DES_ACC_CURRENCY,DES_AMT,TXN_SOURCE,REF_NO
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>QPAY_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>QPAY_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.qpay.QpayEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.qpay.QpayPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.qpay.QpayPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.qpay.QpayPersistancePlugin</persistancePlugin>
	</Integration>	
	
	
	<Integration id="11">
		<name>VISA</name>
		<fileType>csv</fileType>
		<fileNamePattern>visa_aq.txt</fileNamePattern>
		<sequenceName>VISA_SEQ</sequenceName>
		<columnNamesString>
bat_num,xmit_date,time,card_number,card_no,retrieval_ref_number,trace_number,issuer_trmnl,tran_type,procss_code,ent_mod,cn_stp,rsp_cd,reas_code,amount,kes_currency,settlement_amount,cr_currency,ca_id,terminal_id,fpi,ci,report_date,tr_id,aci,fee_juris,routing,fee_level
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>VISA_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>VISA_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.visa.VisaEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.visa.VisaPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.visa.VisaPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.visa.VisaPersistancePlugin</persistancePlugin>
	</Integration>
	<Integration id="11">
		<name>UnionPay</name>
		<fileType>csv</fileType>
		<fileNamePattern>unionpay_aq</fileNamePattern>
		<sequenceName>CUP_SEQ</sequenceName>
		<columnNamesString>
acq_no,sender_no,system_trace_no,tran_tranmission_time,pan,trans_amount,msg_type,trans_type_code,merchant_type,teminal_code,
acq_identy_code,name_add_acceptor,retr_ref_no,point_of_service_condition_code,authorizer_resp_code,receiver_no,orgnl_sys_trace_no,
tran_resp_code,tran_currency,service_entry_mode,settlement_currency,settlement_volume,sett_exchange_rate,sett_date,conversion_date,
receivable_service_fee,payable_service_no,interchange_fee,exchange_rate_interchange_fee,exchange_rate_service_fee,transaction_fee,
currency_transaction_fee,exchange_rate_tran_fee,resered
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>CUP_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>CUP_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.cup.CupEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.cup.CupPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.cup.CupPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.cup.CupPersistancePlugin</persistancePlugin>
	</Integration>

	<Integration id="11">
		<name>MASTER</name>
		<fileType>csv</fileType>
		<fileNamePattern>master_aq.001</fileNamePattern>
		<sequenceName>TTUF_SEQ</sequenceName>
		<columnNamesString>
msg_type,switch_serial_no,processor_acq_iss,processor_id,tran_date,tran_time,pan,primary_acc_no,
process_code,trace_no,merchant_type,pos_entry,refrence_no,acq_inst_id,terminal_id,resp_code,brand,advice_reason_code,
intra_arg_code,authorize_id,currecy_code_tran,implied_decimal,trans_amount,trans_amt_dr_cr_indicator,cash_back_amt,
cash_back_amt_dr_cr_indicator,access_fee,access_fee_dr_cr_indicator,currecy_code_sett,implied_decimal_sett,conversion_rate_sett,
sett_amount,sett_amount_dr_cr_indicator,interchange_fee,interchange_fee_dr_cr_indicator,service_level_indicator,resp_code1,
filler,positive_id_indicator,atm_surchange_fee,cross_border_indicator,cross_border_currency_indicator,visa_isa_fee_indicator,
req_amount,filler_1,tran_no,filler_2
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>MASTER_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>MASTER_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.master.MasterEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.master.MasterPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.master.MasterPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.master.MasterPersistancePlugin</persistancePlugin>
	</Integration>
	
	<Integration id="10">
		<name>DPAY</name>
		<fileType>csv</fileType>
		<fileNamePattern>_dpay.csv</fileNamePattern>
		<sequenceName>DEPAY_SEQ</sequenceName>
		<columnNamesString>
                      SERNO,EMP_CODE,COMP_CODE,CARDNO,BALANCE,CREDITLIMIT,BATCHSERNO,I004_AMT_TRXN,I007_LOAD_DATE,
                      I013_TRXN_DATE,I048_TEXT_DATA,I049_CUR_TRXN,AMOUNT,POSTDATE,PARTITIONKEY,POSTTIMESTAMP
        </columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>DPAY_STG_INSERT_QRY</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>DPAY_STG_EX_INSERT_QRY</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.dpay.DpayEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.dpay.DpayPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.dpay.DpayPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.dpay.DpayPersistancePlugin</persistancePlugin>
	</Integration>
	 <Integration id="1">
		<name>LEDG_CLOSING</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_LEDG_CLOSING.csv)</fileNamePattern>
		<sequenceName>ledg_close_seq</sequenceName>
		<columnNamesString>
			 BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,PRE_DAY_CRNT_BAL,PRE_BANK_DATE
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>LEDG_CLOSING_INSERT_STG</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>LEDG_CLOSING_INSERT_STG_EX</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.ledgclose.LedgCloseEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.ledgclose.LedgClosePreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.ledgclose.LedgClosePostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.ledgclose.LedgClosePersistancePlugin</persistancePlugin>
	</Integration>
	 <Integration id="1">
		<name>BCS</name>
		<fileType>csv</fileType>
		<fileNamePattern>(_REPLENISHMENT.csv)</fileNamePattern>
		<sequenceName>Replenishment</sequenceName>
		<columnNamesString>
			DISPLAYID,LOCATION,START_DATETIME,END_DATETIME,NOTE_LEFT_CAS1,NOTE_LEFT_CAS2,NOTE_LEFT_CAS3,NOTE_LEFT_CAS4,NOTE_ADDED_CAS1,NOTE_ADDED_CAS2,NOTE_ADDED_CAS3,NOTE_ADDED_CAS4
		</columnNamesString>
		<StagingCreateQueryName>Dummy</StagingCreateQueryName>
		<StagingExCreateQueryName>Dummy</StagingExCreateQueryName>
		<StagingInsertQueryName>REPL_INSERT_STG</StagingInsertQueryName>
		<StagingUpdateQueryName>Dummy</StagingUpdateQueryName>
		<StagingExInsertQueryName>REPL_INSERT_STG_EX</StagingExInsertQueryName>
		<StagingExUpdateQueryName>Dummy</StagingExUpdateQueryName>
		<enrichmentPlugin>com.ascent.integration.sources.bcs.BcsEnrichment</enrichmentPlugin>
		<preEnrichValidationPlugin>com.ascent.integration.sources.bcs.BcsPreEnrichValidationPlugin</preEnrichValidationPlugin>
		<postEnrichValidationPlugin>com.ascent.integration.sources.bcs.BcsPostEnrichValidationPlugin</postEnrichValidationPlugin>
		<persistancePlugin>com.ascent.integration.sources.bcs.BcsPersistancePlugin</persistancePlugin>
	</Integration> -->
	 
</Integrations>

