package com.ascent.boot.recon;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.query.Queries;
import com.ascent.custumize.recon.Recon;
import com.ascent.custumize.recon.Recons;
import com.ascent.util.AscentAutoReconConstants;

public class ReconMetaInstance {

	private static Logger logger = LogManager.getLogger(ReconMetaInstance.class);

	private static ReconMetaInstance instance;
	private Properties bootProperties;
	private Properties dbProperties;
	private Properties applicationProperties;

	private Recons reconConfs = null;
	private Map<String, Queries> reconQueriesMap = new HashMap<String, Queries>();

	private ReconMetaInstance() throws Exception {

		String bootPropFileName = "boot.properties";
		String dbPropFileName = "db.properties";
		String applicationPropFileName = "application.properties";

		this.bootProperties = new Properties();
		this.dbProperties = new Properties();
		this.applicationProperties = new Properties();

		InputStream inputStream = getClass().getClassLoader().getResourceAsStream(bootPropFileName);

		if (inputStream != null) {
			try {
				this.bootProperties.load(inputStream);

				//logger.trace("Loaded bootProperties ");
				for (Object key : this.bootProperties.keySet()) {
					//logger.trace(key + " : " + this.bootProperties.get(key));
					this.bootProperties.get(key);
				}
				//logger.trace("Sucessfully ");
				String appMode = (String) this.bootProperties.get("APP_MODE");
				try {

					InputStream dbInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + dbPropFileName);

					InputStream appInputStream = getClass().getClassLoader()
							.getResourceAsStream(appMode + "/" + applicationPropFileName);

					if (dbInputStream != null) {
						try {
							this.dbProperties.load(dbInputStream);

							//logger.trace("Loaded dbProperties ");
							for (Object key : this.dbProperties.keySet()) {
								//logger.trace(key + " : " + this.dbProperties.get(key));
								this.dbProperties.get(key);
							}
							//logger.trace("Sucessfully ");

						} catch (Exception e) {
							//logger.trace("Unable to load dbProperties ");
							e.printStackTrace();
							throw e;

						}
					} else {
						//logger.trace("property file '" + dbPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + dbPropFileName + "' not found in the classpath");
					}

					if (appInputStream != null) {

						try {
							this.applicationProperties.load(appInputStream);

							//logger.trace("Loaded applicationProperties ");
							/*for (Object key : this.applicationProperties.keySet()) {
								logger.trace(key + " : " + this.applicationProperties.get(key));
							}*/
							//logger.trace("Sucessfully ");

						} catch (Exception e) {
							//logger.trace("Unable to load applicationProperties ");
							e.printStackTrace();
							throw e;

						}

					} else {
						//logger.trace("property file '" + applicationPropFileName + "' not found in the classpath");
						throw new FileNotFoundException(
								"property file '" + applicationPropFileName + "' not found in the classpath");
					}

				} catch (Exception e) {
					e.printStackTrace();
					//logger.trace("unable to load properties under the APP_MODE " + appMode);
					throw new Exception("unable to load properties under the APP_MODE " + appMode);
				}
			} catch (Exception e) {
				//logger.trace("Unable to load bootProperties properties");
				e.printStackTrace();
				throw e;
			}

		} else {
			//logger.trace("property file '" + bootPropFileName + "' not found in the classpath");
			throw new FileNotFoundException("property file '" + bootPropFileName + "' not found in the classpath");
		}
		try {
			loadReconConf();
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			throw e;
		}
	}

	static {
		try {
			instance = new ReconMetaInstance();
		} catch (Exception e) {
			throw new RuntimeException("Exception occured in creating singleton instance");
		}
	}

	public void loadReconConf() {

		JAXBContext jaxbContext = null;
		Unmarshaller jaxbUnmarshaller = null;
		try {

			File reconConfFile = new File(
					this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF));

			InputStream reconConfFileStream = getClass().getClassLoader().getResourceAsStream(
					this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF_PATH_PREFIX)
							+ this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF));

			jaxbContext = JAXBContext.newInstance(Recons.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();

			if (reconConfFileStream != null) {
				try {
					this.reconConfs = (Recons) jaxbUnmarshaller.unmarshal(reconConfFileStream);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
				}
			} else {
				try {
					this.reconConfs = (Recons) jaxbUnmarshaller.unmarshal(reconConfFile);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error(e.getMessage(), e);
				}
			}
			this.reconConfs.bootConf();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {

		}

	}

	public Queries loadReconConfQueries(String reconConfName) {

		JAXBContext jaxbContext = null;
		Unmarshaller jaxbUnmarshaller = null;
		Queries queries = null;
		try {

			File reconConfFile = new File(
					this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF_PATH_PREFIX)
							+ reconConfName + "_QRY_CONF.xml");

			InputStream reconConfFileStream = getClass().getClassLoader().getResourceAsStream(
					this.applicationProperties.getProperty(AscentAutoReconConstants.RECON_CONF_PATH_PREFIX)
							+ reconConfName + "_QRY_CONF.xml");

			jaxbContext = JAXBContext.newInstance(Queries.class);

			jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			if (reconConfFileStream != null) {
				queries = (Queries) jaxbUnmarshaller.unmarshal(reconConfFileStream);
			} else {
				queries = (Queries) jaxbUnmarshaller.unmarshal(reconConfFile);
			}

			if (queries == null) {
				this.reconQueriesMap.put(reconConfName, queries);
			}
			queries.bootConf();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {

		}
		return queries;
	}

	public Queries getReconQueries(String reconConfName) throws Exception {
		Queries reconConfQueries = null;

		reconConfQueries = reconQueriesMap.get(reconConfName);

		if (reconConfQueries == null) {
			Recon reconCoonf = getReconConf(reconConfName);

			if (reconCoonf != null) {
				reconConfQueries = loadReconConfQueries(reconConfName);
				if (reconConfQueries == null) {
					throw new Exception("Unable to loadd the queries for recon confName" + reconConfName);
				}
			} else {
				throw new Exception("Unable to loadd the recon configuration :" + reconConfName);
			}
		}

		return reconConfQueries;
	}

	public static ReconMetaInstance getInstance() {
		return instance;
	}

	public Properties getBootProperties() {
		return bootProperties;
	}

	public void setBootProperties(Properties bootProperties) {
		this.bootProperties = bootProperties;
	}

	public Properties getDbProperties() {
		return dbProperties;
	}

	public void setDbProperties(Properties dbProperties) {
		this.dbProperties = dbProperties;
	}

	public Properties getApplicationProperties() {
		return applicationProperties;
	}

	public void setApplicationProperties(Properties applicationProperties) {
		this.applicationProperties = applicationProperties;
	}

	public Recon getReconConf(String reconConfName) throws Exception {
		Recon reconConf = null;
		if (this.reconConfs != null) {
			reconConf = this.reconConfs.getReconConf(reconConfName);
			if (reconConf == null) {
				throw new Exception("No Recon Configuration available with the name" + reconConf);
			}
		} else {
			throw new Exception("Recon configuration not loaded ");
		}
		return reconConf;
	}

	public Recons getReconConfs() {
		return reconConfs;
	}

	public void setEtlConfs(Recons reconConfs) {
		this.reconConfs = reconConfs;
	}
}