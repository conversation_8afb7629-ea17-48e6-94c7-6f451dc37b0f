package com.ascent.ds.login.ldapv2;

import java.util.Hashtable;

import javax.naming.Context;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;

import org.apache.log4j.chainsaw.Main;

import com.itextpdf.text.log.SysoCounter;

public class LdapSimpleAuth {

	public static void main(String[] args) {
		LdapSimpleAuth ldapSimpleAuth= new LdapSimpleAuth();
		
		boolean flag=ldapSimpleAuth.validateUser(null, null, null);
		
		
		if(flag){
			System.out.println("use is valid");
		}else{
			System.out.println("use is not validf");
		}
	}
	
	
	public boolean validateUser(String userName, String password, String url){
		boolean isvalid=false;
		url = "ldap://***********:389";
		userName="ascentitgroup\\giri";
		password="ygbr$321";
		Hashtable<String, String> env = new Hashtable<String, String>();
		env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
		env.put(Context.PROVIDER_URL, url);
		env.put(Context.SECURITY_AUTHENTICATION, "simple");
		env.put(Context.SECURITY_PRINCIPAL, userName);
		env.put(Context.SECURITY_CREDENTIALS, password);

		try {
			DirContext ctx = new InitialDirContext(env);
			System.out.println("connected");
			if(ctx!=null){
				isvalid=true;
			}

			ctx.close();

		} catch (Exception e) {
			isvalid=false;
			//e.printStackTrace();

		}
		return isvalid;
	}
	
	
}
