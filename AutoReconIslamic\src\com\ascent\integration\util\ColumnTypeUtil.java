package com.ascent.integration.util;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

public class ColumnTypeUtil {

	public static Map<String, Integer> dbTypes = new HashMap<String, Integer>();

	static {

		dbTypes.put("ARRAY", Types.ARRAY);
		dbTypes.put("BIGINT", Types.BIGINT);
		dbTypes.put("BINARY", Types.BINARY);
		dbTypes.put("BIT", Types.BIT);
		dbTypes.put("BLOB", Types.BLOB);
		dbTypes.put("BOOLEAN", Types.BOOLEAN);
		dbTypes.put("CHAR", Types.CHAR);
		dbTypes.put("CLOB", Types.CLOB);
		dbTypes.put("DATALINK", Types.DATALINK);
		dbTypes.put("DATE", Types.DATE);
		dbTypes.put("DECIMAL", Types.DECIMAL);
		dbTypes.put("DISTINCT", Types.DISTINCT);
		dbTypes.put("DOUBLE", Types.DOUBLE);
		dbTypes.put("FLOAT", Types.FLOAT);
		dbTypes.put("INTEGER", Types.INTEGER);
		dbTypes.put("JAVA_OBJECT", Types.JAVA_OBJECT);
		dbTypes.put("LONGNVARCHAR", Types.LONGNVARCHAR);
		dbTypes.put("LONGVARBINARY", Types.LONGVARBINARY);
		dbTypes.put("NCHAR", Types.NCHAR);
		dbTypes.put("NCLOB", Types.NCLOB);
		dbTypes.put("NUMERIC", Types.NUMERIC);
		dbTypes.put("NVARCHAR", Types.NVARCHAR);
		dbTypes.put("REAL", Types.REAL);
		dbTypes.put("REF", Types.REF);
		dbTypes.put("ROWID", Types.ROWID);
		dbTypes.put("SMALLINT", Types.SMALLINT);
		dbTypes.put("SQLXML", Types.SQLXML);
		dbTypes.put("STRUCT", Types.STRUCT);
		dbTypes.put("TIME", Types.TIME);
		dbTypes.put("TIMESTAMP", Types.TIMESTAMP);
		dbTypes.put("TINYINT", Types.TINYINT);
		dbTypes.put("VARCHAR", Types.VARCHAR);
		dbTypes.put("VARBINARY", Types.VARBINARY);

	}

}
