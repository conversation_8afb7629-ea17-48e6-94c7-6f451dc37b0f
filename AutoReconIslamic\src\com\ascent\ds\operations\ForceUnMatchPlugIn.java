package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ForceUnMatchPlugIn extends BasicDataSource implements PagesConstants {


	
	/**
	 * 
	 */
	private static final long serialVersionUID = -6870261822605048138L;
	public static long start = 0l;

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();

		User user = (User) httpSession.getAttribute("userId");

		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		//TODO: is user authorized utility to verify user priviliges.
		
		String userId = user.getUserId();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		//List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		Map<String,List>  selectedRecords=(Map) reqCriteria.get("selectedRecords");
		String reconDataSource= (String) reqCriteria.get("reconDataSource");
		String centrifugal=(String) reqCriteria.get("centrifugal");
		List basestylelist= (List) reqCriteria.get("basestylelist");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName =(String)reqCriteria.get("moduleName");
		List dsName=(List)reqCriteria.get("dsName");
		List<Map<String, Object>> recordsList=(List<Map<String, Object>>)reqCriteria.get("recordsList");
		
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
		Object amountField= (Object)reqCriteria.get("centrifugalAmount");
		String centrifugalAmountField=String.valueOf(amountField);
		System.out.println(amountField+"<------------------->"+centrifugalAmountField);
		
		// action :"SUPPRESS",

		//TODO: operation synchronization.
		if (selectedRecords != null) {

			StringBuilder commentSb = new StringBuilder();
			List<Object> workflowIds = new ArrayList<Object>();
			/*for (String  key : selectedRecords.keySet()) {
                  List<Map<String,Object>> reclist=selectedRecords.get(key);
                  for(Map rec : reclist){
				 if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
						
					}else{
						workflowIds.add(rec.get(SID));
					}
                  }
			}*/
			 for (Map<String, Object> rec : recordsList) {
	if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
		
	}else{
		workflowIds.add(rec.get("RECON_ID"));
	}
	
}
			
			
			String commentPrefix = " ";
			if (workflowIds.size() > 0) {
				result = new HashMap<String, Object>();
				//String commentPrefix = null;
				if (workflowIds.size() == 1) {
					commentSb.append("Selected record with RECON_ID ");
				} else if (workflowIds.size() > 1) {
					commentSb.append("Selected records with RECON_IDs ");
				}
				for (Object obj : workflowIds) {
					if (commentSb.length() != 0) {
						commentSb.append(",");
					}
					commentSb.append(obj);
				}
				commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
				updateResultStatus(result, FAILED, commentPrefix);
				response.setData(result);
				return response;
			}
						
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		paramsMap.put("recordsList", recordsList);
		paramsMap.put("basestylelist", basestylelist);
		paramsMap.put("reconDataSource", reconDataSource);
		paramsMap.put("centrifugal", centrifugal);
		
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		if(centrifugalAmountField!=null){
			paramsMap.put("centrifugalAmountField",centrifugalAmountField);	
		}
		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> forceUnmatchArgs) {

		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
			Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) forceUnmatchArgs.get(USER_ID);
			List dsName = (List) forceUnmatchArgs.get(DS_NAME);
			forceUnmatchArgs.put(PERSIST_CLASS, FORCE_UNMATCH_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", forceUnmatchArgs);
			
			

			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) forceUnmatchArgs.get(BUSINES_AREA);
				String reconName = (String) forceUnmatchArgs.get(RECON_NAME);
				String comments = (String) forceUnmatchArgs.get(COMMENTS);
				String requesterComments=userId+" : "+comments;
				String moduleName=(String)forceUnmatchArgs.get(MODULE);
				userAdminManager.createActivity(connection, user, businessArea, reconName, "RECON_FORCE_UNMATCH",
						FORCE_UNMATCH_OPERATION, activityDataMap, activityStatus, comments);

				updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				
				String integrationName= (String) forceUnmatchArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator=new LoadRegulator();
				InsertRegulator insertRegulator=new InsertRegulator();
				//Recon Data Sorces will get through DS_NAME key from front end 
				String reconDataSource=(String) (forceUnmatchArgs.get("reconDataSource"));
				String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
			//	List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) forceUnmatchArgs.get(SELECTED_RECORDS);
				List<Map<String,Object>> recordsList=(List<Map<String,Object>> ) forceUnmatchArgs.get("recordsList");
				System.out.println("Recon Table Name : "+reconTableName);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement reconDataSelectPstmt=null;
				PreparedStatement stagingDataSelectPstmt=null;
				PreparedStatement auditDataInsertPstmt=null;
				PreparedStatement stagingDataUpdatePstmt=null;
				PreparedStatement reconDataUpdatePstmt=null;
				
				try{
			
					
			         
				Long reconId=null;
					
					for(Map<String,Object> selectedRec:recordsList){
			
						 reconId=(Long) selectedRec.get("RECON_ID");
				
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
						
						//FINANCE ONS
						if(reconSide.equalsIgnoreCase("CBS") ){	
								stgTableName="FIN_ONS_CBS_STG";
								auditTableName="FIN_ONS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CBO") ){	
								stgTableName="FIN_ONS_CBO_STG";
								auditTableName="FIN_ONS_CBO_STG_AUDIT";
							}
						//FINANCE MPCLEAR
						else if(reconSide.equalsIgnoreCase("MP_CLEAR_CBS") ){	
								stgTableName="FIN_MP_CLEAR_CBS_STG";
								auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("EXT") ){	
								stgTableName="FIN_MP_CLEAR_EXT_STG";
								auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
							}
						//FINANCE -  SUSPENSE
						else if(reconSide.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || reconSide.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
								stgTableName="FIN_SUSPENSE_STG";
								auditTableName="FIN_SUSPENSE_STG_AUDIT";
							}
						//CARDS -  ATM MASTER CARD ACQUIRER
						else if(reconSide.equalsIgnoreCase("ATM_MC_CBS") ){	
								stgTableName="CARD_ATM_MC_CBS_STG";
								auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("ACQ") ){	
								stgTableName="CARD_ATM_MC_ACQ_STG";
								auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
							}
						//CARDS -  ATM VISA ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
								stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
								auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
								stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
								auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
							}
						//CARDS -  POS NI ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_CBS") ){	
								stgTableName="CARD_POSNI_CBS_STG";
								auditTableName="CARD_POSNI_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
								stgTableName="CARD_POSNI_TRAN_STG";
								auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
							}
						//CARDS -  CREDIT CARD STATEMENT
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
								stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
								auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
								stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
								auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
							}
						//CARDS -  ATM/POS VISA ISSUER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
								stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
								auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
							stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
							auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
						}
						/*else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_PST") ){	
								stgTableName="CARD_ATM_VISA_ISS_PST_STG";
								auditTableName="CARD_ATM_VISA_ISS_PST_STG_AUDIT";
							}*/
						//CENTRAL OPERATION DEPARTMENT - CDM
						else if(reconSide.equalsIgnoreCase("CO_CDM_CBS") ){	
								stgTableName="CO_CDM_CBS_STG";
								auditTableName="CO_CDM_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
								stgTableName="CO_CDM_JOURNAL_STG";
								auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
							}
						//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
						else if(reconSide.equalsIgnoreCase("CO_ATM_CBS") ){	
								stgTableName="CO_ATM_CBS_STG";
								auditTableName="CO_ATM_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
								stgTableName="CO_ATM_JOURNAL_STG";
								auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
							}
						//CENTRAL OPERATION DEPARTMENT - ACH
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBS") ){	
								stgTableName="CO_ACH_CBS_STG";
								auditTableName="CO_ACH_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBO") ){	
								stgTableName="CO_ACH_CBO_STG";
								auditTableName="CO_ACH_CBO_STG_AUDIT";
							}
						
						//inserting STG to AUDIT
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						Map stagingDataMap=new HashMap();
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
						long version=0;
						while(stagingRs.next()){
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						//inserting STG to AUDIT End
						
						
						//Updating STG table
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='Y',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=? WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stagingDataUpdatePstmt.setObject(4, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
					   //Updating STG table End
						
						
					}
					
					//Updating Recon table
					reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='Y',UPDATED_ON=?,ACTIVITY_COMMENTS=? WHERE RECON_ID="+reconId);
					reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					reconDataUpdatePstmt.setObject(2, requesterComments);
					reconDataUpdatePstmt.executeUpdate();	
					//Updating Recon table End
						
					}
				
					try{
						String strquery="select email_id from users where user_name='"+user.getReporting()+"'";
						Statement st=connection.createStatement();
						ResultSet rs=st.executeQuery(strquery);
						if(rs.next()){
							String appMailId=rs.getString(1);
							OperationMail mail=	new OperationMail(user.getEmailId(), appMailId);
							mail.sendMail(user.getEmailId(), appMailId);
							mail.sentMail(appMailId, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE UNMATCH PENDING FOR APPROVAL");
						}
					}catch(Exception e){
						
					}
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
				}
			
			return result;
			} else {

				

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED,OPERATION_FAILED);
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}
		
		
	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection
			) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator=new LoadRegulator();
		InsertRegulator insertRegulator=new InsertRegulator();
		try {
			Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
			String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
			String reconName = (String) (activityRecordsMap.get(RECON_NAME));
			//List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
			String reconDataSource=(String) (activityRecordsMap.get("reconDataSource"));
			String centrifugal=(String) (activityRecordsMap.get("centrifugal"));
			String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
			List<Map<String,Object>> recordsList=(List<Map<String,Object>> ) activityRecordsMap.get("recordsList");
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement reconDataSelectPstmt=null;
			PreparedStatement stagingDataSelectPstmt=null;
			PreparedStatement auditDataInsertPstmt=null;
			PreparedStatement stagingDataUpdatePstmt=null;
			PreparedStatement reconDataUpdatePstmt=null;
			Long reconId=null;
			 String userId = (String) activityDataMap.get("userId");
				String comments = (String) activityRecordsMap.get(COMMENTS);
				String requesterComments=userId+" : "+comments;
			connection = DbUtil.getConnection();

			if (APPROVED.equalsIgnoreCase(status)) {
				
			
				
				try{
			
					
			         
				
					
					for(Map<String,Object> selectedRec:recordsList){
			
						 reconId=(Long) selectedRec.get("RECON_ID");
				
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					int jj = 0;
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						start = System.currentTimeMillis();
						jj++;
						long start1 = start;
						DateFormat dateFormat = new SimpleDateFormat("yyMMdd");
						Date date = new Date();
						System.out.println(start1);
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
							
						//FINANCE ONS
						if(reconSide.equalsIgnoreCase("CBS") ){	
								stgTableName="FIN_ONS_CBS_STG";
								auditTableName="FIN_ONS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CBO") ){	
								stgTableName="FIN_ONS_CBO_STG";
								auditTableName="FIN_ONS_CBO_STG_AUDIT";
							}
						//FINANCE MPCLEAR
						else if(reconSide.equalsIgnoreCase("MP_CLEAR_CBS") ){	
								stgTableName="FIN_MP_CLEAR_CBS_STG";
								auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("EXT") ){	
								stgTableName="FIN_MP_CLEAR_EXT_STG";
								auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
							}
						//FINANCE -  SUSPENSE
						else if(reconSide.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || reconSide.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
								stgTableName="FIN_SUSPENSE_STG";
								auditTableName="FIN_SUSPENSE_STG_AUDIT";
							}
						//CARDS -  ATM MASTER CARD ACQUIRER
						else if(reconSide.equalsIgnoreCase("ATM_MC_CBS") ){	
								stgTableName="CARD_ATM_MC_CBS_STG";
								auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("ACQ") ){	
								stgTableName="CARD_ATM_MC_ACQ_STG";
								auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
							}
						//CARDS -  ATM VISA ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
								stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
								auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
								stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
								auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
							}
						//CARDS -  POS NI ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_CBS") ){	
								stgTableName="CARD_POSNI_CBS_STG";
								auditTableName="CARD_POSNI_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
								stgTableName="CARD_POSNI_TRAN_STG";
								auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
							}
						//CARDS -  CREDIT CARD STATEMENT
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
								stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
								auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
								stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
								auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
							}
						//CARDS -  ATM/POS VISA ISSUER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
								stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
								auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
							stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
							auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
						}
						/*else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_PST") ){	
								stgTableName="CARD_ATM_VISA_ISS_PST_STG";
								auditTableName="CARD_ATM_VISA_ISS_PST_STG_AUDIT";
							}*/
						//CENTRAL OPERATION DEPARTMENT - CDM
						else if(reconSide.equalsIgnoreCase("CO_CDM_CBS") ){	
								stgTableName="CO_CDM_CBS_STG";
								auditTableName="CO_CDM_CBS_STG_AUDIT";
						}
						else if(reconSide.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
								stgTableName="CO_CDM_JOURNAL_STG";
								auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
						}
						//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
						else if(reconSide.equalsIgnoreCase("CO_ATM_CBS") ){	
								stgTableName="CO_ATM_CBS_STG";
								auditTableName="CO_ATM_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
								stgTableName="CO_ATM_JOURNAL_STG";
								auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
							}
						//CENTRAL OPERATION DEPARTMENT - ACH
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBS") ){	
								stgTableName="CO_ACH_CBS_STG";
								auditTableName="CO_ACH_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBO") ){	
							stgTableName="CO_ACH_CBO_STG";
								auditTableName="CO_ACH_CBO_STG_AUDIT";
							}
						
						
						//inserting STG to AUDIT
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						
						Map stagingDataMap=new HashMap();
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
						long version=0;
						
						while(stagingRs.next()){
							
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						//inserting STG to AUDIT End
						
						
						String reconUpdateQuery=null;
						int recversion=Integer.parseInt(rs.getObject("VERSION").toString());
						++recversion;
						if(reconSide.equalsIgnoreCase(centrifugal)){ //CHECKING CENTRIFUGAL OR NOT
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?,RECON_STATUS=?,RECON_ID=?  WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stagingDataUpdatePstmt.setObject(4, "MU");
						
						stagingDataUpdatePstmt.setObject(5, start1+""+jj);
						stagingDataUpdatePstmt.setObject(6, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
					    /*reconUpdateQuery="UPDATE "+reconTableName+" SET MATCH_TYPE='MU',COMMENTS='MANUAL UNMATCH',WORKFLOW_STATUS='N',ACTIVE_INDEX='Y',STATUS='FORCE UNMATCH',"
					    		+ "UPDATED_ON=?,USER_ID=?,VERSION=?,RECON_ID=?,ACTIVITY_COMMENTS=?  WHERE ID="+rs.getObject("ID")+" AND  RECON_SIDE=?";*/
					    reconUpdateQuery="UPDATE "+reconTableName+" SET MATCH_TYPE='MU',COMMENTS='MANUAL UNMATCH',WORKFLOW_STATUS='N',ACTIVE_INDEX='Y',STATUS='FORCE UNMATCH',"
					    		+ "UPDATED_ON=?,USER_ID=?,VERSION=?,RECON_ID=?,ACTIVITY_COMMENTS=?  WHERE SID=? AND RECON_ID=? AND RECON_SIDE=?";//Narendra Added
						}else{
							
							String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?,RECON_STATUS=?,RECON_ID=?  WHERE SID=?";
							stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
							stagingDataUpdatePstmt.setObject(1, version);
							stagingDataUpdatePstmt.setObject(2, requesterComments);
							stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));
							stagingDataUpdatePstmt.setObject(4, null);
							stagingDataUpdatePstmt.setObject(5, null);
							
							stagingDataUpdatePstmt.setObject(6, sid);
						    int row=	stagingDataUpdatePstmt.executeUpdate();
							/*reconUpdateQuery="UPDATE "+reconTableName+" SET MATCH_TYPE='MU',WORKFLOW_STATUS='N',ACTIVE_INDEX='N',STATUS='FORCE UNMATCH',"
									+ "UPDATED_ON=?,USER_ID=?,VERSION=?,RECON_ID=?,ACTIVITY_COMMENTS=?  WHERE ID="+rs.getObject("ID")+" AND RECON_SIDE!=?";*/
						    reconUpdateQuery="UPDATE "+reconTableName+" SET MATCH_TYPE='MU',WORKFLOW_STATUS='N',ACTIVE_INDEX='N',STATUS='FORCE UNMATCH',"
									+ "UPDATED_ON=?,USER_ID=?,VERSION=?,RECON_ID=?,ACTIVITY_COMMENTS=?  WHERE SID=? AND RECON_ID=? AND RECON_SIDE!=?";//Narendra Added
						}
					 reconDataUpdatePstmt=connection.prepareStatement(reconUpdateQuery);
						reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconDataUpdatePstmt.setObject(2, userId);
						reconDataUpdatePstmt.setObject(3, recversion);
						reconDataUpdatePstmt.setObject(4, start1+""+jj);
						reconDataUpdatePstmt.setObject(5, requesterComments);
						//reconDataUpdatePstmt.setObject(6, centrifugal);
						
						//Narendra Added
						reconDataUpdatePstmt.setObject(6, sid);
						reconDataUpdatePstmt.setObject(7, reconId);
						reconDataUpdatePstmt.setObject(8, centrifugal);
						//End
						
						int reconRow=reconDataUpdatePstmt.executeUpdate();
						
					}
					
					/*String reconActiveUpdateQuery="UPDATE "+reconTableName+" SET ACTIVE_INDEX='N'  WHERE RECON_ID=? and RECON_SIDE!=?";
					reconDataUpdatePstmt=connection.prepareStatement(reconUpdateQuery);
					reconDataUpdatePstmt.setObject(1, centrifugal);
					int activerow=reconDataUpdatePstmt.executeUpdate();*/
					}
					
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE UNMATCH OPERATION APPROVED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
					
				}catch(Exception e){
					e.printStackTrace();
				}finally{
		
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
					DbUtil.closePreparedStatement(reconDataUpdatePstmt);
				}
				
				
				
				
				
			} else if (REJECTED.equalsIgnoreCase(status)) {
				
		
				try{
			
					for(Map<String,Object> selectedRec:recordsList){
			
						 reconId=(Long) selectedRec.get("RECON_ID");
							int recversion=Integer.parseInt(selectedRec.get("VERSION").toString());
						reconDataSelectPstmt=connection.prepareStatement("select * from "+reconTableName+" where RECON_ID="+reconId);
					ResultSet rs=	reconDataSelectPstmt.executeQuery();
					
					while(rs.next()){
						String reconSide=(String) rs.getObject("RECON_SIDE");
						long sid=(long) rs.getObject("SID");
						String stgTableName=reconSide+"_STG";
						String auditTableName=reconSide+"_STG_AUDIT";
							
						//FINANCE ONS
						if(reconSide.equalsIgnoreCase("CBS") ){	
								stgTableName="FIN_ONS_CBS_STG";
								auditTableName="FIN_ONS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CBO") ){	
								stgTableName="FIN_ONS_CBO_STG";
								auditTableName="FIN_ONS_CBO_STG_AUDIT";
							}
						//FINANCE MPCLEAR
						else if(reconSide.equalsIgnoreCase("MP_CLEAR_CBS") ){	
								stgTableName="FIN_MP_CLEAR_CBS_STG";
								auditTableName="FIN_MP_CLEAR_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("EXT") ){	
								stgTableName="FIN_MP_CLEAR_EXT_STG";
								auditTableName="FIN_MP_CLEAR_EXT_STG_AUDIT";
							}
						//FINANCE -  SUSPENSE
						else if(reconSide.equalsIgnoreCase("FIN_SUSPENSE_DEBIT") || reconSide.equalsIgnoreCase("FIN_SUSPENSE_CREDIT")){	
								stgTableName="FIN_SUSPENSE_STG";
								auditTableName="FIN_SUSPENSE_STG_AUDIT";
							}
						//CARDS -  ATM MASTER CARD ACQUIRER
						else if(reconSide.equalsIgnoreCase("ATM_MC_CBS") ){	
								stgTableName="CARD_ATM_MC_CBS_STG";
								auditTableName="CARD_ATM_MC_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("ACQ") ){	
								stgTableName="CARD_ATM_MC_ACQ_STG";
								auditTableName="CARD_ATM_MC_ACQ_STG_AUDIT";
							}
						//CARDS -  ATM VISA ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_CBS") ){	
								stgTableName="CARD_ATM_VISA_ACQ_CBS_STG";
								auditTableName="CARD_ATM_VISA_ACQ_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ACQ_EXT") ){	
								stgTableName="CARD_ATM_VISA_ACQ_EXT_STG";
								auditTableName="CARD_ATM_VISA_ACQ_EXT_STG_AUDIT";
							}
						//CARDS -  POS NI ACQUIRER
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_CBS") ){	
								stgTableName="CARD_POSNI_CBS_STG";
								auditTableName="CARD_POSNI_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_POSNI_TRAN") ){	
								stgTableName="CARD_POSNI_TRAN_STG";
								auditTableName="CARD_POSNI_TRAN_STG_AUDIT";
							}
						//CARDS -  CREDIT CARD STATEMENT
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_CBS") ){	
								stgTableName="CREDIT_CARD_STATEMENT_CBS_STG";
								auditTableName="CREDIT_CARD_STATEMENT_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CREDIT_CARD_STATEMENT_TRAN") ){	
								stgTableName="CREDIT_CARD_STATEMENT_TRAN_STG";
								auditTableName="CREDIT_CARD_STATEMENT_TRAN_STG_AUDIT";
							}
						//CARDS -  ATM/POS VISA ISSUER
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_CBS") ){	
								stgTableName="CARD_ATM_VISA_ISS_CBS_STG";
								auditTableName="CARD_ATM_VISA_ISS_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_INCT") ){	
							stgTableName="CARD_ATM_VISA_ISS_INCT_STG";
							auditTableName="CARD_ATM_VISA_ISS_INCT_STG_AUDIT";
						}
						/*else if(reconSide.equalsIgnoreCase("CARD_ATM_VISA_ISS_PST") ){	
								stgTableName="CARD_ATM_VISA_ISS_PST_STG";
								auditTableName="CARD_ATM_VISA_ISS_PST_STG_AUDIT";
							}*/
						//CENTRAL OPERATION DEPARTMENT - CDM
						else if(reconSide.equalsIgnoreCase("CO_CDM_CBS") ){	
								stgTableName="CO_CDM_CBS_STG";
								auditTableName="CO_CDM_CBS_STG_AUDIT";
						}
						else if(reconSide.equalsIgnoreCase("CO_CDM_JOURNAL") ){	
								stgTableName="CO_CDM_JOURNAL_STG";
								auditTableName="CO_CDM_JOURNAL_STG_AUDIT";
						}
						//CENTRAL OPERATION DEPARTMENT - ATM TRANSACTIONS
						else if(reconSide.equalsIgnoreCase("CO_ATM_CBS") ){	
								stgTableName="CO_ATM_CBS_STG";
								auditTableName="CO_ATM_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ATM_JOURNAL") ){	
								stgTableName="CO_ATM_JOURNAL_STG";
								auditTableName="CO_ATM_JOURNAL_STG_AUDIT";
							}
						//CENTRAL OPERATION DEPARTMENT - ACH
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBS") ){	
								stgTableName="CO_ACH_CBS_STG";
								auditTableName="CO_ACH_CBS_STG_AUDIT";
							}
						else if(reconSide.equalsIgnoreCase("CO_ACH_CBO") ){	
								stgTableName="CO_ACH_CBO_STG";
								auditTableName="CO_ACH_CBO_STG_AUDIT";
							}
						
						//inserting STG to AUDIT
						stagingDataSelectPstmt=connection.prepareStatement("select * from "+stgTableName+" where SID="+sid);
						List<String> columnList=new ArrayList<String>();
						ResultSet stagingRs=stagingDataSelectPstmt.executeQuery();
						ResultSetMetaData rsm=stagingRs.getMetaData();
						int columnCount=rsm.getColumnCount();
						Map stagingDataMap=new HashMap();
						
						Query auditQuery=OperationsUtil.getInsertQueryConf(auditTableName, connection);
						auditDataInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
						long version=0;
						while(stagingRs.next()){
							version= Integer.parseInt(stagingRs.getObject("VERSION").toString());
							++version;
							for(int i=1;i<=columnCount;i++){
								columnList.add(rsm.getColumnName(i));
								stagingDataMap.put(rsm.getColumnName(i), stagingRs.getObject(rsm.getColumnName(i).toUpperCase()));
							}
						}
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", stagingDataMap);
						insertRegulator.insert(auditDataInsertPstmt, paramValueMap, auditQuery.getQueryParam());
						//inserting STG to AUDIT End
						
						
						//Updating STG table
						String updateQuery="UPDATE "+stgTableName+" SET WORKFLOW_STATUS='N',VERSION=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?  WHERE SID=?";
						stagingDataUpdatePstmt=connection.prepareStatement(updateQuery);
						stagingDataUpdatePstmt.setObject(1, version);
						stagingDataUpdatePstmt.setObject(2, requesterComments);
						stagingDataUpdatePstmt.setObject(3, new Timestamp(Calendar.getInstance().getTimeInMillis()));	
						stagingDataUpdatePstmt.setObject(4, sid);
					    int row=	stagingDataUpdatePstmt.executeUpdate();
					    //Updating STG table End
						
						
					}
					
					//Updating Recon table
					reconDataUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS='N',UPDATED_ON=?,VERSION=?,USER_ID=?,ACTIVITY_COMMENTS=? WHERE RECON_ID="+reconId);
					reconDataUpdatePstmt.setObject(1, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					reconDataUpdatePstmt.setObject(2, recversion);
					reconDataUpdatePstmt.setObject(3, userId);
					reconDataUpdatePstmt.setObject(4, requesterComments);
					reconDataUpdatePstmt.executeUpdate();
					//Updating Recon table End
						
					}
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, recordsList, "FORCE UNMATCH OPERATION REJECTED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
				}catch(Exception e){
					e.printStackTrace();
				}finally{
						
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(reconDataSelectPstmt);
					DbUtil.closePreparedStatement(stagingDataSelectPstmt);
					DbUtil.closePreparedStatement(auditDataInsertPstmt);
					DbUtil.closePreparedStatement(stagingDataUpdatePstmt);
					DbUtil.closePreparedStatement(reconDataUpdatePstmt);
				}
				
				
				
				
				
			} else if("PENDING".equalsIgnoreCase(status)){/*

				String tableName=integrationName+"_STG";
				String query="UPDATE "+tableName+" set ACTIVE_INDEX='Y',Status='APPROVED',WORKFLOW_STATUS='Y' where SID=?";
				PreparedStatement updateStmt=null;
				
				try{
					updateStmt=connection.prepareStatement(query);
					
				for(Map<String,Object> rec:records){
					updateStmt.setLong(1,(Long)rec.get(SID));
					updateStmt.addBatch();
				}
				updateStmt.executeUpdate();
				
				}catch(Exception e){
					e.printStackTrace();
				}
				
			
				updateResultStatus(result, FAILED, "Undefined Action");
			*/}

		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}

	private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,VERSION@BIGINT");
		
		
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				/*Long version= (Long) rec.get("VERSION");
				++version;
				auditRec.put("VERSION",version);*/
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);
				
				
				
				insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}
		
	}


}
