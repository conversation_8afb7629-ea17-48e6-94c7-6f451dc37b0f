package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class ATMVisaAcquirer {

	private static Logger logger = LogManager.getLogger(ATMVisaAcquirer.class.getName());

	
	private static final String ATM_VISA_ACQUIRER_INTERNAL_RECONCILED_RECON="ATM_VISA_ACQUIRER_INTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ACQUIRER_INTERNAL_UNRECONCILED_RECON="ATM_VISA_ACQUIRER_INTERNAL_UNRECONCILED_RECON";
	
	private static final String ATM_VISA_ACQUIRER_EXTERNAL_RECONCILED_RECON="ATM_VISA_ACQUIRER_EXTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ACQUIRER_EXTERNAL_UNRECONCILED_RECON="ATM_VISA_ACQUIRER_EXTERNAL_UNRECONCILED_RECON";
	
	private static final String ATM_VISA_ACQUIRER_INTERNAL_SUPPRESSED_RECON="ATM_VISA_ACQUIRER_INTERNAL_SUPPRESSED_RECON";
	private static final String ATM_VISA_ACQUIRER_EXTERNAL_SUPPRESSED_RECON="ATM_VISA_ACQUIRER_EXTERNAL_SUPPRESSED_RECON";
	
	private static final String ATM_VISA_ACQUIRER_AGING_RECON="ATM_VISA_ACQUIRER_AGING_RECON";
	
	private static final String ATMVISAACQ_INTERNAL_DRCR = "ATMVISAACQ_INTERNAL_DRCR";
	private static final String ATMVISAACQ_EXTERNAL_DRCR = "ATMVISAACQ_EXTERNAL_DRCR";
	
	private static final String ATMVISAACQ_RECONCILE_DRCR = "ATMVISAACQ_RECONCILE_DRCR";
	private static final String ATMVISAACQ_UNRECONCILE_DRCR = "ATMVISAACQ_UNRECONCILE_DRCR";
	
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>>getInternalReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("BRANCH ID", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("TRAN PARTICULAR", rset.getString(10));
				map.put("REFERENCE NUMBER", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN CRNCY CODE", rset.getString(13));
				map.put("REF CRNCY CODE", rset.getString(14));
				map.put("REF AMT", rset.getString(15));
				map.put("POSTED ON", rset.getString(16));
				map.put("POSTED BY", rset.getString(17));
				map.put("COMMENTS", rset.getString(18));
				map.put("VERSION", rset.getString(19));
				map.put("ACTIVE INDEX", rset.getString(20));
				map.put("WORKFLOW STATUS", rset.getString(21));
				map.put("UPDATED ON", rset.getString(22));
				map.put("CREATED ON", rset.getString(23));
				map.put("RECON STATUS", rset.getString(24));
				map.put("RECON ID", rset.getString(25));
				map.put("ACTIVE COMMENTS", rset.getString(26));
				map.put("MAIN REV IND", rset.getString(27));
				map.put("OPRATION", rset.getString(28));
				map.put("FILE NAME", rset.getString(29));
				map.put("BUSINESS AREA", rset.getString(30));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>>getInternalUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("BRANCH ID", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("TRAN PARTICULAR", rset.getString(10));
				map.put("REFERENCE NUMBER", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN CRNCY CODE", rset.getString(13));
				map.put("REF CRNCY CODE", rset.getString(14));
				map.put("REF AMT", rset.getString(15));
				map.put("POSTED ON", rset.getString(16));
				map.put("POSTED BY", rset.getString(17));
				map.put("COMMENTS", rset.getString(18));
				map.put("VERSION", rset.getString(19));
				map.put("ACTIVE INDEX", rset.getString(20));
				map.put("WORKFLOW STATUS", rset.getString(21));
				map.put("UPDATED ON", rset.getString(22));
				map.put("CREATED ON", rset.getString(23));
				map.put("RECON STATUS", rset.getString(24));
				map.put("RECON ID", rset.getString(25));
				map.put("ACTIVE COMMENTS", rset.getString(26));
				map.put("MAIN REV IND", rset.getString(27));
				map.put("OPRATION", rset.getString(28));
				map.put("FILE NAME", rset.getString(29));
				map.put("BUSINESS AREA", rset.getString(30));
				map.put("AGE", rset.getString(31));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	
	
	public List<Map<String, Object>>getInternalSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_INTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("BRANCH ID", rset.getString(3));
				map.put("TRAN ID", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("VALUE DATE", rset.getString(6));
				map.put("CUSTOMER ACCT", rset.getString(7));
				map.put("DRCR", rset.getString(8));
				map.put("AMOUNT", rset.getString(9));
				map.put("TRAN PARTICULAR", rset.getString(10));
				map.put("REFERENCE NUMBER", rset.getString(11));
				map.put("TRAN REMARKS", rset.getString(12));
				map.put("TRAN CRNCY CODE", rset.getString(13));
				map.put("REF CRNCY CODE", rset.getString(14));
				map.put("REF AMT", rset.getString(15));
				map.put("POSTED ON", rset.getString(16));
				map.put("POSTED BY", rset.getString(17));
				map.put("COMMENTS", rset.getString(18));
				map.put("VERSION", rset.getString(19));
				map.put("ACTIVE INDEX", rset.getString(20));
				map.put("WORKFLOW STATUS", rset.getString(21));
				map.put("UPDATED ON", rset.getString(22));
				map.put("CREATED ON", rset.getString(23));
				map.put("RECON STATUS", rset.getString(24));
				map.put("RECON ID", rset.getString(25));
				map.put("ACTIVE COMMENTS", rset.getString(26));
				map.put("MAIN REV IND", rset.getString(27));
				map.put("OPRATION", rset.getString(28));
				map.put("FILE NAME", rset.getString(29));
				map.put("BUSINESS AREA", rset.getString(30));
				list.add(map);
				
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	
	public List<Map<String, Object>>getExternalUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BAT NUM", rset.getString(2));
				map.put("XMIT DATE", rset.getString(3));				
				map.put("TIME", rset.getString(4));
				map.put("CARD NUMBER", rset.getString(5));
				map.put("RETRIEVAL REF NUMBER", rset.getString(6));
				map.put("TRACE NUMBER", rset.getString(7));
				map.put("ISSUER TRMNL", rset.getString(8));
				map.put("TRAN TYPE", rset.getString(9));
				map.put("PROCSS CODE", rset.getString(10));
				map.put("ENT MOD", rset.getString(11));
				map.put("CN STP", rset.getString(12));
				map.put("RSP CD", rset.getString(13));
				map.put("REAS CODE", rset.getString(14));
				map.put("TRANSACTION AMOUNT", rset.getString(15));
				map.put("KES CURRENCY", rset.getString(16));
				map.put("SETTLEMENT AMOUNT", rset.getString(17));
				map.put("CR CURRENCY", rset.getString(18));
				map.put("CA ID", rset.getString(19));
				map.put("TERMINAL ID", rset.getString(20));
				map.put("FPI", rset.getString(21));
				map.put("CI", rset.getString(22));
				map.put("REPORT DATE", rset.getString(23));
				map.put("TR ID", rset.getString(24));
				map.put("ACI", rset.getString(25));
				map.put("FEE JURIS", rset.getString(26));
				map.put("ROUTING", rset.getString(27));
				map.put("VERSION", rset.getString(28));
				map.put("ACTIVE INDEX", rset.getString(29));
				map.put("STATUS", rset.getString(30));
				map.put("COMMENTS", rset.getString(31));
				map.put("WORKFLOW STATUS", rset.getString(32));
				map.put("USER ID", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("UPDATED ON", rset.getString(35));
				if(rset.getString(36)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(36));
				map.put("RECON ID", rset.getString(37));
				map.put("MAIN REV IND", rset.getString(38));
				map.put("CREATED ON", rset.getString(39));
				map.put("PROC CODE", rset.getString(40));
				map.put("FILENAME", rset.getString(41));
				map.put("ACTIVITY COMMENTS", rset.getString(42));
				map.put("AGE", rset.getString(43));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	public List<Map<String, Object>>getExternalReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BAT NUM", rset.getString(2));
				map.put("XMIT DATE", rset.getString(3));				
				map.put("TIME", rset.getString(4));
				map.put("CARD NUMBER", rset.getString(5));
				map.put("RETRIEVAL REF NUMBER", rset.getString(6));
				map.put("TRACE NUMBER", rset.getString(7));
				map.put("ISSUER TRMNL", rset.getString(8));
				map.put("TRAN TYPE", rset.getString(9));
				map.put("PROCSS CODE", rset.getString(10));
				map.put("ENT MOD", rset.getString(11));
				map.put("CN STP", rset.getString(12));
				map.put("RSP CD", rset.getString(13));
				map.put("REAS CODE", rset.getString(14));
				map.put("TRANSACTION AMOUNT", rset.getString(15));
				map.put("KES CURRENCY", rset.getString(16));
				map.put("SETTLEMENT AMOUNT", rset.getString(17));
				map.put("CR CURRENCY", rset.getString(18));
				map.put("CA ID", rset.getString(19));
				map.put("TERMINAL ID", rset.getString(20));
				map.put("FPI", rset.getString(21));
				map.put("CI", rset.getString(22));
				map.put("REPORT DATE", rset.getString(23));
				map.put("TR ID", rset.getString(24));
				map.put("ACI", rset.getString(25));
				map.put("FEE JURIS", rset.getString(26));
				map.put("ROUTING", rset.getString(27));
				map.put("VERSION", rset.getString(28));
				map.put("ACTIVE INDEX", rset.getString(29));
				map.put("STATUS", rset.getString(30));
				map.put("COMMENTS", rset.getString(31));
				map.put("WORKFLOW STATUS", rset.getString(32));
				map.put("USER ID", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("UPDATED ON", rset.getString(35));
				map.put("RECON STATUS", rset.getString(36));
				map.put("RECON ID", rset.getString(37));
				map.put("MAIN REV IND", rset.getString(38));
				map.put("CREATED ON", rset.getString(39));
				map.put("PROC CODE", rset.getString(40));
				map.put("FILENAME", rset.getString(41));
				map.put("ACTIVITY COMMENTS", rset.getString(42));
		
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	public List<Map<String, Object>>getExternalSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_EXTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("BAT NUM", rset.getString(2));
				map.put("XMIT DATE", rset.getString(3));				
				map.put("TIME", rset.getString(4));
				map.put("CARD NUMBER", rset.getString(5));
				map.put("RETRIEVAL REF NUMBER", rset.getString(6));
				map.put("TRACE NUMBER", rset.getString(7));
				map.put("ISSUER TRMNL", rset.getString(8));
				map.put("TRAN TYPE", rset.getString(9));
				map.put("PROCSS CODE", rset.getString(10));
				map.put("ENT MOD", rset.getString(11));
				map.put("CN STP", rset.getString(12));
				map.put("RSP CD", rset.getString(13));
				map.put("REAS CODE", rset.getString(14));
				map.put("TRANSACTION AMOUNT", rset.getString(15));
				map.put("KES CURRENCY", rset.getString(16));
				map.put("SETTLEMENT AMOUNT", rset.getString(17));
				map.put("CR CURRENCY", rset.getString(18));
				map.put("CA ID", rset.getString(19));
				map.put("TERMINAL ID", rset.getString(20));
				map.put("FPI", rset.getString(21));
				map.put("CI", rset.getString(22));
				map.put("REPORT DATE", rset.getString(23));
				map.put("TR ID", rset.getString(24));
				map.put("ACI", rset.getString(25));
				map.put("FEE JURIS", rset.getString(26));
				map.put("ROUTING", rset.getString(27));
				map.put("VERSION", rset.getString(28));
				map.put("ACTIVE INDEX", rset.getString(29));
				map.put("STATUS", rset.getString(30));
				map.put("COMMENTS", rset.getString(31));
				map.put("WORKFLOW STATUS", rset.getString(32));
				map.put("USER ID", rset.getString(33));
				map.put("OPERATION", rset.getString(34));
				map.put("UPDATED ON", rset.getString(35));
				map.put("RECON STATUS", rset.getString(36));
				map.put("RECON ID", rset.getString(37));
				map.put("MAIN REV IND", rset.getString(38));
				map.put("CREATED ON", rset.getString(39));
				map.put("PROC CODE", rset.getString(40));
				map.put("FILENAME", rset.getString(41));
				map.put("ACTIVITY COMMENTS", rset.getString(42));
		
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> AtmVisaAcquireAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ACQUIRER_AGING_RECON );
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> atmviaacqInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMVISAACQ_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmvisaacqExternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMVISAACQ_EXTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmviaacqReconcileDrcr(String fromDate, String toDate) { 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMVISAACQ_RECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> atmvisaacqUnreconcileDrcr(String fromDate, String toDate) { 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATMVISAACQ_UNRECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public static void main(String[] args) {
		ATMVisaAcquirer c = new ATMVisaAcquirer();
		c.getExternalReconciledData("2015-01-01", "2019-01-01");
		c.getExternalSuppressedData("2015-01-01", "2019-01-01");
		c.getExternalUnReconciledData("2015-01-01", "2019-01-01");
		c.getInternalReconciledData("2015-01-01", "2019-01-01");
		c.getInternalSuppressedData("2015-01-01", "2019-01-01");
		c.getInternalUnReconciledData("2015-01-01", "2019-01-01");
		c.AtmVisaAcquireAgingMethod();
		c.atmviaacqInternalDrcr("2015-01-01", "2019-01-01");
		c.atmvisaacqExternalDrcr("2015-01-01", "2019-01-01");
		
		
		
		
	
	}

}
