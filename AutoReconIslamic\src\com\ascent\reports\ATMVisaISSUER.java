package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class ATMVisaISSUER {

	private static Logger logger = LogManager.getLogger(ATMVisaISSUER.class.getName());

	
	private static final String ATM_VISA_ISSUER1_INTERNAL_RECONCILED_RECON="ATM_VISA_ISSUER1_INTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ISSUER1_INTERNAL_UNRECONCILED_RECON="ATM_VISA_ISSUER1_INTERNAL_UNRECONCILED_RECON";
	private static final String ATM_VISA_ISSUER1_EXTERNAL_RECONCILED_RECON="ATM_VISA_ISSUER1_EXTERNAL_RECONCILED_RECON";
	private static final String ATM_VISA_ISSUER1_EXTERNAL_UNRECONCILED_RECON="ATM_VISA_ISSUER1_EXTERNAL_UNRECONCILED_RECON";
	private static final String ATM_VISA_ISSUER1_INTERNAL_SUPPRESSED_RECON="ATM_VISA_ISSUER1_INTERNAL_SUPPRESSED_RECON";
	private static final String ATM_VISA_ISSUER1_EXTERNAL_SUPPRESSED_RECON="ATM_VISA_ISSUER1_EXTERNAL_SUPPRESSED_RECON";
	private static final String ATM_VISA_ISSUER1_AGING_RECON="ATM_VISA_ISSUER1_AGING_RECON";
	private static final String ATM_VISA_ISSUER1_INTERNAL_DRCR = "ATM_VISA_ISSUER1_INTERNAL_DRCR";
	
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {

		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>>getInternalatmissuerReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ISSUER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SRL NUM", rset.getString(2));
				map.put("AUTH CODE", rset.getString(3));
				map.put("TRAN REF NUM", rset.getString(4));
				map.put("INTERNAL REF NUM", rset.getString(5));
				map.put("ACCT NUM", rset.getString(6));
				map.put("TRAN DATE", rset.getString(7));
				map.put("VALUE DATE", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("DRCR", rset.getString(10));
				map.put("CURRENCY", rset.getString(11));
				map.put("CARD NUMBER", rset.getString(12));
				map.put("ACCT BRANCH ID", rset.getString(13));
				map.put("TRAN PARTICULAR", rset.getString(14));
				map.put("TRAN REMARKS", rset.getString(15));
				map.put("TRAN ENTRY USER", rset.getString(16));
				map.put("TRAN POSTED USER", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVE COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPRATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				map.put("VALUE DATE", rset.getString(29));
				list.add(map);
				
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>>getInternalatmissuerUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SRL NUM", rset.getString(2));
				map.put("AUTH CODE", rset.getString(3));
				map.put("TRAN REF NUM", rset.getString(4));
				map.put("INTERNAL REF NUM", rset.getString(5));
				map.put("ACCT NUM", rset.getString(6));
				map.put("TRAN DATE", rset.getString(7));
				map.put("VALUE DATE", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("DRCR", rset.getString(10));
				map.put("CURRENCY", rset.getString(11));
				map.put("CARD NUMBER", rset.getString(12));
				map.put("ACCT BRANCH ID", rset.getString(13));
				map.put("TRAN PARTICULAR", rset.getString(14));
				map.put("TRAN REMARKS", rset.getString(15));
				map.put("TRAN ENTRY USER", rset.getString(16));
				map.put("TRAN POSTED USER", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVE COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPRATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				map.put("AGE", rset.getString(29));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	
	
	public List<Map<String, Object>>getInternalatmissuerSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_INTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("SRL NUM", rset.getString(2));
				map.put("AUTH CODE", rset.getString(3));
				map.put("TRAN REF NUM", rset.getString(4));
				map.put("INTERNAL REF NUM", rset.getString(5));
				map.put("ACCT NUM", rset.getString(6));
				map.put("TRAN DATE", rset.getString(7));
				map.put("VALUE DATE", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("DRCR", rset.getString(10));
				map.put("CURRENCY", rset.getString(11));
				map.put("CARD NUMBER", rset.getString(12));
				map.put("ACCT BRANCH ID", rset.getString(13));
				map.put("TRAN PARTICULAR", rset.getString(14));
				map.put("TRAN REMARKS", rset.getString(15));
				map.put("TRAN ENTRY USER", rset.getString(16));
				map.put("TRAN POSTED USER", rset.getString(17));
				map.put("VERSION", rset.getString(18));
				map.put("ACTIVE INDEX", rset.getString(19));
				map.put("WORKFLOW STATUS", rset.getString(20));
				map.put("UPDATED ON", rset.getString(21));
				map.put("CREATED ON", rset.getString(22));
				map.put("RECON STATUS", rset.getString(23));
				map.put("RECON ID", rset.getString(24));
				map.put("ACTIVE COMMENTS", rset.getString(25));
				map.put("MAIN REV IND", rset.getString(26));
				map.put("OPRATION", rset.getString(27));
				map.put("BUSINESS AREA", rset.getString(28));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	
	public List<Map<String, Object>>getExternalatmissuerUnReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ISSUER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("Tran Code", rset.getString(2));
				map.put("Acct Num PAN", rset.getString(3));				
				map.put("ARN", rset.getString(4));
				map.put("Purchase Date", rset.getString(5));
				map.put("Dest Amt", rset.getString(6));
				map.put("Dest Curr", rset.getString(7));
				map.put("Source Amt", rset.getString(8));
				map.put("Source Curr", rset.getString(9));
				map.put("Mer Cat Code", rset.getString(10));
				map.put("Usage Code", rset.getString(11));
				map.put("Auth Code", rset.getString(12));
				map.put("Pos Enty Mode", rset.getString(13));
				map.put("acq bus id", rset.getString(14));
				map.put("acq workstn bin", rset.getString(15));
				map.put("atm acct selection", rset.getString(16));
				map.put("auth char ind", rset.getString(17));
				map.put("auth source code", rset.getString(18));
				map.put("avs resp code", rset.getString(19));
				map.put("card accpt id", rset.getString(20));
				map.put("card id method", rset.getString(21));
				map.put("cardhold act term ind", rset.getString(22));
				map.put("cashback", rset.getString(23));
				map.put("central pr date", rset.getString(24));
				map.put("chgbk ref numb", rset.getString(25));
				map.put("chip cond code", rset.getString(26));
				map.put("coll only flag", rset.getString(27));
				map.put("crb excp", rset.getString(28));
				map.put("doc ind", rset.getString(29));
				map.put("floor limit", rset.getString(30));
				map.put("install paymt count", rset.getString(31));
				map.put("interface trace numb", rset.getString(32));
				map.put("intl fee ind", rset.getString(33));
				map.put("issuer workstn bin", rset.getString(34));
				map.put("mail tel ecom ind", rset.getString(35));
				map.put("memb mesg text", rset.getString(36));
				map.put("mer city", rset.getString(37));
				map.put("mer cntry", rset.getString(38));
				map.put("mer name", rset.getString(39));
				map.put("mer state code", rset.getString(40));
				map.put("mer zip code", rset.getString(41));
				map.put("national reimb fee", rset.getString(42));
				map.put("pan extn", rset.getString(43));				
				map.put("pcas", rset.getString(44));
				map.put("pos term cap", rset.getString(45));
				map.put("prepaid card ind", rset.getString(46));
				map.put("purch year", rset.getString(47));
				map.put("purchase id", rset.getString(48));
				map.put("purchase id format", rset.getString(49));
				map.put("reason code", rset.getString(50));
				map.put("reimb attrib", rset.getString(51));
				map.put("req payment", rset.getString(52));
				map.put("reserved tcr1 1", rset.getString(53));
				map.put("reserved tcr1 2", rset.getString(54));
				map.put("service devlp field", rset.getString(55));
				map.put("settl flag", rset.getString(56));
				map.put("spcl chgbk ind", rset.getString(57));
				map.put("spcl cond ind", rset.getString(58));
				map.put("tcr numb", rset.getString(59));
				map.put("term id", rset.getString(60));
				map.put("tran code qualifier", rset.getString(61));
				map.put("map code", rset.getString(62));
				map.put("recon flag", rset.getString(63));
				map.put("proc date", rset.getString(64));
				map.put("ctf file", rset.getString(65));
				map.put("RECON ID", rset.getString(66));
				map.put("UPDATED ON", rset.getString(67));
				map.put("VERSION", rset.getString(68));
				map.put("bin", rset.getString(69));
				map.put("ACTIVE INDEX", rset.getString(70));
				map.put("STATUS", rset.getString(71));
				map.put("WORKFLOW STATUS", rset.getString(72));
				map.put("USER ID", rset.getString(73));				
				map.put("OPERATION", rset.getString(74));
				if(rset.getString(75)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(75));
				map.put("CREATED ON", rset.getString(76));
				map.put("MAIN REV IND", rset.getString(77));
				map.put("ACTIVITY COMMENTS", rset.getString(78));
				map.put("TRAN CUR", rset.getString(79));
				map.put("AGE", rset.getString(80));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	public List<Map<String, Object>>getExternalatmissuerReconciledData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("Tran Code", rset.getString(2));
				map.put("Acct Num PAN", rset.getString(3));				
				map.put("ARN", rset.getString(4));
				map.put("Purchase Date", rset.getString(5));
				map.put("Dest Amt", rset.getString(6));
				map.put("Dest Curr", rset.getString(7));
				map.put("Source Amt", rset.getString(8));
				map.put("Source Curr", rset.getString(9));
				map.put("Mer Cat Code", rset.getString(10));
				map.put("Usage Code", rset.getString(11));
				map.put("Auth Code", rset.getString(12));
				map.put("Pos Enty Mode", rset.getString(13));
				map.put("acq bus id", rset.getString(14));
				map.put("acq workstn bin", rset.getString(15));
				map.put("atm acct selection", rset.getString(16));
				map.put("auth char ind", rset.getString(17));
				map.put("auth source code", rset.getString(18));
				map.put("avs resp code", rset.getString(19));
				map.put("card accpt id", rset.getString(20));
				map.put("card id method", rset.getString(21));
				map.put("cardhold act term ind", rset.getString(22));
				map.put("cashback", rset.getString(23));
				map.put("central pr date", rset.getString(24));
				map.put("chgbk ref numb", rset.getString(25));
				map.put("chip cond code", rset.getString(26));
				map.put("coll only flag", rset.getString(27));
				map.put("crb excp", rset.getString(28));
				map.put("doc ind", rset.getString(29));
				map.put("floor limit", rset.getString(30));
				map.put("install paymt count", rset.getString(31));
				map.put("interface trace numb", rset.getString(32));
				map.put("intl fee ind", rset.getString(33));
				map.put("issuer workstn bin", rset.getString(34));
				map.put("mail tel ecom ind", rset.getString(35));
				map.put("memb mesg text", rset.getString(36));
				map.put("mer city", rset.getString(37));
				map.put("mer cntry", rset.getString(38));
				map.put("mer name", rset.getString(39));
				map.put("mer state code", rset.getString(40));
				map.put("mer zip code", rset.getString(41));
				map.put("national reimb fee", rset.getString(42));
				map.put("pan extn", rset.getString(43));				
				map.put("pcas", rset.getString(44));
				map.put("pos term cap", rset.getString(45));
				map.put("prepaid card ind", rset.getString(46));
				map.put("purch year", rset.getString(47));
				map.put("purchase id", rset.getString(48));
				map.put("purchase id format", rset.getString(49));
				map.put("reason code", rset.getString(50));
				map.put("reimb attrib", rset.getString(51));
				map.put("req payment", rset.getString(52));
				map.put("reserved tcr1 1", rset.getString(53));
				map.put("reserved tcr1 2", rset.getString(54));
				map.put("service devlp field", rset.getString(55));
				map.put("settl flag", rset.getString(56));
				map.put("spcl chgbk ind", rset.getString(57));
				map.put("spcl cond ind", rset.getString(58));
				map.put("tcr numb", rset.getString(59));
				map.put("term id", rset.getString(60));
				map.put("tran code qualifier", rset.getString(61));
				map.put("map code", rset.getString(62));
				map.put("recon flag", rset.getString(63));
				map.put("proc date", rset.getString(64));
				map.put("ctf file", rset.getString(65));
				map.put("RECON ID", rset.getString(66));
				map.put("UPDATED ON", rset.getString(67));
				map.put("VERSION", rset.getString(68));
				map.put("bin", rset.getString(69));
				map.put("ACTIVE INDEX", rset.getString(70));
				map.put("STATUS", rset.getString(71));
				map.put("WORKFLOW STATUS", rset.getString(72));
				map.put("USER ID", rset.getString(73));				
				map.put("OPERATION", rset.getString(74));
				map.put("RECON STATUS", rset.getString(75));
				map.put("CREATED ON", rset.getString(76));
				map.put("MAIN REV IND", rset.getString(77));
				map.put("ACTIVITY COMMENTS", rset.getString(78));
				map.put("TRAN CUR", rset.getString(79));
				
		
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	
	public List<Map<String, Object>>getExternalatmissuerSuppressedData(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching Internal data for ATM VISA ACQUIRER INTERNAL RECON..");
		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_EXTERNAL_SUPPRESSED_RECON);
			String query = queryConf.getQueryString();
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("Tran Code", rset.getString(2));
				map.put("Acct Num PAN", rset.getString(3));				
				map.put("ARN", rset.getString(4));
				map.put("Purchase Date", rset.getString(5));
				map.put("Dest Amt", rset.getString(6));
				map.put("Dest Curr", rset.getString(7));
				map.put("Source Amt", rset.getString(8));
				map.put("Source Curr", rset.getString(9));
				map.put("Mer Cat Code", rset.getString(10));
				map.put("Usage Code", rset.getString(11));
				map.put("Auth Code", rset.getString(12));
				map.put("Pos Enty Mode", rset.getString(13));
				map.put("acq bus id", rset.getString(14));
				map.put("acq workstn bin", rset.getString(15));
				map.put("atm acct selection", rset.getString(16));
				map.put("auth char ind", rset.getString(17));
				map.put("auth source code", rset.getString(18));
				map.put("avs resp code", rset.getString(19));
				map.put("card accpt id", rset.getString(20));
				map.put("card id method", rset.getString(21));
				map.put("cardhold act term ind", rset.getString(22));
				map.put("cashback", rset.getString(23));
				map.put("central pr date", rset.getString(24));
				map.put("chgbk ref numb", rset.getString(25));
				map.put("chip cond code", rset.getString(26));
				map.put("coll only flag", rset.getString(27));
				map.put("crb excp", rset.getString(28));
				map.put("doc ind", rset.getString(29));
				map.put("floor limit", rset.getString(30));
				map.put("install paymt count", rset.getString(31));
				map.put("interface trace numb", rset.getString(32));
				map.put("intl fee ind", rset.getString(33));
				map.put("issuer workstn bin", rset.getString(34));
				map.put("mail tel ecom ind", rset.getString(35));
				map.put("memb mesg text", rset.getString(36));
				map.put("mer city", rset.getString(37));
				map.put("mer cntry", rset.getString(38));
				map.put("mer name", rset.getString(39));
				map.put("mer state code", rset.getString(40));
				map.put("mer zip code", rset.getString(41));
				map.put("national reimb fee", rset.getString(42));
				map.put("pan extn", rset.getString(43));				
				map.put("pcas", rset.getString(44));
				map.put("pos term cap", rset.getString(45));
				map.put("prepaid card ind", rset.getString(46));
				map.put("purch year", rset.getString(47));
				map.put("purchase id", rset.getString(48));
				map.put("purchase id format", rset.getString(49));
				map.put("reason code", rset.getString(50));
				map.put("reimb attrib", rset.getString(51));
				map.put("req payment", rset.getString(52));
				map.put("reserved tcr1 1", rset.getString(53));
				map.put("reserved tcr1 2", rset.getString(54));
				map.put("service devlp field", rset.getString(55));
				map.put("settl flag", rset.getString(56));
				map.put("spcl chgbk ind", rset.getString(57));
				map.put("spcl cond ind", rset.getString(58));
				map.put("tcr numb", rset.getString(59));
				map.put("term id", rset.getString(60));
				map.put("tran code qualifier", rset.getString(61));
				map.put("map code", rset.getString(62));
				map.put("recon flag", rset.getString(63));
				map.put("proc date", rset.getString(64));
				map.put("ctf file", rset.getString(65));
				map.put("RECON ID", rset.getString(66));
				map.put("UPDATED ON", rset.getString(67));
				map.put("VERSION", rset.getString(68));
				map.put("bin", rset.getString(69));
				map.put("ACTIVE INDEX", rset.getString(70));
				map.put("STATUS", rset.getString(71));
				map.put("WORKFLOW STATUS", rset.getString(72));
				map.put("USER ID", rset.getString(73));				
				map.put("OPERATION", rset.getString(74));
				map.put("RECON STATUS", rset.getString(75));
				map.put("CREATED ON", rset.getString(76));
				map.put("MAIN REV IND", rset.getString(77));
				map.put("ACTIVITY COMMENTS", rset.getString(78));
				map.put("TRAN CUR", rset.getString(79));
				
		
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> AtmVisaIssuierAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_AGING_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("TOTAL AMOUNT", 0);
					else
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> atmvisaissuerInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ATM_VISA_ISSUER1_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	
	
	public static void main(String[] args) {
		ATMVisaISSUER c = new ATMVisaISSUER();
		c.getExternalatmissuerReconciledData("2015-01-01", "2019-01-01");
		c.getExternalatmissuerSuppressedData("2015-01-01", "2019-01-01");
		c.getExternalatmissuerUnReconciledData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerReconciledData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerSuppressedData("2015-01-01", "2019-01-01");
		c.getInternalatmissuerUnReconciledData("2015-01-01", "2019-01-01");
		c.AtmVisaIssuierAgingMethod();
	c.atmvisaissuerInternalDrcr("2015-01-01", "2019-01-01");
		
		
	
	}

}
