package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

/**
 * <AUTHOR>
 *
 */
public class POSNIACQUIRER {

	private static final String POSNI_ACQUIRER_INTERNAL_RECONCILE_REPORT = "POSNI_ACQUIRER_INTERNAL_RECONCILE_REPORT";
	private static final String POSNI_ACQUIRER_INTERNAL_UNRECONCILE_REPORT = "POSNI_ACQUIRER_INTERNAL_UNRECONCILE_REPORT";

	private static final String POSNI_ACQUIRER_EXTERNAL_RECONCILE_REPORT = "POSNI_ACQUIRER_EXTERNAL_RECONCILE_REPORT";
	private static final String POSNI_ACQUIRER_EXTERNAL_UNRECONCILE_REPORT = "POSNI_ACQUIRER_EXTERNAL_UNRECONCILE_REPORT";

	private static final String POSNI_ACQUIRER_SUPPRESS_INTERNAL = "POSNI_ACQUIRER_SUPPRESS_INTERNAL";
	private static final String POSNI_ACQUIRER_SUPPRESS_EXTERNAL = "POSNI_ACQUIRER_SUPPRESS_EXTERNAL";
	private static final String POSNI_ACQUIRER_AGING = "POSNI_ACQUIRER_AGING";
	
	private static final String POSNI_INTERNAL_DRCR = "POSNI_INTERNAL_DRCR";
	private static final String POSNI_RECONCILE_DRCR = "POSNI_RECONCILE_DRCR";
	private static final String POSNI_UNRECONCILE_DRCR = "POSNI_UNRECONCILE_DRCR";

	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> posniAcquirerInternalReconcile(String fromDate, String toDate) {
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(POSNI_ACQUIRER_INTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("VALUE DATE", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("REF NUM", rset.getString(6));
				map.put("TRAN ID", rset.getString(7));
				map.put("TRAN AMT", rset.getString(8));
				map.put("PART TRAN TYPE", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("PSTD FLG", rset.getString(12));
				map.put("PSTD DATE", rset.getString(13));
				map.put("ACTIVE INDEX", rset.getString(14));
				map.put("WORKFLOW STATUS", rset.getString(15));
				map.put("UPDATED ON", rset.getString(16));
				map.put("CREATED ON", rset.getString(17));
				map.put("RECON STATUS", rset.getString(18));
				map.put("RECON ID", rset.getString(19));
				map.put("ACTIVITY COMMENTS", rset.getString(20));
				map.put("MAIN REV IND", rset.getString(21));
				map.put("OPERATION", rset.getString(22));
				map.put("BUSINESS AREA", rset.getString(23));
				map.put("VERSION", rset.getString(24));

				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> posniAcquirerInternalUnReconcile(String fromDate, String toDate) {
		
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(POSNI_ACQUIRER_INTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("VALUE DATE", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("REF NUM", rset.getString(6));
				map.put("TRAN ID", rset.getString(7));
				map.put("TRAN AMT", rset.getString(8));
				map.put("PART TRAN TYPE", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("PSTD FLG", rset.getString(12));
				map.put("PSTD DATE", rset.getString(13));
				map.put("ACTIVE INDEX", rset.getString(14));
				map.put("WORKFLOW STATUS", rset.getString(15));
				map.put("UPDATED ON", rset.getString(16));
				map.put("CREATED ON", rset.getString(17));
				map.put("RECON STATUS", rset.getString(18));
				map.put("RECON ID", rset.getString(19));
				map.put("ACTIVITY COMMENTS", rset.getString(20));
				map.put("MAIN REV IND", rset.getString(21));
				map.put("OPERATION", rset.getString(22));
				map.put("BUSINESS AREA", rset.getString(23));
				map.put("VERSION", rset.getString(24));
				map.put("AGE", rset.getString(25));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> posniAcquirerExternalReconcile(String fromDate, String toDate) { // Method
																										// for
																										// ExternalReconcile
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(POSNI_ACQUIRER_EXTERNAL_RECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("FILE NAME", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("UPLOAD DATE", rset.getString(6));
				map.put("REF NUM", rset.getString(7));
				map.put("CARD NO", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("MERCHANT ID", rset.getString(10));
				map.put("MERCHANT NAME", rset.getString(11));
				map.put("LOCATION", rset.getString(12));
				map.put("TRAN CRNCY", rset.getString(13));
				map.put("TRAN TYPE", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("BUSINESS AREA", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				/* map.put("AGE", rset.getString(17)); */
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> posniAcquirerExternalUnReconcile(String fromDate, String toDate) { // Method
																										// for
																										// ExternalUnReconcile
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs()
					.getQueryConf(POSNI_ACQUIRER_EXTERNAL_UNRECONCILE_REPORT);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("FILE NAME", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("UPLOAD DATE", rset.getString(6));
				map.put("REF NUM", rset.getString(7));
				map.put("CARD NO", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("MERCHANT ID", rset.getString(10));
				map.put("MERCHANT NAME", rset.getString(11));
				map.put("LOCATION", rset.getString(12));
				map.put("TRAN CRNCY", rset.getString(13));
				map.put("TRAN TYPE", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				if(rset.getString(19)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("BUSINESS AREA", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				map.put("AGE", rset.getString(26));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> posniAcquirerSuppressInternal(String fromDate, String toDate) { // Method for Suppress Internal
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_ACQUIRER_SUPPRESS_INTERNAL);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("VALUE DATE", rset.getString(4));
				map.put("TRAN DATE", rset.getString(5));
				map.put("REF NUM", rset.getString(6));
				map.put("TRAN ID", rset.getString(7));
				map.put("TRAN AMT", rset.getString(8));
				map.put("PART TRAN TYPE", rset.getString(9));
				map.put("TRAN RMKS", rset.getString(10));
				map.put("TRAN PARTICULAR", rset.getString(11));
				map.put("PSTD FLG", rset.getString(12));
				map.put("PSTD DATE", rset.getString(13));
				map.put("ACTIVE INDEX", rset.getString(14));
				map.put("WORKFLOW STATUS", rset.getString(15));
				map.put("UPDATED ON", rset.getString(16));
				map.put("CREATED ON", rset.getString(17));
				map.put("RECON STATUS", rset.getString(18));
				map.put("RECON ID", rset.getString(19));
				map.put("ACTIVITY COMMENTS", rset.getString(20));
				map.put("MAIN REV IND", rset.getString(21));
				map.put("OPERATION", rset.getString(22));
				map.put("BUSINESS AREA", rset.getString(23));
				map.put("VERSION", rset.getString(24));

				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> posniAcquirerSuppressExternal(String fromDate, String toDate) { // Method for Suppress External
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_ACQUIRER_SUPPRESS_EXTERNAL);
			String query = queryConf.getQueryString();
			// logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("ACCT NUM", rset.getString(2));
				map.put("CARD TYPE", rset.getString(3));
				map.put("FILE NAME", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("UPLOAD DATE", rset.getString(6));
				map.put("REF NUM", rset.getString(7));
				map.put("CARD NO", rset.getString(8));
				map.put("TRAN AMT", rset.getString(9));
				map.put("MERCHANT ID", rset.getString(10));
				map.put("MERCHANT NAME", rset.getString(11));
				map.put("LOCATION", rset.getString(12));
				map.put("TRAN CRNCY", rset.getString(13));
				map.put("TRAN TYPE", rset.getString(14));
				map.put("ACTIVE INDEX", rset.getString(15));
				map.put("WORKFLOW STATUS", rset.getString(16));
				map.put("UPDATED ON", rset.getString(17));
				map.put("CREATED ON", rset.getString(18));
				map.put("RECON STATUS", rset.getString(19));
				map.put("RECON ID", rset.getString(20));
				map.put("ACTIVITY COMMENTS", rset.getString(21));
				map.put("MAIN REV IND", rset.getString(22));
				map.put("OPERATION", rset.getString(23));
				map.put("BUSINESS AREA", rset.getString(24));
				map.put("VERSION", rset.getString(25));
				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> PosniAgingMethod() {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		//logger.debug("Fetching OnsSummry data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_ACQUIRER_AGING);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("TOTAL TRANS", rset.getString(2));
				map.put("TOTAL AMOUNT", rset.getString(3));
			    map.put("TOTAL_TRANS_0_3", rset.getString(4));
			    
			    if(rset.getString(5)==null)
					map.put("TOTAL_AMOUNT_0_3", 0);
				else
					map.put("TOTAL_AMOUNT_0_3", rset.getString(5));

			    map.put("TOTAL_TRANS_4_6", rset.getString(6));
				
			    if(rset.getString(7)==null)
					 map.put( "TOTAL_AMOUNT_4_6", 0);
				else
					map.put("TOTAL_AMOUNT_4_6", rset.getString(7));
			    
				map.put("TOTAL_TRANS_11_15", rset.getString(8));
				
				 if(rset.getString(9)==null)
					map.put("TOTAL_AMOUNT_11_15", 0);
				 else
					map.put("TOTAL_AMOUNT_11_15", rset.getString(9));
				 
				map.put("TOTAL_TRANS_16_30", rset.getString(10));
				
				 if(rset.getString(11)==null)
					map.put("TOTAL_AMOUNT_16_30", 0);
				 else
					map.put("TOTAL_AMOUNT_16_30", rset.getString(11));
				
				 map.put("TOTAL_TRANS_31_60", rset.getString(12));
				 
				 if(rset.getString(13)==null)
					map.put("TOTAL_AMOUNT_31_60", 0);
				 else
					 map.put("TOTAL_AMOUNT_31_60", rset.getString(13));
				
				 map.put("TOTAL_TRANS_61_90", rset.getString(14));
				 
				 if(rset.getString(15)==null)
				 	map.put("TOTAL_AMOUNT_61_90", 0);
				 else
					map.put("TOTAL_AMOUNT_61_90", rset.getString(15));
				
				 map.put("TOTAL_TRANS_181_365", rset.getString(16));
				 
				 if(rset.getString(17)==null)
				 	map.put("TOTAL_AMOUNT_181_365", 0);
				 else
					map.put("TOTAL_AMOUNT_181_365", rset.getString(17));

				list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> posniInternalDrcr(String fromDate, String toDate) {// Atm internal method for reconciled 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_INTERNAL_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
			//logger.debug("OnsExternalReconsiled : "+list);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> posniReconcileDrcr(String fromDate, String toDate) { 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_RECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> posniUnReconcileDrcr(String fromDate, String toDate) { 
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();

		try {
			
			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(POSNI_UNRECONCILE_DRCR);
			String query = queryConf.getQueryString();

			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {

			
				Map<String, Object> map = new HashMap<String, Object>();
				
				map.put("DRCR", rset.getString(1));
				map.put("NO_OF_ENTRIES", rset.getString(2));
				 if(rset.getString(3)==null)
						map.put("AMOUNT", 0);
					else
				map.put("AMOUNT", rset.getString(3));
					list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	

	public static void main(String[] args) {
		POSNIACQUIRER p = new POSNIACQUIRER();
		p.posniAcquirerInternalReconcile("2018-01-01", "2018-10-01");
		p.posniAcquirerInternalUnReconcile("2018-01-01", "2018-10-01");
		p.posniAcquirerExternalReconcile("2018-01-01", "2018-10-01");
		p.posniAcquirerExternalUnReconcile("2018-01-01", "2018-10-01");
		p.posniAcquirerSuppressInternal("2018-01-01", "2018-10-01");
		p.posniAcquirerSuppressExternal("2018-01-01", "2018-10-01");
		p.PosniAgingMethod();
		p.posniInternalDrcr("2018-01-01", "2018-10-01");
	}
}
