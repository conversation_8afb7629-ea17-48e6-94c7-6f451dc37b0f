<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<queries id="0">
	<query id="1">
		<name>Dummy</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from abc
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="2">
		<name>IRIS_INSERT_STG</name>
		<targetTables>IRIS_STG</targetTables>
		<queryString>
		INSERT INTO ATM_GL_STG(SID,BRANCH_CODE,CURRENCY_CODE,GL_CODE,CIF_SUB_NO,SL_NO,OP_NO,LINE_NO,TRANSACTION_DATE
			 ,VALUE_DATE,DESCRIPTION,AMOUNT,DECIMAL_POINTS,BRIEF_NAME_ENG,SHORT_NAME_ENG,YTD_FC_BALANCE,YTD_CV_BALANCE,CV_AVAIL_BALANCE,
			 FC_AVAIL_BALANCE,CURRENCY_NAME,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,
			 ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,TRANSACTION_TYPE) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)			
		</queryString>
		<queryParam>
			SID@BIGINT,BRANCH_CODE@INTEGER,CURRENCY_CODE@INTEGER,GL_CODE@VARCHAR,CIF_SUB_NO@VARCHAR
			 ,SL_NO@VARCHAR,OP_NO@VARCHAR,LINE_NO@VARCHAR,TRANSACTION_DATE@DATE,VALUE_DATE@DATE,DESCRIPTION@VARCHAR,AMOUNT@DECIMAL
			 ,DECIMAL_POINTS@INTEGER,BRIEF_NAME_ENG@VARCHAR,SHORT_NAME_ENG@VARCHAR,YTD_FC_BALANCE@DECIMAL,YTD_CV_BALANCE@DECIMAL
			 ,CV_AVAIL_BALANCE@DECIMAL,FC_AVAIL_BALANCE@DECIMAL,CURRENCY_NAME@VARCHAR,COMMENTS@VARCHAR,VERSION@VARCHAR,
			 ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
			 RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,TRANSACTION_TYPE@VARCHAR
		</queryParam>
	</query>


	<query id="4">
		<name>IRIS_INSERT_STG_EX</name>
		<targetTables>IRIS_STG_EX</targetTables>
		<queryString>
		INSERT INTO ATM_GL_STG_EX(SID,BRANCH_CODE,CURRENCY_CODE,GL_CODE,CIF_SUB_NO,SL_NO,OP_NO,LINE_NO,TRANSACTION_DATE
			 ,VALUE_DATE,DESCRIPTION,AMOUNT,DECIMAL_POINTS,BRIEF_NAME_ENG,SHORT_NAME_ENG,YTD_FC_BALANCE,YTD_CV_BALANCE,CV_AVAIL_BALANCE,
			 FC_AVAIL_BALANCE,CURRENCY_NAME,COMMENTS,VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,
			 ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME,TRANSACTION_TYPE) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)			
		</queryString>
		<queryParam>
			SID@BIGINT,BRANCH_CODE@INTEGER,CURRENCY_CODE@INTEGER,GL_CODE@VARCHAR,CIF_SUB_NO@VARCHAR
			 ,SL_NO@VARCHAR,OP_NO@VARCHAR,LINE_NO@VARCHAR,TRANSACTION_DATE@DATE,VALUE_DATE@DATE,DESCRIPTION@VARCHAR,AMOUNT@DECIMAL
			 ,DECIMAL_POINTS@INTEGER,BRIEF_NAME_ENG@VARCHAR,SHORT_NAME_ENG@VARCHAR,YTD_FC_BALANCE@DECIMAL,YTD_CV_BALANCE@DECIMAL
			 ,CV_AVAIL_BALANCE@DECIMAL,FC_AVAIL_BALANCE@DECIMAL,CURRENCY_NAME@VARCHAR,COMMENTS@VARCHAR,VERSION@VARCHAR,
			 ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
			 RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,RANSACTION_TYPE@VARCHAR
		</queryParam>
	</query>
	
	
	
	<query id="3">
		<name>EJ_INSERT_STG</name>
		<targetTables>EJ_STG</targetTables>
		<queryString>
		INSERT INTO EJ_STG (SID,CURRENCY,TRA_TIME,STAN,TXN_TYPE,TRA_DATE,ATM_ID,RRN,RM1,RM5,RM10,RM20,RM50,SEQ,PAN,AMOUNT,STATUS,COMMENTS,
VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME)
VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)		
		</queryString>
		<queryParam>
			SID@BIGINT,CURRENCY@VARCHAR,TRA_TIME@VARCHAR,STAN@VARCHAR,TXN_TYPE@VARCHAR,TRA_DATE@DATE,ATM_ID@VARCHAR,RRN@VARCHAR,
RM1@VARCHAR,RM5@VARCHAR,RM10@VARCHAR,RM20@VARCHAR,RM50@VARCHAR,SEQ@VARCHAR,PAN@VARCHAR,AMOUNT@DECIMAL,STATUS@VARCHAR,COMMENTS@VARCHAR,
	VERSION@VARCHAR,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
	RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR
	</queryParam>
	</query>


	<query id="5">
		<name>EJ_INSERT_STG_EX</name>
		<targetTables>EJ_STG_EX</targetTables>
		<queryString>
		INSERT INTO EJ_STG_EX (SID,CURRENCY,TRA_TIME,STAN,TXN_TYPE,TRA_DATE,ATM_ID,RRN,RM1,RM5,RM10,RM20,RM50,SEQ,PAN,AMOUNT,STATUS,COMMENTS,
VERSION,ACTIVE_INDEX,WORKFLOW_STATUS,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,OPERATION,FILE_NAME)
VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,CURRENCY@VARCHAR,TRA_TIME@VARCHAR,STAN@VARCHAR,TXN_TYPE@VARCHAR,TRA_DATE@DATE,ATM_ID@VARCHAR,RRN@VARCHAR,
RM1@VARCHAR,RM5@VARCHAR,RM10@VARCHAR,RM20@VARCHAR,RM50@VARCHAR,SEQ@VARCHAR,PAN@VARCHAR,AMOUNT@DECIMAL,STATUS@VARCHAR,COMMENTS@VARCHAR,
	VERSION@VARCHAR,ACTIVE_INDEX@VARCHAR,WORKFLOW_STATUS@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
	RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,OPERATION@VARCHAR,FILE_NAME@VARCHAR,
		</queryParam>
	</query>
	

	
	
		<query id="6">
		<name>EJ_EXCEPTION_UPDATE_QUERY</name>
		<targetTables>EJ_STG_EX</targetTables>
		<queryString>
		UPDATE  EJ_STG_EX SET VERSION=? WHERE SID=?  
		</queryString>
		<queryParam>
		VERSION@VARCHAR,SID@BIGINT
		</queryParam>
	</query>
	 
	

	<!-- <query id="5">
		<name>GL_1002_STG_INSERT_QRY</name>
		<targetTables>GL_1002_STG</targetTables>
		<queryString>
			INSERT INTO GL_1002_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TRAN_CUR,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TRAN_CUR@VARCHAR,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>

	<query id="6">
		<name>GL_1002_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1002_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1002_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>

	<query id="7">
		<name>GL_1472_STG_INSERT_QRY</name>
		<targetTables>GL_1472_STG</targetTables>
		<queryString>
			INSERT INTO GL_1472_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,TRAN_CUR,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,CREATED_ON,MAIN_REV_IND,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?
			)
		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,TRAN_CUR@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP,
			MAIN_REV_IND@VARCHAR,DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>




	<query id="9">
		<name>GL_1472_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1472_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1472_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@VARCHAR,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>





	<query id="5">
		<name>GL_1015_STG_INSERT_QRY</name>
		<targetTables>GL_1015_STG</targetTables>
		<queryString>
			INSERT INTO GL_1015_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,TRAN_CUR,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,TRAN_CUR@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>

	<query id="6">
		<name>GL_1015_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1015_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1015_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>




	<query id="5">
		<name>GL_1016_STG_INSERT_QRY</name>
		<targetTables>GL_1016_STG</targetTables>
		<queryString>
			INSERT INTO GL_1016_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,TRAN_CUR,MOBILE_NETWORK,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,TRAN_CUR@VARCHAR,
			MOBILE_NETWORK@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>



	<query id="6">
		<name>GL_1016_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1016_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1016_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>




	<query id="5">
		<name>GL_2247_STG_INSERT_QRY</name>
		<targetTables>GL_2247_STG</targetTables>
		<queryString>
			INSERT INTO GL_2247_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,TRAN_CUR,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,TRAN_CUR@VARCHAR,
			MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>


	<query id="6">
		<name>GL_2247_STG_EX_INSERT_QRY</name>
		<targetTables>GL_2247_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_2247_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>

	<query id="5">
		<name>GL_1006_STG_INSERT_QRY</name>
		<targetTables>GL_1006_STG</targetTables>
		<queryString>
			INSERT INTO GL_1006_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,TRAN_CUR,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,
			CREATED_ON@TIMESTAMP,DEBIT_CREDIT_IND@VARCHAR,TRAN_CUR@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>



	<query id="6">
		<name>GL_1006_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1006_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1006_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>



	<query id="5">
		<name>GL_1482_STG_INSERT_QRY</name>
		<targetTables>GL_1482_STG</targetTables>
		<queryString>
			INSERT INTO GL_1482_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,TRAN_CUR,LOCAL_AMT) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,
			DEBIT_CREDIT_IND@VARCHAR,TRAN_CUR@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>



	<query id="6">
		<name>GL_1482_STG_EX_INSERT_QRY</name>
		<targetTables>GL_1482_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_1482_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@VARCHAR,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@VARCHAR,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>



	<query id="5">
		<name>GL_2279_STG_INSERT_QRY</name>
		<targetTables>GL_2279_STG</targetTables>
		<queryString>
			INSERT INTO GL_2279_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,C_ACCEP_TERM_ID,RETR_REF_NO,TRA_TIME,
			AUTHORIZING_CHANNEL,ACQUIRING_CHANNEL_ID,PROC_CODE,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,TELLER_ID,STAN,
			SETTL_DATE,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,DEBIT_CREDIT_IND,LOCAL_AMT
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,
			CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,
			DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@DECIMAL,DEB_CRE_IND@VARCHAR,CRNT_BAL@DECIMAL,
			MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@DATE,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@DECIMAL,
			ORIGT_BRA_CODE@VARCHAR,ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,
			REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,DRA_ON_BRA_CODE@VARCHAR,
			HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,
			C_ACCEP_TERM_ID@VARCHAR,RETR_REF_NO@VARCHAR,TRA_TIME@VARCHAR,
			AUTHORIZING_CHANNEL@VARCHAR,ACQUIRING_CHANNEL_ID@VARCHAR,PROC_CODE@VARCHAR,GEOGRAPHY@VARCHAR,
			BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,TELLER_ID@VARCHAR,STAN@VARCHAR,
			SETTL_DATE@DATE,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,
			DEBIT_CREDIT_IND@VARCHAR,LOCAL_AMT@DECIMAL
		</queryParam>
	</query>



	<query id="6">
		<name>GL_2279_STG_EX_INSERT_QRY</name>
		<targetTables>GL_2279_STG_EX</targetTables>
		<queryString>
			INSERT INTO GL_2279_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,TRA_DATE,TRA_SEQ1,TRA_SEQ2,BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,TELL_ID,
			EXT_INT_FLAG,DEP_CODE,DIS_CODE,TRA_AMT,DEB_CRE_IND,CRNT_BAL,MAN_APP,MAN_REP,EXPL_CODE,VAL_DATE,
			INT_DATE,CAN_REA_CODE,DOC_ALP,DOC_NUM,CUR_PRI,EQU_TRA_AMT,ORIGT_BRA_CODE,ORIGT_TRA_DATE,ORIGT_TRA_SEQ1,ORIGT_TRA_SEQ2,
			REMARKS,BANK_CODE,CITY_LOC_CODE,DRA_ON_BRA_CODE,HO_TELL_ID,OFFICER_CODE,UPD_TIME,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES (
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@INTEGER,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,
			WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			TRA_DATE@DATE,TRA_SEQ1@VARCHAR,TRA_SEQ2@VARCHAR,BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,
			SUB_ACCT_CODE@VARCHAR,TELL_ID@VARCHAR,EXT_INT_FLAG@VARCHAR,DEP_CODE@VARCHAR,DIS_CODE@VARCHAR,TRA_AMT@VARCHAR,
			DEB_CRE_IND@VARCHAR,CRNT_BAL@VARCHAR,MAN_APP@VARCHAR,MAN_REP@VARCHAR,EXPL_CODE@VARCHAR,VAL_DATE@DATE,INT_DATE@VARCHAR,
			CAN_REA_CODE@VARCHAR,DOC_ALP@VARCHAR,DOC_NUM@VARCHAR,CUR_PRI@VARCHAR,EQU_TRA_AMT@VARCHAR,ORIGT_BRA_CODE@VARCHAR,
			ORIGT_TRA_DATE@DATE,ORIGT_TRA_SEQ1@VARCHAR,ORIGT_TRA_SEQ2@VARCHAR,REMARKS@VARCHAR,BANK_CODE@VARCHAR,CITY_LOC_CODE@VARCHAR,
			DRA_ON_BRA_CODE@VARCHAR,HO_TELL_ID@VARCHAR,OFFICER_CODE@VARCHAR,UPD_TIME@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>

	<query id="36">
		<name>BATCHES_STG_INSERT_QRY</name>
		<targetTables>BATCHES_STG</targetTables>
		<queryString>
			INSERT INTO BATCHES_STG (

			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,CREATED_ON,
			INSTITUTION_ID,SERNO,EODSERNO,EODCYCLESERNO,CHECKSUM,BATCHDATE,LOADTIME,LOADUSER,LASTMODUSER,LASTMODDATE,FILENAME,FILEDATE,FILESIZE,
			FILESOURCE,FILEDESTINATION,DBAMOUNT,DBCURRENCY,CRAMOUNT,CRCURRENCY,DIRECTION,CHANNELSERNO,FILEID,PRODUCT,ENTEREDAMT,ENTEREDTRXNS,
			EXPECTEDTRXNS,MERSERNO,EXPECTEDAMT,EXPECTEDTIPAMT,FILESEQNO,BATCHNO,BATCHID,DBACTION,DBCLIENTINFO,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,BUSINESS_AREA
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@BIGINT,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,
			INSTITUTION_ID@BIGINT,SERNO@BIGINT,EODSERNO@BIGINT,EODCYCLESERNO@BIGINT,CHECKSUM@VARCHAR,BATCHDATE@DATE,LOADTIME@VARCHAR,
			LOADUSER@VARCHAR,LASTMODUSER@VARCHAR,LASTMODDATE@VARCHAR,FILENAME@VARCHAR,FILEDATE@VARCHAR,FILESIZE@BIGINT,
			FILESOURCE@VARCHAR,FILEDESTINATION@VARCHAR,DBAMOUNT@VARCHAR,DBCURRENCY@BIGINT,CRAMOUNT@VARCHAR,CRCURRENCY@BIGINT,
			DIRECTION@VARCHAR,CHANNELSERNO@VARCHAR,FILEID@VARCHAR,PRODUCT@BIGINT,ENTEREDAMT@VARCHAR,ENTEREDTRXNS@VARCHAR,
			EXPECTEDTRXNS@VARCHAR,MERSERNO@VARCHAR,EXPECTEDAMT@VARCHAR,EXPECTEDTIPAMT@VARCHAR,FILESEQNO@VARCHAR,
			BATCHNO@VARCHAR,BATCHID@VARCHAR,DBACTION@VARCHAR,DBCLIENTINFO@VARCHAR,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,BUSINESS_AREA@VARCHAR
		</queryParam>
	</query>

	<query id="37">
		<name>BATCHES_EX_STG_INSERT_QRY</name>
		<targetTables>BATCHES_EX_STG</targetTables>
		<queryString>
			INSERT INTO BATCHES_STG_EX (

			SID,INSTITUTION_ID,SERNO,EODSERNO,EODCYCLESERNO,CHECKSUM,BATCHDATE,LOADTIME,LOADUSER,LASTMODUSER,LASTMODDATE,FILENAME,FILEDATE,FILESIZE,
			FILESOURCE,FILEDESTINATION,DBAMOUNT,DBCURRENCY,CRAMOUNT,CRCURRENCY,DIRECTION,CHANNELSERNO,FILEID,PRODUCT,ENTEREDAMT,ENTEREDTRXNS,
			EXPECTEDTRXNS,MERSERNO,EXPECTEDAMT,EXPECTEDTIPAMT,FILESEQNO,BATCHNO,BATCHID,DBACTION,DBCLIENTINFO,COMMENTS,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@BIGINT,INSTITUTION_ID@BIGINT,SERNO@BIGINT,EODSERNO@BIGINT,EODCYCLESERNO@BIGINT,CHECKSUM@VARCHAR,BATCHDATE@VARCHAR,LOADTIME@VARCHAR,
			LOADUSER@VARCHAR,LASTMODUSER@VARCHAR,LASTMODDATE@VARCHAR,FILENAME@VARCHAR,FILEDATE@VARCHAR,FILESIZE@BIGINT,
			FILESOURCE@VARCHAR,FILEDESTINATION@VARCHAR,DBAMOUNT@VARCHAR,DBCURRENCY@BIGINT,CRAMOUNT@VARCHAR,CRCURRENCY@BIGINT,
			DIRECTION@VARCHAR,CHANNELSERNO@VARCHAR,FILEID@VARCHAR,PRODUCT@BIGINT,ENTEREDAMT@VARCHAR,ENTEREDTRXNS@VARCHAR,
			EXPECTEDTRXNS@VARCHAR,MERSERNO@VARCHAR,EXPECTEDAMT@VARCHAR,EXPECTEDTIPAMT@VARCHAR,FILESEQNO@VARCHAR,
			BATCHNO@VARCHAR,BATCHID@VARCHAR,DBACTION@VARCHAR,DBCLIENTINFO@VARCHAR,COMMENTS@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>



	<query id="22">
		<name>CISO_STG_INSERT_QRY</name>
		<targetTables>CISO_STG</targetTables>
		<queryString>
			INSERT INTO CISO_STG
			(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,CREATED_ON,INSTITUTION_ID,SERNO,PARTITIONKEY,I011_TRACE_NUM,I012_TRXN_TIME,I015_SETTLE_DATE,
			I016_CONVERS_DATE,I018_MERCH_TYPE,I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,I037_RET_REF_NUM,I038_AUTH_ID,I039_RESP_CD,
			I041_POS_ID,I042_MERCH_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I059_POS_GEO_DATA,I060_POS_CAP,I062V2_TRANS_ID,I062M_INF_DATA,CASHBACKAMOUNT,PSVATAMOUNT,TRXNQUALIFIER,SUMMCOMCODE,PAYMENTMODE,
			AUTHTRXNTYPE,AUTHSERNO,AUTHREASONCODE,ACQCOUNTRYCODE,ACQREGION,LINEITEMIND,CONVERTED,TRXNMSGIDENTIFIER,CHANNEL,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS
			)
			VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			CREATED_ON@TIMESTAMP,INSTITUTION_ID@BIGINT,SERNO@BIGINT,PARTITIONKEY@BIGINT,I011_TRACE_NUM@VARCHAR,I012_TRXN_TIME@VARCHAR,I015_SETTLE_DATE@DATE,
			I016_CONVERS_DATE@DATE,I018_MERCH_TYPE@BIGINT,I019_ACQ_COUNTRY@VARCHAR,I022_POS_ENTRY@VARCHAR,I023_SEQUENCE@BIGINT,I024_FUNCT_CODE@BIGINT,
			I025_POS_COND@BIGINT,I028_TRXN_FEE@DECIMAL,I031_ARN@VARCHAR,I032_ACQUIRER_ID@BIGINT,I037_RET_REF_NUM@VARCHAR,I038_AUTH_ID@VARCHAR,I039_RESP_CD@INTEGER,
			I041_POS_ID@VARCHAR,I042_MERCH_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I043B_MERCH_CITY@VARCHAR,I043C_MERCH_CNT@VARCHAR,I059_POS_GEO_DATA@VARCHAR,
			I060_POS_CAP@VARCHAR,I062V2_TRANS_ID@VARCHAR,I062M_INF_DATA@VARCHAR,CASHBACKAMOUNT@DECIMAL,PSVATAMOUNT@DECIMAL,TRXNQUALIFIER@VARCHAR,SUMMCOMCODE@VARCHAR,
			PAYMENTMODE@VARCHAR,AUTHTRXNTYPE@VARCHAR,AUTHSERNO@VARCHAR,AUTHREASONCODE@VARCHAR,ACQCOUNTRYCODE@VARCHAR,ACQREGION@VARCHAR,LINEITEMIND@VARCHAR,CONVERTED@VARCHAR,
			TRXNMSGIDENTIFIER@VARCHAR,CHANNEL@VARCHAR,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
	</query>
	<query id="23">
		<name>CISO_STG_EX_INSERT_QRY</name>
		<targetTables>CISO_STG_EX</targetTables>
		<queryString>
			INSERT INTO CISO_STG_EX
			(SID,VERSION,ACTIVE_INDEX,STATUS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,CREATED_ON,INSTITUTION_ID,SERNO,PARTITIONKEY,I011_TRACE_NUM,I012_TRXN_TIME,I015_SETTLE_DATE,I016_CONVERS_DATE,I018_MERCH_TYPE,I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,
			I037_RET_REF_NUM,I038_AUTH_ID,I039_RESP_CD,I041_POS_ID,I042_MERCH_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I059_POS_GEO_DATA,I060_POS_CAP,I062V2_TRANS_ID,I062M_INF_DATA,CASHBACKAMOUNT,PSVATAMOUNT,TRXNQUALIFIER,SUMMCOMCODE,PAYMENTMODE,
			AUTHTRXNTYPE,AUTHSERNO,AUTHREASONCODE,ACQCOUNTRYCODE,ACQREGION,LINEITEMIND,CONVERTED,TRXNMSGIDENTIFIER,COMMENTS,ACTIVITY_COMMENTS
			) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			CREATED_ON@TIMESTAMP,INSTITUTION_ID@VARCHAR,SERNO@VARCHAR,PARTITIONKEY@VARCHAR,I011_TRACE_NUM@VARCHAR,I012_TRXN_TIME@VARCHAR,I015_SETTLE_DATE@VARCHAR,I016_CONVERS_DATE@VARCHAR,I018_MERCH_TYPE@VARCHAR,I019_ACQ_COUNTRY@VARCHAR,
			I022_POS_ENTRY@VARCHAR,I023_SEQUENCE@VARCHAR,I024_FUNCT_CODE@VARCHAR,I025_POS_COND@VARCHAR,I028_TRXN_FEE@VARCHAR,I031_ARN@VARCHAR,I032_ACQUIRER_ID@VARCHAR,I037_RET_REF_NUM@VARCHAR,I038_AUTH_ID@VARCHAR,I039_RESP_CD@VARCHAR,
			I041_POS_ID@VARCHAR,I042_MERCH_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I043B_MERCH_CITY@VARCHAR,I043C_MERCH_CNT@VARCHAR,I059_POS_GEO_DATA@VARCHAR,I060_POS_CAP@VARCHAR,I062V2_TRANS_ID@VARCHAR,I062M_INF_DATA@VARCHAR,
			CASHBACKAMOUNT@VARCHAR,PSVATAMOUNT@VARCHAR,TRXNQUALIFIER@VARCHAR,SUMMCOMCODE@VARCHAR,PAYMENTMODE@VARCHAR,AUTHTRXNTYPE@VARCHAR,AUTHSERNO@VARCHAR,AUTHREASONCODE@VARCHAR,
			ACQCOUNTRYCODE@VARCHAR,ACQREGION@VARCHAR,LINEITEMIND@VARCHAR,CONVERTED@VARCHAR,TRXNMSGIDENTIFIER@VARCHAR,COMMENTS@VARCHAR,ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
	</query>


	<query id="22">
		<name>CTXNS_STG_INSERT_QRY</name>
		<targetTables>CTXNS_STG</targetTables>
		<queryString>
			INSERT INTO CTXNS_STG
			(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,CREATED_ON,INSTITUTION_ID,SERNO,PARTITIONKEY,
			CACCSERNO,CARDSERNO,DEF_CACCSERNO,PRODUCT,BATCHSERNO,TYPESERNO_ALLOC,TYPESERNO_FEES,TYPESERNO_REPORTS,TYPESERNO_REWARDS,
			TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_NOPOST,MSGCLASS,MSGTYPE,TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,
			I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I008_BILLING_FEE,I013_TRXN_DATE,I044_REASON_CODE,
			I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,INSTALMENTTYPE,INSTALMENTINDEPFLAG,INSTALMENTSNUMBER,INSTALMENTSEQ,
			INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTTOTALAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,
			INSTALMENTSERNO,INSTALMENTPARTITIONKEY,SINGLE_MSG_FLAG,AUTHACCOUNTTYPE,ORIGINATOR,ORIGINATORREASONCODE,PROXYCARDNUMBER,
			INVOICENUMBER,AMOUNT,EMBEDDEDFEE,TOTALPOINTS,TAXFLAG,VALUEDATE,STARTOFINTEREST,MINDUEVALUEDATE,POSTDATE,TIME_IN_MILLIS,POSTTIMESTAMP,DESCSTRINGID,
			STGENERAL,LOGACTION,CONVERTED,NETWORK,CARD_TYPE,BIN,RECON_STATUS,RECON_ID,ACTIVITY_COMMENTS,MAIN_REV_IND,PROC_CODE,TRAN_CUR
			) VALUES(?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,
			INSTITUTION_ID@BIGINT,SERNO@BIGINT,PARTITIONKEY@BIGINT,CACCSERNO@BIGINT,CARDSERNO@BIGINT,DEF_CACCSERNO@VARCHAR,PRODUCT@BIGINT,
			BATCHSERNO@BIGINT,TYPESERNO_ALLOC@BIGINT,TYPESERNO_FEES@BIGINT,TYPESERNO_REPORTS@BIGINT,TYPESERNO_REWARDS@BIGINT,TYPESERNO_GLEDGER@BIGINT,
			TYPESERNO_DIVERT@BIGINT,TYPESERNO_NOPOST@BIGINT,MSGCLASS@BIGINT,MSGTYPE@BIGINT,TRXNTYPE@BIGINT,ORIG_MSG_TYPE@VARCHAR,
			I000_MSG_TYPE@VARCHAR,I002_NUMBER@VARCHAR,I003_PROC_CODE@VARCHAR,I004_AMT_TRXN@DECIMAL,I005_AMT_SETTLE@DECIMAL,I006_AMT_BILL@DECIMAL,
			I007_LOAD_DATE@DATE,I008_BILLING_FEE@VARCHAR,I013_TRXN_DATE@DATE,I044_REASON_CODE@VARCHAR,I048_TEXT_DATA@VARCHAR,
			I049_CUR_TRXN@BIGINT,I050_CUR_SETTLE@BIGINT,I051_CUR_BILL@BIGINT,INSTALMENTTYPE@VARCHAR,INSTALMENTINDEPFLAG@VARCHAR,
			INSTALMENTSNUMBER@VARCHAR,INSTALMENTSEQ@VARCHAR,INSTALMENTREPAYMENTTYPE@VARCHAR,INSTALMENTOFFSET@VARCHAR,INSTALMENTORIGAMOUNT@VARCHAR,
			INSTALMENTTOTALAMOUNT@VARCHAR,INSTALMENTPLANSERNO@VARCHAR,INSTALMENTINTERESTANCHORDATE@VARCHAR,INSTALMENTSERNO@VARCHAR,
			INSTALMENTPARTITIONKEY@VARCHAR,SINGLE_MSG_FLAG@VARCHAR,AUTHACCOUNTTYPE@VARCHAR,ORIGINATOR@VARCHAR,ORIGINATORREASONCODE@VARCHAR,
			PROXYCARDNUMBER@VARCHAR,INVOICENUMBER@VARCHAR,AMOUNT@VARCHAR,EMBEDDEDFEE@VARCHAR,TOTALPOINTS@VARCHAR,TAXFLAG@VARCHAR,
			VALUEDATE@DATE,STARTOFINTEREST@VARCHAR,MINDUEVALUEDATE@VARCHAR,POSTDATE@DATE,TIME_IN_MILLIS@BIGINT,POSTTIMESTAMP@VARCHAR,DESCSTRINGID@VARCHAR,
			STGENERAL@VARCHAR,LOGACTION@VARCHAR,CONVERTED@VARCHAR,NETWORK@VARCHAR,CARD_TYPE@VARCHAR,BIN@BIGINT,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,PROC_CODE@VARCHAR,TRAN_CUR@VARCHAR
		</queryParam>
	</query>


	<query id="22">
		<name>CTXNS_STG_EX_INSERT_QRY</name>
		<targetTables>CTXNS_STG_EX</targetTables>
		<queryString>
			INSERT INTO CTXNS_STG_EX (SID,VERSION,INSTITUTION_ID,SERNO,PARTITIONKEY,
			CACCSERNO,CARDSERNO,DEF_CACCSERNO,PRODUCT,BATCHSERNO,TYPESERNO_ALLOC,TYPESERNO_FEES,TYPESERNO_REPORTS,TYPESERNO_REWARDS,
			TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_NOPOST,MSGCLASS,MSGTYPE,TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,
			I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I008_BILLING_FEE,I013_TRXN_DATE,I044_REASON_CODE,
			I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,INSTALMENTTYPE,INSTALMENTINDEPFLAG,INSTALMENTSNUMBER,INSTALMENTSEQ,
			INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTTOTALAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,
			INSTALMENTSERNO,INSTALMENTPARTITIONKEY,SINGLE_MSG_FLAG,AUTHACCOUNTTYPE,ORIGINATOR,ORIGINATORREASONCODE,PROXYCARDNUMBER,
			INVOICENUMBER,AMOUNT,EMBEDDEDFEE,TOTALPOINTS,TAXFLAG,VALUEDATE,STARTOFINTEREST,MINDUEVALUEDATE,POSTDATE,POSTTIMESTAMP,DESCSTRINGID,
			STGENERAL,LOGACTION,CONVERTED,COMMENTS,ACTIVITY_COMMENTS,CREATED_ON
			) VALUES(?,?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,INSTITUTION_ID@BIGINT,SERNO@BIGINT,PARTITIONKEY@BIGINT,CACCSERNO@BIGINT,CARDSERNO@BIGINT,DEF_CACCSERNO@VARCHAR,PRODUCT@BIGINT,
			BATCHSERNO@BIGINT,TYPESERNO_ALLOC@BIGINT,TYPESERNO_FEES@BIGINT,TYPESERNO_REPORTS@BIGINT,TYPESERNO_REWARDS@BIGINT,TYPESERNO_GLEDGER@BIGINT,
			TYPESERNO_DIVERT@BIGINT,TYPESERNO_NOPOST@BIGINT,MSGCLASS@BIGINT,MSGTYPE@BIGINT,TRXNTYPE@BIGINT,ORIG_MSG_TYPE@VARCHAR,
			I000_MSG_TYPE@VARCHAR,I002_NUMBER@VARCHAR,I003_PROC_CODE@VARCHAR,I004_AMT_TRXN@DECIMAL,I005_AMT_SETTLE@DECIMAL,I006_AMT_BILL@DECIMAL,
			I007_LOAD_DATE@VARCHAR,I008_BILLING_FEE@VARCHAR,I013_TRXN_DATE@VARCHAR,I044_REASON_CODE@VARCHAR,I048_TEXT_DATA@VARCHAR,
			I049_CUR_TRXN@BIGINT,I050_CUR_SETTLE@BIGINT,I051_CUR_BILL@BIGINT,INSTALMENTTYPE@VARCHAR,INSTALMENTINDEPFLAG@VARCHAR,
			INSTALMENTSNUMBER@VARCHAR,INSTALMENTSEQ@VARCHAR,INSTALMENTREPAYMENTTYPE@VARCHAR,INSTALMENTOFFSET@VARCHAR,INSTALMENTORIGAMOUNT@VARCHAR,
			INSTALMENTTOTALAMOUNT@VARCHAR,INSTALMENTPLANSERNO@VARCHAR,INSTALMENTINTERESTANCHORDATE@VARCHAR,INSTALMENTSERNO@VARCHAR,
			INSTALMENTPARTITIONKEY@VARCHAR,SINGLE_MSG_FLAG@VARCHAR,AUTHACCOUNTTYPE@VARCHAR,ORIGINATOR@VARCHAR,ORIGINATORREASONCODE@VARCHAR,
			PROXYCARDNUMBER@VARCHAR,INVOICENUMBER@VARCHAR,AMOUNT@VARCHAR,EMBEDDEDFEE@VARCHAR,TOTALPOINTS@VARCHAR,TAXFLAG@VARCHAR,
			VALUEDATE@DATE,STARTOFINTEREST@VARCHAR,MINDUEVALUEDATE@VARCHAR,POSTDATE@DATE,POSTTIMESTAMP@VARCHAR,DESCSTRINGID@VARCHAR,
			STGENERAL@VARCHAR,LOGACTION@VARCHAR,CONVERTED@VARCHAR,COMMENTS@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>
	<query id="16">
		<name>MISO_STG_INSERT_QRY</name>
		<targetTables>MISO_STG</targetTables>
		<queryString>

			INSERT INTO
			MISO_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,ATTACHMENT_ID,WORKFLOW_STATUS,USER_ID,OPERATION,CREATED_ON,
			UPDATED_ON,RECON_STATUS,RECON_ID,INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,OUTBATCHSERNO,PSNAME,TYPESERNO_CHANNEL,
			CHANNELSERNO,ONUS,TRXNENTRY,TRXNPRESENCE,TRXNBATCHNUMBER,I011_TRACE_NUM,I014_EXPIRY_DATE,I015_SETTLE_DATE,I016_CONVERS_DATE,I018_MERCH_TYPE,
			I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,I037_RET_REF_NUM,I038_AUTH_ID,
			I039_RESP_CD,I041_POS_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I043D_ADDRESS1,I043E_ADDRESS2,I043F_ZIP,I043G_REGION_CODE,I059_POS_GEO_DATA,
			I060_POS_CAP,I062M_INF_DATA,I062V2_TRANS_ID,I126V8_TRAN_ID,CASHBACKAMOUNT,AUTHTRXNTYPE,AUTHACCOUNTTYPE,ISSCOUNTRYCODE,CARDTYPE,FEEPROGRAMIND,
			INTERCHANGEREGION,INTERCHANGEDESCRIPTOR,INTERCHANGEFEEIND,INTERCHANGERATE,INTERCHANGEFEEAMOUNT,INTERCHANGEFEEAMOUNTTRXN,INTERFACELOGSERNO,
			INTERFACEINVOICENUMBER,INTERFACEOTHERDATA,PRODUCTCODE,FUELACCEPTANCEMODE,UNITS,UNITOFMEASURE,UNITPRICE,FLEETNUMBER,FLEETBRAND,LINEITEMIND,EXTERNALFILEID,
			EXTERNALTRXNID,INTERCHANGEFEEAMTCNTCUR,POICURRCONV,POICURRCODE,DCCSTATUS,DCCSCONVRATE,TRXNMSGIDENTIFIER,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,ACTIVITY_COMMENTS)
			VALUES(?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?
			)
		</queryString>
		<queryParam>
			SID@VARCHAR,VERSION@VARCHAR,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,ATTACHMENT_ID@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,CREATED_ON@VARCHAR,UPDATED_ON@VARCHAR,RECON_STATUS@VARCHAR,RECON_ID@VARCHAR,
			INSTITUTION_ID@VARCHAR,SERNO@BIGINT,PARTITIONKEY@BIGINT,SUBPARTITIONKEY@VARCHAR,OUTBATCHSERNO@BIGINT,PSNAME@VARCHAR,TYPESERNO_CHANNEL@BIGINT,CHANNELSERNO@BIGINT,ONUS@INTEGER,TRXNENTRY@INTEGER,TRXNPRESENCE@INTEGER,TRXNBATCHNUMBER@INTEGER,
			I011_TRACE_NUM@BIGINT,I014_EXPIRY_DATE@DATE,I015_SETTLE_DATE@VARCHAR,I016_CONVERS_DATE@DATE,I018_MERCH_TYPE@BIGINT,I019_ACQ_COUNTRY@VARCHAR,I022_POS_ENTRY@VARCHAR,I023_SEQUENCE@VARCHAR,I024_FUNCT_CODE@VARCHAR,
			I025_POS_COND@VARCHAR,I028_TRXN_FEE@DECIMAL,I031_ARN@VARCHAR,I032_ACQUIRER_ID@BIGINT,I037_RET_REF_NUM@VARCHAR,I038_AUTH_ID@VARCHAR,I039_RESP_CD@VARCHAR,I041_POS_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I043B_MERCH_CITY@VARCHAR,
			I043C_MERCH_CNT@VARCHAR,I043D_ADDRESS1@VARCHAR,I043E_ADDRESS2@VARCHAR,I043F_ZIP@VARCHAR,I043G_REGION_CODE@VARCHAR,I059_POS_GEO_DATA@VARCHAR,I060_POS_CAP@VARCHAR,I062M_INF_DATA@VARCHAR,I062V2_TRANS_ID@VARCHAR,
			I126V8_TRAN_ID@VARCHAR,CASHBACKAMOUNT@DECIMAL,AUTHTRXNTYPE@VARCHAR,AUTHACCOUNTTYPE@VARCHAR,ISSCOUNTRYCODE@VARCHAR,CARDTYPE@VARCHAR,FEEPROGRAMIND@VARCHAR,INTERCHANGEREGION@VARCHAR,INTERCHANGEDESCRIPTOR@VARCHAR,
			INTERCHANGEFEEIND@VARCHAR,INTERCHANGERATE@VARCHAR,INTERCHANGEFEEAMOUNT@VARCHAR,INTERCHANGEFEEAMOUNTTRXN@VARCHAR,INTERFACELOGSERNO@VARCHAR,INTERFACEINVOICENUMBER@VARCHAR,INTERFACEOTHERDATA@VARCHAR,
			PRODUCTCODE@VARCHAR,FUELACCEPTANCEMODE@VARCHAR,UNITS@VARCHAR,UNITOFMEASURE@VARCHAR,UNITPRICE@VARCHAR,FLEETNUMBER@VARCHAR,FLEETBRAND@VARCHAR,LINEITEMIND@VARCHAR,EXTERNALFILEID@VARCHAR,EXTERNALTRXNID@VARCHAR,INTERCHANGEFEEAMTCNTCUR@VARCHAR,
			POICURRCONV@VARCHAR,POICURRCODE@VARCHAR,DCCSTATUS@VARCHAR,DCCSCONVRATE@VARCHAR,TRXNMSGIDENTIFIER@VARCHAR,GEOGRAPHY@VARCHAR,BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
	</query>


	<query id="16">
		<name>MISO_STG_EX_INSERT_QRY</name>
		<targetTables>MISO_STG</targetTables>
		<queryString>

			INSERT INTO
			MISO_STG_EX(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,ATTACHMENT_ID,WORKFLOW_STATUS,USER_ID,OPERATION,CREATED_ON,
			UPDATED_ON,RECON_STATUS,RECON_ID,INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,OUTBATCHSERNO,PSNAME,TYPESERNO_CHANNEL,
			CHANNELSERNO,ONUS,TRXNENTRY,TRXNPRESENCE,TRXNBATCHNUMBER,I011_TRACE_NUM,I014_EXPIRY_DATE,I015_SETTLE_DATE,I016_CONVERS_DATE,I018_MERCH_TYPE,
			I019_ACQ_COUNTRY,I022_POS_ENTRY,I023_SEQUENCE,I024_FUNCT_CODE,I025_POS_COND,I028_TRXN_FEE,I031_ARN,I032_ACQUIRER_ID,I037_RET_REF_NUM,I038_AUTH_ID,
			I039_RESP_CD,I041_POS_ID,I043A_MERCH_NAME,I043B_MERCH_CITY,I043C_MERCH_CNT,I043D_ADDRESS1,I043E_ADDRESS2,I043F_ZIP,I043G_REGION_CODE,I059_POS_GEO_DATA,
			I060_POS_CAP,I062M_INF_DATA,I062V2_TRANS_ID,I126V8_TRAN_ID,CASHBACKAMOUNT,AUTHTRXNTYPE,AUTHACCOUNTTYPE,ISSCOUNTRYCODE,CARDTYPE,FEEPROGRAMIND,
			INTERCHANGEREGION,INTERCHANGEDESCRIPTOR,INTERCHANGEFEEIND,INTERCHANGERATE,INTERCHANGEFEEAMOUNT,INTERCHANGEFEEAMOUNTTRXN,INTERFACELOGSERNO,
			INTERFACEINVOICENUMBER,INTERFACEOTHERDATA,PRODUCTCODE,FUELACCEPTANCEMODE,UNITS,UNITOFMEASURE,UNITPRICE,FLEETNUMBER,FLEETBRAND,LINEITEMIND,EXTERNALFILEID,
			EXTERNALTRXNID,INTERCHANGEFEEAMTCNTCUR,POICURRCONV,POICURRCODE,DCCSTATUS,DCCSCONVRATE,TRXNMSGIDENTIFIER,ACTIVITY_COMMENTS)
			VALUES(?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?
			)
		</queryString>
		<queryParam>
			SID@VARCHAR,VERSION@VARCHAR,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,ATTACHMENT_ID@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,CREATED_ON@VARCHAR,UPDATED_ON@VARCHAR,RECON_STATUS@VARCHAR,RECON_ID@VARCHAR,
			INSTITUTION_ID@VARCHAR,SERNO@VARCHAR,PARTITIONKEY@VARCHAR,SUBPARTITIONKEY@VARCHAR,OUTBATCHSERNO@VARCHAR,PSNAME@VARCHAR,TYPESERNO_CHANNEL@VARCHAR,CHANNELSERNO@VARCHAR,ONUS@VARCHAR,TRXNENTRY@VARCHAR,TRXNPRESENCE@VARCHAR,TRXNBATCHNUMBER@VARCHAR,
			I011_TRACE_NUM@VARCHAR,I014_EXPIRY_DATE@VARCHAR,I015_SETTLE_DATE@VARCHAR,I016_CONVERS_DATE@VARCHAR,I018_MERCH_TYPE@VARCHAR,I019_ACQ_COUNTRY@VARCHAR,I022_POS_ENTRY@VARCHAR,I023_SEQUENCE@VARCHAR,I024_FUNCT_CODE@VARCHAR,
			I025_POS_COND@VARCHAR,I028_TRXN_FEE@VARCHAR,I031_ARN@VARCHAR,I032_ACQUIRER_ID@VARCHAR,I037_RET_REF_NUM@VARCHAR,I038_AUTH_ID@VARCHAR,I039_RESP_CD@VARCHAR,I041_POS_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I043B_MERCH_CITY@VARCHAR,
			I043C_MERCH_CNT@VARCHAR,I043D_ADDRESS1@VARCHAR,I043E_ADDRESS2@VARCHAR,I043F_ZIP@VARCHAR,I043G_REGION_CODE@VARCHAR,I059_POS_GEO_DATA@VARCHAR,I060_POS_CAP@VARCHAR,I062M_INF_DATA@VARCHAR,I062V2_TRANS_ID@VARCHAR,
			I126V8_TRAN_ID@VARCHAR,CASHBACKAMOUNT@VARCHAR,AUTHTRXNTYPE@VARCHAR,AUTHACCOUNTTYPE@VARCHAR,ISSCOUNTRYCODE@VARCHAR,CARDTYPE@VARCHAR,FEEPROGRAMIND@VARCHAR,INTERCHANGEREGION@VARCHAR,INTERCHANGEDESCRIPTOR@VARCHAR,
			INTERCHANGEFEEIND@VARCHAR,INTERCHANGERATE@VARCHAR,INTERCHANGEFEEAMOUNT@VARCHAR,INTERCHANGEFEEAMOUNTTRXN@VARCHAR,INTERFACELOGSERNO@VARCHAR,INTERFACEINVOICENUMBER@VARCHAR,INTERFACEOTHERDATA@VARCHAR,
			PRODUCTCODE@VARCHAR,FUELACCEPTANCEMODE@VARCHAR,UNITS@VARCHAR,UNITOFMEASURE@VARCHAR,UNITPRICE@VARCHAR,FLEETNUMBER@VARCHAR,FLEETBRAND@VARCHAR,LINEITEMIND@VARCHAR,EXTERNALFILEID@VARCHAR,EXTERNALTRXNID@VARCHAR,INTERCHANGEFEEAMTCNTCUR@VARCHAR,
			POICURRCONV@VARCHAR,POICURRCODE@VARCHAR,DCCSTATUS@VARCHAR,DCCSCONVRATE@VARCHAR,TRXNMSGIDENTIFIER@VARCHAR,ACTIVITY_COMMENTS@VARCHAR
		</queryParam>
	</query>


	<query id="15">
		<name>MTXNS_STG_INSERT_QRY</name>
		<targetTables>MTXNS_STG</targetTables>
		<queryString>
			INSERT INTO MTXNS_STG(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,
			RECON_ID,INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,MERSERNO,MERACCSERNO,BALANCESERNO,BALANCEPARTITIONKEY,BALANCESUBPARTITIONKEY,
			PRODUCT,INBATCHSERNO,TYPESERNO_COM,TYPESERNO_REPORTS,TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_FEES,TYPESERNO_VAT,MSGCLASS,MSGTYPE,
			TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I012_TRXN_TIME,
			I013_TRXN_DATE,I042_MERCH_ID,I044_REASON_CODE,I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,EXRATETRXNTOSETTLEMENT,CENTERCURRENCY,CENTERAMOUNT,
			INSTALMENTTYPE,INSTALMENTSNUMBER,INSTALMENTSEQ,INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,INSTALMENTSERNO,INSTALMENTPARTITIONKEY,
			INSTALMENTSUBPARTITIONKEY,SINGLE_MSG_FLAG,ORIGINATOR,ORIGINATORREASONCODE,AMOUNT,TIPAMOUNT,BILLTIPAMOUNT,COMAMOUNT,ORIGCOMAMOUNT,ISCOMEMBEDDED,
			EMBEDDEDFEE,VATAMOUNT,VATSETTLEMENTAMOUNT,VATBILLINGAMOUNT,ISVATEMBEDDED,VATCOMMISSIONAMOUNT,ISCOMVATEMBEDDED,POSTDATE,POSTTIMESTAMP,PAYDATE,
			STGENERAL,LOGACTION,CARD_TYPE,BIN,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,PROC_CODE,TRAN_CUR) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
			RECON_ID@BIGINT,INSTITUTION_ID@VARCHAR,SERNO@BIGINT,PARTITIONKEY@VARCHAR,SUBPARTITIONKEY@VARCHAR,MERSERNO@VARCHAR,MERACCSERNO@VARCHAR,BALANCESERNO@VARCHAR,BALANCEPARTITIONKEY@VARCHAR,BALANCESUBPARTITIONKEY@VARCHAR,
			PRODUCT@VARCHAR,INBATCHSERNO@BIGINT,TYPESERNO_COM@VARCHAR,TYPESERNO_REPORTS@VARCHAR,TYPESERNO_GLEDGER@VARCHAR,TYPESERNO_DIVERT@VARCHAR,TYPESERNO_FEES@VARCHAR,TYPESERNO_VAT@VARCHAR,MSGCLASS@VARCHAR,MSGTYPE@VARCHAR,
			TRXNTYPE@VARCHAR,ORIG_MSG_TYPE@VARCHAR,I000_MSG_TYPE@VARCHAR,I002_NUMBER@VARCHAR,I003_PROC_CODE@VARCHAR,I004_AMT_TRXN@DECIMAL,I005_AMT_SETTLE@VARCHAR,I006_AMT_BILL@VARCHAR,I007_LOAD_DATE@DATE,I012_TRXN_TIME@VARCHAR,
			I013_TRXN_DATE@DATE,I042_MERCH_ID@VARCHAR,I044_REASON_CODE@VARCHAR,I048_TEXT_DATA@VARCHAR,I049_CUR_TRXN@VARCHAR,I050_CUR_SETTLE@VARCHAR,I051_CUR_BILL@VARCHAR,EXRATETRXNTOSETTLEMENT@VARCHAR,CENTERCURRENCY@VARCHAR,CENTERAMOUNT@VARCHAR,
			INSTALMENTTYPE@VARCHAR,INSTALMENTSNUMBER@VARCHAR,INSTALMENTSEQ@VARCHAR,INSTALMENTREPAYMENTTYPE@VARCHAR,INSTALMENTOFFSET@VARCHAR,INSTALMENTORIGAMOUNT@VARCHAR,INSTALMENTPLANSERNO@VARCHAR,INSTALMENTINTERESTANCHORDATE@VARCHAR,INSTALMENTSERNO@VARCHAR,INSTALMENTPARTITIONKEY@VARCHAR,
			INSTALMENTSUBPARTITIONKEY@VARCHAR,SINGLE_MSG_FLAG@VARCHAR,ORIGINATOR@VARCHAR,ORIGINATORREASONCODE@VARCHAR,AMOUNT@VARCHAR,TIPAMOUNT@VARCHAR,BILLTIPAMOUNT@VARCHAR,COMAMOUNT@VARCHAR,ORIGCOMAMOUNT@VARCHAR,ISCOMEMBEDDED@VARCHAR,
			EMBEDDEDFEE@VARCHAR,VATAMOUNT@VARCHAR,VATSETTLEMENTAMOUNT@VARCHAR,VATBILLINGAMOUNT@VARCHAR,ISVATEMBEDDED@VARCHAR,VATCOMMISSIONAMOUNT@VARCHAR,ISCOMVATEMBEDDED@VARCHAR,POSTDATE@VARCHAR,POSTTIMESTAMP@VARCHAR,PAYDATE@VARCHAR,
			STGENERAL@VARCHAR,LOGACTION@VARCHAR,CARD_TYPE@VARCHAR,BIN@BIGINT,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,PROC_CODE@VARCHAR,TRAN_CUR@VARCHAR

		</queryParam>
	</query>


	
	<query id="24">
        <name>AUTH_ACQUIRER_STG_INSERT_QRY</name>
		<targetTables>AUTH_ACQUIRER_STG</targetTables>
        <queryString>
				INSERT INTO AUTH_ACQUIRER_STG (SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,
											PROCESS_NAME,EDCFLAG,I007_TRANS_DT,CARDBIN,I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,
											I006_AMT_BILL,SERNO,LTIMESTAMP,I000_MSG_TYPE,SOURCE,I039_RESP_CD,REASONCODE,I003_PROC_CODE,
											I025_POS_COND,I002_NUMBER,I042_MERCH_ID,I043A_MERCH_NAME,I032_ACQUIRER_ID,I041_POS_ID,I019_ACQ_COUNTRY,
											I018_MERCH_TYPE,I037_RET_REF_NUM,I004_AMT_TRXN,BUSINESS_AREA,CHANNEL,NETWORK,CARD_TYPE,BIN,ACTIVITY_COMMENTS,
											I038_AUTH_ID,CENTERAMT,TIME_IN_MILLIS,MAIN_REV_IND,TRA_TIME,CREATED_ON,TRAN_CUR)
				 VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
				 		?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
				SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,
				USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@VARCHAR,PROCESS_NAME@VARCHAR,EDCFLAG@VARCHAR,I007_TRANS_DT@VARCHAR,
				CARDBIN@BIGINT,I049_CUR_TRXN@BIGINT,I050_CUR_SETTLE@VARCHAR,I005_AMT_SETTLE@VARCHAR,I006_AMT_BILL@VARCHAR,
				SERNO@BIGINT,LTIMESTAMP@DATE,I000_MSG_TYPE@VARCHAR,SOURCE@VARCHAR,I039_RESP_CD@INTEGER,
				REASONCODE@INTEGER,I003_PROC_CODE@BIGINT,I025_POS_COND@INTEGER,I002_NUMBER@VARCHAR,I042_MERCH_ID@BIGINT,
				I043A_MERCH_NAME@VARCHAR,I032_ACQUIRER_ID@BIGINT,I041_POS_ID@BIGINT,I019_ACQ_COUNTRY@VARCHAR,
				I018_MERCH_TYPE@VARCHAR,I037_RET_REF_NUM@VARCHAR,I004_AMT_TRXN@DECIMAL,BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,
				NETWORK@VARCHAR,CARD_TYPE@VARCHAR,BIN@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,I038_AUTH_ID@VARCHAR,CENTERAMT@DECIMAL,
				TIME_IN_MILLIS@BIGINT,MAIN_REV_IND@VARCHAR,TRA_TIME@VARCHAR,CREATED_ON@TIMESTAMP,TRAN_CUR@VARCHAR
		</queryParam>
    </query>
	<query id="25">
        <name>AUTH_ACQUIRER_STG_EX_INSERT_QRY</name>
		<targetTables>AUTH_ACQUIRER_STG_EX</targetTables>
        <queryString>
				INSERT INTO AUTH_ACQUIRER_STG_EX (SID,PROCESS_NAME,EDCFLAG,I007_TRANS_DT,CARDBIN,I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,
				I006_AMT_BILL,SERNO,LTIMESTAMP,I000_MSG_TYPE,SOURCE,I039_RESP_CD,REASONCODE,I003_PROC_CODE,I025_POS_COND,I002_NUMBER,I042_MERCH_ID,
				I043A_MERCH_NAME,I032_ACQUIRER_ID,I041_POS_ID,I019_ACQ_COUNTRY,I018_MERCH_TYPE,I037_RET_REF_NUM,I004_AMT_TRXN,COMMENTS,ACTIVITY_COMMENTS,CREATED_ON) VALUES(?,?,?,?,?,?
				,?,?,?,?,?
				,?,?,?,?,?
				,?,?,?,?,?
				,?,?,?,?,?
				,?,?,?)</queryString>
		<queryParam>
				SID@BIGINT,PROCESS_NAME@VARCHAR,EDCFLAG@VARCHAR,I007_TRANS_DT@VARCHAR,CARDBIN@VARCHAR,I049_CUR_TRXN@VARCHAR,I050_CUR_SETTLE@VARCHAR,
				I005_AMT_SETTLE@VARCHAR,I006_AMT_BILL@VARCHAR,SERNO@VARCHAR,LTIMESTAMP@VARCHAR,I000_MSG_TYPE@VARCHAR,SOURCE@VARCHAR,I039_RESP_CD@VARCHAR,REASONCODE@VARCHAR,
				I003_PROC_CODE@VARCHAR,I025_POS_COND@VARCHAR,I002_NUMBER@VARCHAR,I042_MERCH_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I032_ACQUIRER_ID@NVARCHAR,
				I041_POS_ID@VARCHAR,I019_ACQ_COUNTRY@VARCHAR,I018_MERCH_TYPE@VARCHAR,I037_RET_REF_NUM@VARCHAR,I004_AMT_TRXN@VARCHAR,COMMENTS@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
				</queryParam>
    </query>

	<query id="15">
		<name>MTXNS_STG_EX_INSERT_QRY</name>
		<targetTables>MTXNS_STG</targetTables>
		<queryString>
			INSERT INTO MTXNS_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,
			RECON_ID,INSTITUTION_ID,SERNO,PARTITIONKEY,SUBPARTITIONKEY,MERSERNO,MERACCSERNO,BALANCESERNO,BALANCEPARTITIONKEY,BALANCESUBPARTITIONKEY,
			PRODUCT,INBATCHSERNO,TYPESERNO_COM,TYPESERNO_REPORTS,TYPESERNO_GLEDGER,TYPESERNO_DIVERT,TYPESERNO_FEES,TYPESERNO_VAT,MSGCLASS,MSGTYPE,
			TRXNTYPE,ORIG_MSG_TYPE,I000_MSG_TYPE,I002_NUMBER,I003_PROC_CODE,I004_AMT_TRXN,I005_AMT_SETTLE,I006_AMT_BILL,I007_LOAD_DATE,I012_TRXN_TIME,
			I013_TRXN_DATE,I042_MERCH_ID,I044_REASON_CODE,I048_TEXT_DATA,I049_CUR_TRXN,I050_CUR_SETTLE,I051_CUR_BILL,EXRATETRXNTOSETTLEMENT,CENTERCURRENCY,CENTERAMOUNT,
			INSTALMENTTYPE,INSTALMENTSNUMBER,INSTALMENTSEQ,INSTALMENTREPAYMENTTYPE,INSTALMENTOFFSET,INSTALMENTORIGAMOUNT,INSTALMENTPLANSERNO,INSTALMENTINTERESTANCHORDATE,INSTALMENTSERNO,INSTALMENTPARTITIONKEY,
			INSTALMENTSUBPARTITIONKEY,SINGLE_MSG_FLAG,ORIGINATOR,ORIGINATORREASONCODE,AMOUNT,TIPAMOUNT,BILLTIPAMOUNT,COMAMOUNT,ORIGCOMAMOUNT,ISCOMEMBEDDED,
			EMBEDDEDFEE,VATAMOUNT,VATSETTLEMENTAMOUNT,VATBILLINGAMOUNT,ISVATEMBEDDED,VATCOMMISSIONAMOUNT,ISCOMVATEMBEDDED,POSTDATE,POSTTIMESTAMP,PAYDATE,
			STGENERAL,LOGACTION,ACTIVITY_COMMENTS,CREATED_ON) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?
			)

		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,
			RECON_ID@BIGINT,INSTITUTION_ID@VARCHAR,SERNO@BIGINT,PARTITIONKEY@VARCHAR,SUBPARTITIONKEY@VARCHAR,MERSERNO@VARCHAR,MERACCSERNO@VARCHAR,BALANCESERNO@VARCHAR,BALANCEPARTITIONKEY@VARCHAR,BALANCESUBPARTITIONKEY@VARCHAR,
			PRODUCT@VARCHAR,INBATCHSERNO@BIGINT,TYPESERNO_COM@VARCHAR,TYPESERNO_REPORTS@VARCHAR,TYPESERNO_GLEDGER@VARCHAR,TYPESERNO_DIVERT@VARCHAR,TYPESERNO_FEES@VARCHAR,TYPESERNO_VAT@VARCHAR,MSGCLASS@VARCHAR,MSGTYPE@VARCHAR,
			TRXNTYPE@VARCHAR,ORIG_MSG_TYPE@VARCHAR,I000_MSG_TYPE@VARCHAR,I002_NUMBER@VARCHAR,I003_PROC_CODE@VARCHAR,I004_AMT_TRXN@DECIMAL,I005_AMT_SETTLE@VARCHAR,I006_AMT_BILL@VARCHAR,I007_LOAD_DATE@VARCHAR,I012_TRXN_TIME@VARCHAR,
			I013_TRXN_DATE@VARCHAR,I042_MERCH_ID@VARCHAR,I044_REASON_CODE@VARCHAR,I048_TEXT_DATA@VARCHAR,I049_CUR_TRXN@VARCHAR,I050_CUR_SETTLE@VARCHAR,I051_CUR_BILL@VARCHAR,EXRATETRXNTOSETTLEMENT@VARCHAR,CENTERCURRENCY@VARCHAR,CENTERAMOUNT@VARCHAR,
			INSTALMENTTYPE@VARCHAR,INSTALMENTSNUMBER@VARCHAR,INSTALMENTSEQ@VARCHAR,INSTALMENTREPAYMENTTYPE@VARCHAR,INSTALMENTOFFSET@VARCHAR,INSTALMENTORIGAMOUNT@VARCHAR,INSTALMENTPLANSERNO@VARCHAR,INSTALMENTINTERESTANCHORDATE@VARCHAR,INSTALMENTSERNO@VARCHAR,INSTALMENTPARTITIONKEY@VARCHAR,
			INSTALMENTSUBPARTITIONKEY@VARCHAR,SINGLE_MSG_FLAG@VARCHAR,ORIGINATOR@VARCHAR,ORIGINATORREASONCODE@VARCHAR,AMOUNT@VARCHAR,TIPAMOUNT@VARCHAR,BILLTIPAMOUNT@VARCHAR,COMAMOUNT@VARCHAR,ORIGCOMAMOUNT@VARCHAR,ISCOMEMBEDDED@VARCHAR,
			EMBEDDEDFEE@VARCHAR,VATAMOUNT@VARCHAR,VATSETTLEMENTAMOUNT@VARCHAR,VATBILLINGAMOUNT@VARCHAR,ISVATEMBEDDED@VARCHAR,VATCOMMISSIONAMOUNT@VARCHAR,ISCOMVATEMBEDDED@VARCHAR,POSTDATE@VARCHAR,POSTTIMESTAMP@VARCHAR,PAYDATE@VARCHAR,
			STGENERAL@VARCHAR,LOGACTION@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP

		</queryParam>
	</query>




	<query id="12">
		<name>AUTH_ISSUER_STG_INSERT_QRY</name>
		<targetTables>AUTH_ISSUER_STG</targetTables>
		<queryString>

			INSERT INTO AUTH_ISSUER_STG (
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,CREATED_ON,RECON_STATUS,RECON_ID,I007_TRANS_DT,CARDBIN,
			I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,I006_AMT_BILL,SERNO,LTIMESTAMP,I000_MSG_TYPE,SOURCE,I039_RSP_CD,REASONCODE,I003_PROC_CODE,I025_POS_COND,I002_NUMBER,I042_MERCH_ID,I043A_MERCH_NAME,
			I032_ACQUIRER_ID,I041_POS_ID,I019_ACQ_COUNTRY,I018_MERCH_TYPE,I037_RET_REF_NUM,I004_AMT_TRXN,
			TIME_IN_MILLIS,CARD_TYPE,BIN,GEOGRAPHY,BUSINESS_AREA,CHANNEL,NETWORK,ACTIVITY_COMMENTS,AMT_CENTER,I038_AUTH_ID,TRA_DATE,TRA_TIME,MAIN_REV_IND,PROC_CODE,TRAN_CUR
			) VALUES(
			?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?
			)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@VARCHAR,CREATED_ON@TIMESTAMP,
			RECON_STATUS@VARCHAR,RECON_ID@BIGINT,I007_TRANS_DT@BIGINT,CARDBIN@BIGINT,I049_CUR_TRXN@VARCHAR,I050_CUR_SETTLE@VARCHAR,I005_AMT_SETTLE@DECIMAL,I006_AMT_BILL@DECIMAL,SERNO@BIGINT,
			LTIMESTAMP@VARCHAR,I000_MSG_TYPE@VARCHAR,SOURCE@VARCHAR,I039_RSP_CD@VARCHAR,REASONCODE@VARCHAR,I003_PROC_CODE@VARCHAR,I025_POS_COND@VARCHAR,I002_NUMBER@VARCHAR,I042_MERCH_ID@VARCHAR,
			I043A_MERCH_NAME@VARCHAR,I032_ACQUIRER_ID@BIGINT,I041_POS_ID@VARCHAR,I019_ACQ_COUNTRY@VARCHAR,I018_MERCH_TYPE@BIGINT,I037_RET_REF_NUM@VARCHAR,I004_AMT_TRXN@DECIMAL,
			TIME_IN_MILLIS@BIGINT,CARD_TYPE@VARCHAR,BIN@BIGINT,GEOGRAPHY@VARCHAR,BUSINESS_AREA@VARCHAR,CHANNEL@VARCHAR,NETWORK@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,AMT_CENTER@DECIMAL,I038_AUTH_ID@VARCHAR,
			TRA_DATE@DATE,TRA_TIME@VARCHAR,MAIN_REV_IND@VARCHAR,PROC_CODE@VARCHAR,TRAN_CUR@VARCHAR
		</queryParam>
	</query>



	<query id="13">
		<name>AUTH_ISS_EX_INSERT_QRY</name>
		<targetTables>AUTH_ISSUER_STG_EX</targetTables>
		<queryString>
			INSERT INTO AUTH_ISSUER_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,
			UPDATED_ON,RECON_STATUS,RECON_ID,SERNO,LTIMESTAMP,I000_MSG_TYPE,TXN_SOURCE,I039_RSP_CD,REASONCODE,I003_PROC_CODE,
			I006_AMT_BILL,TXN_SOURCE2,I025_POS_COND,CARD_NO,I042_MERCH_ID,I043A_MERCH_NAME,I032_ACQUIRER_ID,I018_MERCH_TYPE,I041_POS_ID,I019_ACQ_COUNTRY,
			TXN_SOURCE3,I018_MERCH_TYPE2,I037_RET_REF_NUM,I004_AMT_TRXN,
			CARDBIN,I049_CUR_TRXN,I050_CUR_SETTLE,I005_AMT_SETTLE,I002_NUMBER,ACTIVITY_COMMENTS,CREATED_ON

			)VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?

			)

		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,
			RECON_STATUS@VARCHAR,RECON_ID@BIGINT,SERNO@VARCHAR,LTIMESTAMP@VARCHAR,I000_MSG_TYPE@VARCHAR,TXN_SOURCE@VARCHAR,I039_RSP_CD@VARCHAR,
			REASONCODE@VARCHAR,I003_PROC_CODE@VARCHAR,I006_AMT_BILL@VARCHAR,TXN_SOURCE2@VARCHAR,I025_POS_COND@VARCHAR,
			CARD_NO@VARCHAR,I042_MERCH_ID@VARCHAR,I043A_MERCH_NAME@VARCHAR,I032_ACQUIRER_ID@VARCHAR,I018_MERCH_TYPE@VARCHAR,
			I041_POS_ID@VARCHAR,I019_ACQ_COUNTRY@VARCHAR,TXN_SOURCE3@VARCHAR,I018_MERCH_TYPE2@VARCHAR,I037_RET_REF_NUM@VARCHAR,I004_AMT_TRXN@VARCHAR,
			CARDBIN@VARCHAR,I049_CUR_TRXN@VARCHAR,I050_CUR_SETTLE@VARCHAR,I005_AMT_SETTLE@VARCHAR,I002_NUMBER@VARCHAR,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
	</query>




	<query id="14">
        <name>QCB_STG_INSERT_QRY</name>
		<targetTables>QCB_STG</targetTables>
        <queryString>insert into QCB_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,
        RECON_STATUS,RECON_ID,CHANNEL,ACQ_BANK_CODE,ISS_BANK_CODE,TERMINAL_ID,CARD_NUMBER,TRANS_AMOUNT,
        CURRENCY_CODE,TRAN_DATE,TRAN_TIME,SETTL_DATE,RETR_REF_NO,TRAN_TYPE,ACC_TYPE,RESP_CODE,RETAIL_ID,
        REF_NO,BIN,TRAN_TYPE_DES,ACC_TYPE_DES,BUSINES_AREA,TIME_IN_MILLIS,ACTIVITY_COMMENTS,MAIN_REV_IND,CREATED_ON,TRAN_CUR) 
        values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
        </queryString>
		<queryParam>SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,
		USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,CHANNEL@VARCHAR,
		ACQ_BANK_CODE@INTEGER,ISS_BANK_CODE@INTEGER,TERMINAL_ID@VARCHAR,CARD_NUMBER@VARCHAR,TRANS_AMOUNT@DECIMAL,
		CURRENCY_CODE@INTEGER,TRAN_DATE@DATE,TRAN_TIME@VARCHAR,SETTL_DATE@DATE,RETR_REF_NO@VARCHAR,TRAN_TYPE@VARCHAR,
		ACC_TYPE@INTEGER,RESP_CODE@VARCHAR,RETAIL_ID@BIGINT,REF_NO@VARCHAR,BIN@BIGINT,TRAN_TYPE_DES@VARCHAR,
		ACC_TYPE_DES@VARCHAR,BUSINES_AREA@VARCHAR,TIME_IN_MILLIS@BIGINT,ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,TRAN_CUR@VARCHAR
		</queryParam>
    </query>
	
		
	
		<query id="14">
        <name>QCB_STG_EX_INSERT_QRY</name>
		<targetTables>QCB_STG</targetTables>
        <queryString>
		insert into QCB_STG_EX(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,CHANNEL,ref_no1,ref_no2,ref_no3,card_number,transaction_amount,city_code,tran_date,tra_time,value_date,RETR_REF_NO,ref_no5,ref_no6,resp_code,ref_no7,ACTIVITY_COMMENTS,CREATED_ON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
		sid@BIGINT,version@INTEGER,ACTIVE_INDEX@VARCHAR,status@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,CHANNEL@VARCHAR,ref_no1@INTEGER,ref_no2@INTEGER,ref_no3@INTEGER,card_number@VARCHAR,transaction_amount@NUMERIC,city_code@INTEGER,tran_date@DATE,tra_time@VARCHAR,value_date@DATE,RETR_REF_NO@VARCHAR,ref_no5@INTEGER,ref_no6@INTEGER,resp_code@VARCHAR,ref_no7@BIGINT,ACTIVITY_COMMENTS@VARCHAR,CREATED_ON@TIMESTAMP
		</queryParam>
    </query>
	
		
<query id="15">
        <name>QPAY_STG_INSERT_QRY</name>
		<targetTables>QPAY_STG</targetTables>
         <queryString>
	INSERT INTO QPAY_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,CREATED_ON,
			UPDATED_ON,RECON_STATUS,RECON_ID,BACKEND_ID,TRAN_DATE,AMOUNT,CURRENCY,DATE,TXN_CODE,CARD_NUMBER,ACQ_REF_NO,
			PAYMENT_UNQ_NO,SOURCE_TYPE,SOURCE_ID,SOURCE_MXP_ID,SOURCE_BANK_ACC,SOURCE_ACC_CURRENCY,
			SOURCE_AMOUNT,DEST_TYPE,DEST_ID,DES_MXP_ACC,DES_BANK_ACC,DES_ACC_CURRENCY,DES_AMT,TXN_SOURCE,
			REF_NO,RETR_REF_NO,TIME_IN_MILLIS,TRAN_CUR,MAIN_REV_IND) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
	SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,
	USER_ID@VARCHAR,OPERATION@VARCHAR,CREATED_ON@TIMESTAMP,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,
	BACKEND_ID@VARCHAR,TRAN_DATE@VARCHAR,AMOUNT@DECIMAL,CURRENCY@INTEGER,DATE@VARCHAR,TXN_CODE@INTEGER,
	CARD_NUMBER@VARCHAR,ACQ_REF_NO@VARCHAR,PAYMENT_UNQ_NO@VARCHAR,SOURCE_TYPE@INTEGER,SOURCE_ID@VARCHAR,
	SOURCE_MXP_ID@VARCHAR,SOURCE_BANK_ACC@VARCHAR,SOURCE_ACC_CURRENCY@INTEGER,SOURCE_AMOUNT@DECIMAL,
	DEST_TYPE@VARCHAR,DEST_ID@VARCHAR,DES_MXP_ACC@VARCHAR,DES_BANK_ACC@VARCHAR,DES_ACC_CURRENCY@INTEGER,
	DES_AMT@DECIMAL,TXN_SOURCE@VARCHAR,REF_NO@INTEGER,RETR_REF_NO@VARCHAR,TIME_IN_MILLIS@BIGINT,TRAN_CUR@VARCHAR,MAIN_REV_IND@VARCHAR
	</queryParam>
    </query>
	
	<query id="15">
        <name>QPAY_STG_EX_INSERT_QRY</name>
		<targetTables>QPAY_STG</targetTables>
         <queryString>
INSERT INTO QPAY_STG(BACKEND_ID,TRAN_DATE,AMOUNT,CURRENCY,DATE,TXN_CODE,CARD_NUMBER,ACQ_REF_NO,PAYMENT_UNQ_NO,SOURCE_TYPE,SOURCE_ID,SOURCE_MXP_ID,
SOURCE_BANK_ACC,SOURCE_ACC_CURRENCY,SOURCE_AMOUNT,DEST_TYPE,DEST_ID,DES_MXP_ACC,DES_BANK_ACC,DES_ACC_CURRENCY,DES_AMT,TXN_SOURCE,REF_NO,CREATED_ON) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
BACKEND_ID@VARCHAR,TRAN_DATE@VARCHAR,AMOUNT@VARCHAR,CURRENCY@VARCHAR,DATE@VARCHAR,TXN_CODE@VARCHAR,CARD_NUMBER@VARCHAR,ACQ_REF_NO@VARCHAR,PAYMENT_UNQ_NO@VARCHAR,SOURCE_TYPE@VARCHAR,SOURCE_ID@VARCHAR,SOURCE_MXP_ID@VARCHAR,
SOURCE_BANK_ACC@VARCHAR,SOURCE_ACC_CURRENCY@VARCHAR,SOURCE_AMOUNT@VARCHAR,DEST_TYPE@VARCHAR,DEST_ID@VARCHAR,DES_MXP_ACC@VARCHAR,DES_BANK_ACC@VARCHAR,DES_ACC_CURRENCY@VARCHAR,DES_AMT@VARCHAR,TXN_SOURCE@VARCHAR,REF_NO@VARCHAR,CREATED_ON@TIMESTAMP
	</queryParam>
    </query>
	
	
	<query id="16">
        <name>VISA_STG_INSERT_QRY</name>
		<targetTables>VISA_STG</targetTables>
        <queryString>insert into VISA_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,bat_num,xmit_date,time,card_number,card_no,retrieval_ref_number,trace_number,issuer_trmnl,tran_type,procss_code,ent_mod,cn_stp,rsp_cd,reas_code,transaction_amount,kes_currency,settlement_amount,cr_currency,ca_id,terminal_id,fpi,ci,report_date,tr_id,aci,fee_juris,routing,fee_level,MAIN_REV_IND,CREATED_ON,PROC_CODE) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)</queryString>
		<queryParam>SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,bat_num@INTEGER,xmit_date@VARCHAR,time@VARCHAR,card_number@VARCHAR,card_no@INTEGER,retrieval_ref_number@VARCHAR,trace_number@VARCHAR,issuer_trmnl@VARCHAR,tran_type@VARCHAR,procss_code@VARCHAR,ent_mod@VARCHAR,cn_stp@VARCHAR,rsp_cdv@VARCHAR,reas_code@VARCHAR,
		transaction_amount@DECIMAL,kes_currency@VARCHAR,settlement_amount@VARCHAR,
		cr_currency@VARCHAR,ca_id@VARCHAR,terminal_id@VARCHAR,fpi@VARCHAR,ci@VARCHAR,report_date@DATE,
		tr_id@VARCHAR,aci@VARCHAR,fee_juris@VARCHAR,routing@VARCHAR,fee_level@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,PROC_CODE@VARCHAR
		</queryParam>
    </query>
	<query id="14">
        <name>VISA_STG_EX_INSERT_QRY</name>
		<targetTables>VISA_STG_EX</targetTables>
         <queryString>insert into VISA_STG_EX(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,bat_num,xmit_date,time,card_number,card_no,retrieval_ref_number,trace_number,issuer_trmnl,tran_type,procss_code,ent_mod,cn_stp,rsp_cd,reas_code,transaction_amount,kes_currency,settlement_amount,cr_currency,ca_id,terminal_id,fpi,ci,report_date,tr_id,aci,fee_juris,routing,fee_level,MAIN_REV_IND,CREATED_ON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)</queryString>
		<queryParam>SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,bat_num@INTEGER,xmit_date@VARCHAR,time@VARCHAR,card_number@VARCHAR,card_no@INTEGER,retrieval_ref_number@VARCHAR,trace_number@VARCHAR,issuer_trmnl@VARCHAR,tran_type@VARCHAR,procss_code@VARCHAR,ent_mod@VARCHAR,cn_stp@VARCHAR,rsp_cdv@VARCHAR,reas_code@VARCHAR,transaction_amount@VARCHAR,kes_currency@VARCHAR,settlement_amount@VARCHAR,
		cr_currency@VARCHAR,ca_id@VARCHAR,terminal_id@VARCHAR,fpi@VARCHAR,ci@VARCHAR,report_date@DATE,tr_id@VARCHAR,aci@VARCHAR,fee_juris@VARCHAR,routing@VARCHAR,fee_level@VARCHAR,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP</queryParam>
    
    </query>
		
		
	<query id="16">
        <name>CUP_STG_INSERT_QRY</name>
		<targetTables>CUP_STG</targetTables>
        <queryString>
			insert into CUP_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,acq_no,sender_no,system_trace_no,tran_tranmission_time,pan,trans_amount,msg_type,trans_type_code,tran_frst2,merchant_type,teminal_code,
				acq_identy_code,name_add_acceptor,retr_ref_no,point_of_service_condition_code,authorizer_resp_code,receiver_no,orgnl_sys_trace_no,
				tran_resp_code,tran_currency,service_entry_mode,settlement_currency,settlement_volume,sett_exchange_rate,sett_date,conversion_date,
				receivable_service_fee,payable_service_no,interchange_fee,exchange_rate_interchange_fee,exchange_rate_service_fee,transaction_fee,
				currency_transaction_fee,exchange_rate_tran_fee,resered,CREATED_ON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
		SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,acq_no@INTEGER,sender_no@INTEGER,system_trace_no@INTEGER,tran_tranmission_time@VARCHAR,
		pan@BIGINT,trans_amount@DECIMAL,msg_type@INTEGER,trans_type_code@VARCHAR,tran_frst2@VARCHAR,merchant_type@INTEGER,teminal_code@INTEGER,acq_identy_code@INTEGER,name_add_acceptor@VARCHAR,
		retr_ref_no@BIGINT,point_of_service_condition_code@INTEGER,authorizer_resp_code@VARCHAR,receiver_no@BIGINT,orgnl_sys_trace_no@VARCHAR,tran_resp_code@VARCHAR,tran_currency@VARCHAR,
		service_entry_mode@VARCHAR,settlement_currency@VARCHAR,settlement_volume@VARCHAR,sett_exchange_rate@VARCHAR,sett_date@DATE,conversion_date@VARCHAR,receivable_service_fee@VARCHAR,
		payable_service_no@VARCHAR,interchange_fee@VARCHAR,exchange_rate_interchange_fee@VARCHAR,exchange_rate_service_fee@VARCHAR,transaction_fee@VARCHAR,currency_transaction_fee@VARCHAR,
		exchange_rate_tran_fee@VARCHAR,resered@VARCHAR,CREATED_ON@TIMESTAMP
	</queryParam>
    </query>
	
	<query id="14">
        <name>CUP_STG_EX_INSERT_QRY</name>
		<targetTables>CUP_STG</targetTables>
         <queryString>
			insert into CUP_STG(acq_no,sender_no,system_trace_no,tran_tranmission_time,pan,trans_volume,msg_type,trans_type_code,merchant_type,teminal_code,
				acq_identy_code,name_add_acceptor,retrieval_no,point_of_service_condition_code,authorizer_resp_code,receiver_no,orgnl_sys_trace_no,
				tran_resp_code,tran_currency,service_entry_mode,settlement_currency,settlement_volume,sett_exchange_rate,sett_date,conversion_date,
				receivable_service_fee,payable_service_no,interchange_fee,exchange_rate_interchange_fee,exchange_rate_service_fee,transaction_fee,
				currency_transaction_fee,exchange_rate_tran_fee,resered,CREATED_ON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
		acq_no@INTEGER,sender_no@INTEGER,system_trace_no@INTEGER,tran_tranmission_time@VARCHAR,
		pan@BIGINT,trans_volume@VARCHAR,msg_type@VARCHAR,trans_type_code@VARCHAR,merchant_type@VARCHAR,teminal_code@VARCHAR,acq_identy_code@VARCHAR,name_add_acceptor@VARCHAR,
		retrieval_no@VARCHAR,point_of_service_condition_code@VARCHAR,authorizer_resp_code@VARCHAR,receiver_no@VARCHAR,orgnl_sys_trace_no@VARCHAR,tran_resp_code@VARCHAR,tran_currency@VARCHAR,
		service_entry_mode@VARCHAR,settlement_currency@VARCHAR,settlement_volume@VARCHAR,sett_exchange_rate@VARCHAR,sett_date@VARCHAR,conversion_date@VARCHAR,receivable_service_fee@VARCHAR,
		payable_service_no@VARCHAR,interchange_fee@VARCHAR,exchange_rate_interchange_fee@VARCHAR,exchange_rate_service_fee@VARCHAR,transaction_fee@VARCHAR,currency_transaction_fee@VARCHAR,
		exchange_rate_tran_fee@VARCHAR,resered@VARCHAR,CREATED_ON@TIMESTAMP
	</queryParam>
    </query>	

	<query id="14">
        <name>MASTER_STG_INSERT_QRY</name>
		<targetTables>MASTER_STG</targetTables>
         <queryString>
		insert into MASTER_STG(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,msg_type,switch_serial_no,processor_acq_iss,processor_id,tran_date,tran_time,pan,primary_acc_no,
				process_code,trace_no,merchant_type,pos_entry,refrence_no,acq_inst_id,terminal_id,resp_code,brand,advice_reason_code,
				intra_arg_code,authorize_id,currecy_code_tran,implied_decimal,trans_amount,trans_amt_dr_cr_indicator,cash_back_amt,
				cash_back_amt_dr_cr_indicator,access_fee,access_fee_dr_cr_indicator,currecy_code_sett,implied_decimal_sett,conversion_rate_sett,
				sett_amount,sett_amount_dr_cr_indicator,interchange_fee,interchange_fee_dr_cr_indicator,service_level_indicator,resp_code1,
				filler,positive_id_indicator,atm_surchange_fee,cross_border_indicator,cross_border_currency_indicator,visa_isa_fee_indicator,
				req_amount,filler_1,tran_no,filler_2,BUSINESS_AREA,NETWORK,TIME_IN_MILLIS,MAIN_REV_IND,CREATED_ON,TRAN_CUR,PROC_CODE,CHANNEL) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
	SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,msg_type@VARCHAR,switch_serial_no@BIGINT,processor_acq_iss@VARCHAR,processor_id@INTEGER,tran_date@DATE,tran_time@VARCHAR,pan@INTEGER,primary_acc_no@VARCHAR,
	process_code@VARCHAR,trace_no@VARCHAR,merchant_type@VARCHAR,pos_entry@VARCHAR,refrence_no@VARCHAR,acq_inst_id@VARCHAR,terminal_id@VARCHAR,resp_code@VARCHAR,brand@VARCHAR,advice_reason_code@VARCHAR,
	intra_arg_code@VARCHAR,authorize_id@VARCHAR,currecy_code_tran@VARCHAR,implied_decimal@VARCHAR,trans_amount@DECIMAL,trans_amt_dr_cr_indicator@VARCHAR,cash_back_amt@DECIMAL,
	cash_back_amt_dr_cr_indicator@VARCHAR,access_fee@VARCHAR,access_fee_dr_cr_indicator@VARCHAR,currecy_code_sett@VARCHAR,implied_decimal_sett@VARCHAR,conversion_rate_sett@VARCHAR,
	sett_amount@DECIMAL,sett_amount_dr_cr_indicator@VARCHAR,interchange_fee@VARCHAR,interchange_fee_dr_cr_indicator@VARCHAR,service_level_indicator@VARCHAR,resp_code1@VARCHAR,
	filler@VARCHAR,positive_id_indicator@VARCHAR,atm_surchange_fee@VARCHAR,cross_border_indicator@VARCHAR,cross_border_currency_indicator@VARCHAR,visa_isa_fee_indicator@VARCHAR,
	req_amount@DECIMAL,filler_1@VARCHAR,tran_no@VARCHAR,filler_2@VARCHAR,BUSINESS_AREA@VARCHAR,NETWORK@VARCHAR,TIME_IN_MILLIS@BIGINT,MAIN_REV_IND@VARCHAR,CREATED_ON@TIMESTAMP,TRAN_CUR@VARCHAR,PROC_CODE@VARCHAR,CHANNEL@VARCHAR
	</queryParam>
    </query>
	
	<query id="14">
        <name>MASTER_STG_EX_INSERT_QRY</name>
		<targetTables>MASTER_STG</targetTables>
           <queryString>
		insert into MASTER_STG_EX(SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION,UPDATED_ON,RECON_STATUS,RECON_ID,msg_type,switch_serial_no,processor_acq_iss,processor_id,tran_date,tran_time,pan,primary_acc_no,
				process_code,trace_no,merchant_type,pos_entry,refrence_no,acq_inst_id,terminal_id,resp_code,brand,advice_reason_code,
				intra_arg_code,authorize_id,currecy_code_tran,implied_decimal,trans_amount,trans_amt_dr_cr_indicator,cash_back_amt,
				cash_back_amt_dr_cr_indicator,access_fee,access_fee_dr_cr_indicator,currecy_code_sett,implied_decimal_sett,conversion_rate_sett,
				sett_amount,sett_amount_dr_cr_indicator,interchange_fee,interchange_fee_dr_cr_indicator,service_level_indicator,resp_code1,
				filler,positive_id_indicator,atm_surchange_fee,cross_border_indicator,cross_border_currency_indicator,visa_isa_fee_indicator,
				req_amount,filler_1,tran_no,filler_2,TIME_IN_MILLIS,CREATED_ON) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>

	<queryParam>
	SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,RECON_STATUS@VARCHAR,RECON_ID@BIGINT,msg_type@VARCHAR,switch_serial_no@VARCHAR,processor_acq_iss@VARCHAR,processor_id@VARCHAR,tran_date@DATE,tran_time@VARCHAR,pan@VARCHAR,primary_acc_no@VARCHAR,
	process_code@VARCHAR,trace_no@VARCHAR,merchant_type@VARCHAR,pos_entry@VARCHAR,refrence_no@VARCHAR,acq_inst_id@VARCHAR,terminal_id@VARCHAR,resp_code@VARCHAR,brand@VARCHAR,advice_reason_code@VARCHAR,
	intra_arg_code@VARCHAR,authorize_id@VARCHAR,currecy_code_tran@VARCHAR,implied_decimal@VARCHAR,trans_amount@VARCHAR,trans_amt_dr_cr_indicator@VARCHAR,cash_back_amt@VARCHAR,
	cash_back_amt_dr_cr_indicator@VARCHAR,access_fee@VARCHAR,access_fee_dr_cr_indicator@VARCHAR,currecy_code_sett@VARCHAR,implied_decimal_sett@VARCHAR,conversion_rate_sett@VARCHAR,
	sett_amount@VARCHAR,sett_amount_dr_cr_indicator@VARCHAR,interchange_fee@VARCHAR,interchange_fee_dr_cr_indicator@VARCHAR,service_level_indicator@VARCHAR,resp_code1@VARCHAR,
	filler@VARCHAR,positive_id_indicator@VARCHAR,atm_surchange_fee@VARCHAR,cross_border_indicator@VARCHAR,cross_border_currency_indicator@VARCHAR,visa_isa_fee_indicator@VARCHAR,
	req_amount@VARCHAR,filler_1@VARCHAR,tran_no@VARCHAR,filler_2@VARCHAR,TIME_IN_MILLIS@BIGINT,CREATED_ON@TIMESTAMP
	</queryParam>
    </query>
    
    
    <query id="13">
		<name>DPAY_STG_INSERT_QRY</name>
		<targetTables>DPAY_STG</targetTables>
		<queryString>

			INSERT INTO DPAY_STG (
 SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS, 
 USER_ID,OPERATION,UPDATED_ON,CREATED_ON,SERNO, EMP_CODE,COMP_CODE, 
 CARDNO,BALANCE,CREDITLIMIT, BATCHSERNO, I004_AMT_TRXN ,I007_LOAD_DATE, 
 I013_TRXN_DATE ,I048_TEXT_DATA ,I049_CUR_TRXN,AMOUNT, 
 POSTDATE,PARTITIONKEY ,POSTTIMESTAMP ,RECON_STATUS ,RECON_ID, 
 ACTIVITY_COMMENTS ,MAIN_REV_IND,TRAN_CUR) VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			? 
			)
		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR, 
            USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@TIMESTAMP,CREATED_ON@TIMESTAMP,SERNO@BIGINT,EMP_CODE@BIGINT,COMP_CODE@VARCHAR, 
            CARDNO@VARCHAR,BALANCE@DECIMAL,CREDITLIMIT@DECIMAL,BATCHSERNO@BIGINT,I004_AMT_TRXN@DECIMAL,I007_LOAD_DATE@DATE, 
            I013_TRXN_DATE@DATE,I048_TEXT_DATA@VARCHAR,I049_CUR_TRXN@VARCHAR,AMOUNT@DECIMAL, 
            POSTDATE@DATE,PARTITIONKEY@BIGINT,POSTTIMESTAMP@DATE,RECON_STATUS@VARCHAR,RECON_ID@BIGINT, 
            ACTIVITY_COMMENTS@VARCHAR,MAIN_REV_IND@VARCHAR,TRAN_CUR@VARCHAR 
		</queryParam>
	</query>



	<query id="14">
		<name>DPAY_STG_EX_INSERT_QRY</name>
		<targetTables>DPAY_STG_EX</targetTables>
		<queryString>
			INSERT INTO DPAY_STG_EX(
			SID,VERSION,ACTIVE_INDEX,STATUS,COMMENTS,WORKFLOW_STATUS,USER_ID,OPERATION ,UPDATED_ON, 
           CREATED_ON,SERNO,EMP_CODE,COMP_CODE,CARDNO,BALANCE,CREDITLIMIT,BATCHSERNO,I004_AMT_TRXN, 
           I007_LOAD_DATE,I013_TRXN_DATE,I048_TEXT_DATA,I049_CUR_TRXN,AMOUNT,POSTDATE,PARTITIONKEY, 
           POSTTIMESTAMP,ACTIVITY_COMMENTS)VALUES(
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?,?,?,?,
			?,?,?,?,?,?,?)

		</queryString>
		<queryParam>
			SID@BIGINT,VERSION@INTEGER,ACTIVE_INDEX@VARCHAR,STATUS@VARCHAR,COMMENTS@VARCHAR,WORKFLOW_STATUS@VARCHAR,USER_ID@VARCHAR,OPERATION@VARCHAR,UPDATED_ON@VARCHAR  
            ,CREATED_ON@VARCHAR,SERNO@BIGINT,EMP_CODE@VARCHAR,COMP_CODE@VARCHAR,CARDNO@VARCHAR,BALANCE@VARCHAR,CREDITLIMIT@VARCHAR,BATCHSERNO@VARCHAR,I004_AMT_TRXN@VARCHAR  
            ,I007_LOAD_DATE@VARCHAR,I013_TRXN_DATE@VARCHAR,I048_TEXT_DATA@VARCHAR,I049_CUR_TRXN@VARCHAR,AMOUNT@VARCHAR,POSTDATE@VARCHAR,PARTITIONKEY@VARCHAR  
            ,POSTTIMESTAMP@VARCHAR,ACTIVITY_COMMENTS@VARCHAR 
		</queryParam>
	</query>
    
    
	
	<query id="1">
		<name>MDT_CHANNELS</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_CHANNELS a where a.active_index='Y'
			and
			version=(select max(version) from MDT_CHANNELS b where
			a.CHANNEL_NAME=b.CHANNEL_NAME and a.sid=b.sid)
		</queryString>
		<queryParam>

		</queryParam>
	</query>
	

<query id="1">
		<name>MDT_IRIS_CURRENCY</name>
		<targetTables>abc</targetTables>
		<queryString>
			SELECT * FROM MDT_IRIS_CURRENCY
		</queryString>
		<queryParam>

		</queryParam>
	</query>


	<query id="1">
		<name>MDT_CARD_BIN</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_DB_CARD_BINS a where a.ACTIVE_INDEX='Y'
			and
			VERSION=(select max(VERSION) from MDT_DB_CARD_BINS b where
			a.PRODUCT_TYPE=b.PRODUCT_TYPE and a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>


	<query id="1">
		<name>MDT_PROCESS_CODES</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_PROCESS_CODES a where
			a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from
			MDT_PROCESS_CODES b where a.TRAN_NAME=b.TRAN_NAME and a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>


	<query id="1">
		<name>ONUS_AUTH</name>
		<targetTables>abc</targetTables>
		<queryString>
			select CHANNEL_ID from MDT_CHANNELS where CHANNEL_NAME
			in('ATM','CTL','IRIS','HSM','HOST')
		</queryString>
		<queryParam>

		</queryParam>
	</query>


	<query id="1">
		<name>ISS_AUTH</name>
		<targetTables>abc</targetTables>
		<queryString>
			select CHANNEL_ID from MDT_CHANNELS where CHANNEL_NAME
			in('IRIS','CTL')
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>ISS_ACQ_AUTH</name>
		<targetTables>abc</targetTables>
		<queryString>
			select CHANNEL_ID from MDT_CHANNELS where CHANNEL_NAME
			in('VISA','NAPS')
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>PROC_ATM</name>
		<targetTables>abc</targetTables>
		<queryString>
			select TRAN_CODE from MDT_PROCESS_CODES where TRAN_NAME
			in('Withdrawal','Bill payment','Cash Deposit','Cardless Deposit','E
			Voucher')
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>RESPONSE_ATM</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_RESPONSE_CODES a where
			a.ACTIVE_INDEX='Y' and
			VERSION=(select max(VERSION) from
			MDT_RESPONSE_CODES b where
			a.NAME=b.NAME and a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>


	<query id="1">
		<name>GL_ACC</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_GL_ACCOUNTS a where a.ACTIVE_INDEX='Y'
			and VERSION=(select max(VERSION) from MDT_GL_ACCOUNTS b where
			a.ACCOUNT_NAME=b.ACCOUNT_NAME and a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>GL_SUS_ACC</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_ATM_SUSPENSE_ACCOUNTS a where
			a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from
			MDT_ATM_SUSPENSE_ACCOUNTS b where a.ATM_LOCATION=b.ATM_LOCATION and
			a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>CHANN_SERO_ACC</name>
		<targetTables>abc</targetTables>
		<queryString>
			select * from MDT_CHANNEL_SERNO a where
			a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from
			MDT_CHANNEL_SERNO b where a.CHANNELNAME=b.CHANNELNAME and
			a.SID=b.SID)
		</queryString>
		<queryParam>

		</queryParam>
	</query>

	<query id="1">
		<name>ACQ_28_40</name>
		<targetTables>abc</targetTables>
		<queryString>
			select CHANNEL_ID FROM MDT_CHANNELS WHERE CHANNEL_NAME
			in('NAPS','MDS','CUP')
		</queryString>
		<queryParam>

		</queryParam>
	</query>

  <query id="13">
		<name>LEDG_CLOSING_INSERT_STG</name>
		<targetTables>LEDG_CLOSING_STG</targetTables>
		<queryString>

			INSERT INTO LEDG_CLOSING_STG(BRA_CODE,CUS_NUM,CUR_CODE,LED_CODE,SUB_ACCT_CODE,PRE_DAY_CRNT_BAL,PRE_BANK_DATE)
			 VALUES(
			    ?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			  BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,PRE_DAY_CRNT_BAL@DECIMAL,PRE_BANK_DATE@DATE
		</queryParam>
	</query>



	<query id="14">
		<name>LEDG_CLOSING_INSERT_STG_EX</name>
		<targetTables>LEDG_CLOSING_STG_EX</targetTables>
		<queryString>
			INSERT INTO LEDG_CLOSING_STG_EX(BRA_CODE@VARCHAR,CUS_NUM@VARCHAR,CUR_CODE@VARCHAR,LED_CODE@VARCHAR,SUB_ACCT_CODE@VARCHAR,PRE_DAY_CRNT_BAL@DECIMAL,PRE_BANK_DATE@DATE
			 )VALUES(
			?,?,?,?,?,?,?)

		</queryString>
		<queryParam>
			 
		</queryParam>
	</query>
    



  <query id="13">
		<name>REPL_INSERT_STG</name>
		<targetTables>REPL_STG</targetTables>
		<queryString>
			INSERT INTO BCS_ATM_REPL(DISPLAYID,START_DATE,START_TIME,END_DATE,END_TIME,NOTE_LEFT_CAS1,NOTE_LEFT_CAS2,
   		   NOTE_LEFT_CAS3,NOTE_LEFT_CAS4,NOTE_ADDED_CAS1,NOTE_ADDED_CAS2,NOTE_ADDED_CAS4,NOTE_ADDED_CAS3,START_DATETIME,END_DATETIME)
		VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
		</queryString>
		<queryParam>
			DISPLAYID@VARCHAR,START_DATE@DATE,START_TIME@VARCHAR,END_DATE@DATE,END_TIME@VARCHAR,
   			NOTE_LEFT_CAS1@BIGINT,NOTE_LEFT_CAS2@BIGINT,NOTE_LEFT_CAS3@BIGINT,NOTE_LEFT_CAS4@BIGINT,NOTE_ADDED_CAS1@BIGINT,
    	    NOTE_ADDED_CAS2@BIGINT,NOTE_ADDED_CAS4@BIGINT,NOTE_ADDED_CAS3@BIGINT,START_DATETIME@TIMESTAMP,END_DATETIME@TIMESTAMP
		</queryParam>
	</query>



	<query id="14">
		<name>REPL_INSERT_STG_EX</name>
		<targetTables>REPL_STG_EX</targetTables>
		<queryString>
			INSERT INTO BCS_ATM_REPL(DISPLAYID,START_DATE,START_TIME,END_DATE,END_TIME,NOTE_LEFT_CAS1,NOTE_LEFT_CAS2,
      NOTE_LEFT_CAS3,NOTE_LEFT_CAS4,NOTE_ADDED_CAS1,NOTE_ADDED_CAS2,NOTE_ADDED_CAS4,NOTE_ADDED_CAS3,START_DATETIME,END_DATETIME)
		VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)

		</queryString>
		<queryParam>
			 DISPLAYID@VARCHAR,START_DATE@DATE,START_TIME@VARCHAR,END_DATE@DATE,END_TIME@VARCHAR,
     NOTE_LEFT_CAS1@BIGINT,NOTE_LEFT_CAS2@BIGINT,NOTE_LEFT_CAS3@BIGINT,NOTE_LEFT_CAS4@BIGINT,NOTE_ADDED_CAS1@BIGINT,
      NOTE_ADDED_CAS2@BIGINT,NOTE_ADDED_CAS4@BIGINT,NOTE_ADDED_CAS3@BIGINT,START_DATETIME@TIMESTAMP,END_DATETIME@TIMESTAMP
		</queryParam>
	</query>


	USER_LOGIN_QUERY
	
	<query id="15">
		<name>USER_LOGIN_QUERY</name>
		<targetTables>Users</targetTables>
		<queryString>select * from users rl where status='APPROVED' and version = (select MAX(version) from users where user_id=rl.user_id and status='APPROVED') and user_id=?</queryString>
		<queryParam>user_id@VARCHAR</queryParam>
	</query>
	

<query id="16">
		<name>GET_DATE_DIFF_FOR_PASSWORD_EXPIRY_NOTIFICATION</name>
		<queryType>Select</queryType>
		<queryString>select DATEDIFF(DAY,getdate(),pwd_exp_date) as expdate
			from users where user_id=?
		</queryString>
		<queryParam>user_id@VARCHAR</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="17">
		<name>LOAD_PASSWORD_POLICY</name>
		<queryType>SELECT</queryType>
		<queryString>
			SELECT * FROM PASSWORD_POLICY WHERE VERSION=(SELECT
			MAX(VERSION) FROM
			PASSWORD_POLICY) AND ACTIVE_INDEX='Y'
		</queryString>
		<queryParam></queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
<query id="19">
		<name>LOCK_USER_ACCOUNT_QUERY</name>
		<queryType>Update</queryType>
		<queryString>UPDATE users set updated_on= ? ,account_status=? where
			user_id=?
		</queryString>
		<queryParam>updated_on@TIMESTAMP,account_status@VARCHAR,user_id@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query>
	
	
	<query id="18">
		<name>INSERT_USER_lOG_AUDIT_QRY</name>
		<queryType>Insert</queryType>
		<queryString>

			insert into User_Audit
			(
			user_id,action,date_time,bussiness_area,recon_name,user_role
			)
			values
			(
			?,?,?,?,?,?
			)

		</queryString>
		<queryParam>user_id@VARCHAR,action@VARCHAR,date_time@TIMESTAMP,bussiness_area@VARCHAR,recon_name@VARCHAR,user_role@VARCHAR
		</queryParam>
		<queryParamLiteralValues></queryParamLiteralValues>
	</query> -->
	
	
</queries>
