package com.ascent.ds.operations;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.persistance.LoadRegulator;
import com.ascent.scheduler.ExportExcel1;
import com.ascent.scheduler.MailTrigger;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
/**
 * <AUTHOR>
 *
 */
public class EscalationModule extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = 1L;

	private static final String GET_ALL_PENDING_CASES = "GET_ALL_PENDING_CASES";

	private static Logger logger = LogManager.getLogger(CaseManagement.class.getName());

	@SuppressWarnings("unchecked")
	public void escalationModule() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> allpendingreconList = new ArrayList<Map<String, Object>>();

		@SuppressWarnings("rawtypes")
		Map map = new HashMap();
		try {
			queryConf = queryConfs.getQueryConf(GET_ALL_PENDING_CASES);
			allpendingreconList = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);

			for (Map<String, Object> pendingCasesMap : allpendingreconList) {
				System.out.println(map.keySet()+"  jujeu  "+pendingCasesMap.get("RECON")+ "_" +pendingCasesMap.get("AGELIMIT")+"_"+ pendingCasesMap.get("DEPARTMENT"));

				if (map.keySet().contains(pendingCasesMap.get("RECON")+ "_" +pendingCasesMap.get("AGELIMIT")+"_"+ pendingCasesMap.get("DEPARTMENT"))) {
				
					List reconwiseCaseList = (List) map.get(pendingCasesMap.get("RECON")+ "_" +pendingCasesMap.get("AGELIMIT")+"_"+ pendingCasesMap.get("DEPARTMENT"));
					reconwiseCaseList.add(pendingCasesMap);
				} else {
					
					List reconwiseCaseList = new ArrayList();
					reconwiseCaseList.add(pendingCasesMap);
					//map.put(pendingCasesMap.get("RECON") + "_" + pendingCasesMap.get("AGELIMIT"), reconwiseCaseList);		
					 map.put(pendingCasesMap.get("RECON")+ "_" +pendingCasesMap.get("AGELIMIT")+"_"+ pendingCasesMap.get("DEPARTMENT"), reconwiseCaseList);
			}

			}
			System.out.println("mp data "+map);

			@SuppressWarnings("rawtypes")
			Iterator reconWiseList = map.keySet().iterator();
			while (reconWiseList.hasNext()) {
				String reconName=reconWiseList.next().toString();
//				System.out.println("Recon Wise List:" + map.get(reconName));
				System.out.println();
				
				@SuppressWarnings("rawtypes")
				List<Map> recordList=(List<Map>)map.get(reconName);
				
			/*	System.out.println("reconList:"+recordList);*/
			
				@SuppressWarnings("rawtypes")
				Map dataMap = new HashMap();
				@SuppressWarnings("rawtypes")
				List unmatchList = new ArrayList();
				for(Map map1:recordList)
				{
					//dataMap.put("department",map1.get("DEPARTMENT"));
					dataMap.put("recon", map1.get("RECON"));
					dataMap.put("case_ID",map1.get("CASE_ID"));
					dataMap.put("status",map1.get("ststus"));
					dataMap.put("person",map1.get("PERSON"));
					dataMap.put("assigned_to",map.get("ASSIGN_TO"));
					dataMap.put("escalated_person",map1.get("ESCALATED_PERSON"));
					dataMap.put("mail_id",map1.get("MAIL_ID"));
					dataMap.put("sid",map1.get("SID"));
					dataMap.put("content", "PFA for :"+ map1.get("RECON") +" module Unmatch data of Ageing: " + map1.get("AGELIMIT") + " to :"
								+ map1.get("AGE"));
					/*
					dataMap.put("content", "PFA for :"+ map1.get("RECON") +" module Unmatch data of Ageing: " + map1.get("AGELIMIT") + " to :"
							+ map1.get("AGE") +  " days.");
					*/
					unmatchList.add(map1);
				}
				
				
			    dataMap.put("unmatchList",unmatchList);
			    System.out.println("UnmatchPraful:"+dataMap);	
				exportExcelAndTriggerMail1(dataMap);	   
			}

		}

		catch (Exception e) {
			e.printStackTrace();
		}
		logger.debug("allpendingreconList : " + allpendingreconList);

	}

	@SuppressWarnings("unchecked")
	public static void exportExcelAndTriggerMail1(Map<String, Object> dataMap) {

		Map<String, Object> mailData = new HashMap<String, Object>();
	
		MailTrigger mailTrigger = new MailTrigger();
		String fileName = null;

		try {
			// Export Excel of UnMatch data
			fileName = ExportExcel1.exportExcel((List<Map<String, Object>>) dataMap.get("unmatchList"),
					dataMap.get("recon").toString());
		/*	
			fileName = Test2.exportExcel2((List<Map<String, Object>>) dataMap.get("unmatchList"),
					dataMap.get("department").toString());*/
		} catch (IOException e) {
			e.printStackTrace();
		}

		// Sending Mail with Attachment
		mailData.put("content", dataMap.get("content").toString());
	 	mailData.put("mail_id",dataMap.get("mail_id").toString());  //dataMap.get("mail_id").toString()
		mailData.put("fileName", fileName);
		mailData.put("recon", dataMap.get("recon").toString());
		/*mailData.put("department", dataMap.get("department").toString());
*/
		try {
			mailTrigger.sendMailConformation(mailData);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	    public static void main(String[] args) {

		EscalationModule es = new EscalationModule();
		es.escalationModule();
	}

}
