package com.ascent.administration;

import java.io.ByteArrayOutputStream;

import java.io.ObjectOutputStream;
import java.io.Serializable;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ActionEvent;
import javax.faces.event.AjaxBehaviorEvent;
import javax.faces.event.ValueChangeEvent;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.primefaces.event.RowEditEvent;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.ds.login.ForgotPassword;
import com.ascent.ds.login.PasswordPolicy;
import com.ascent.ds.login.Sha256PasswordEncription;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;
import com.ascent.recon.util.PasswordGeneratorUtil;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.Department;
import com.ascent.service.dto.Feature;
import com.ascent.service.dto.Privilege;
import com.ascent.service.dto.PrivilegeDetails;
import com.ascent.service.dto.Role;
import com.ascent.service.dto.User;
import com.ascent.service.dto.UserController;
import com.ascent.service.dto.Users;
import com.ascent.util.PagesConstants;

public class AuthorizationBean implements Serializable, PagesConstants {

	private static Object waitObject = new Object();

	private static AuthorizationBean authorizationBean;

	/*
	 * public static AuthorizationBean getAuthorizationBeanSingleTon() {
	 * System.out.println("getAuthorizationBeanSingleTon"); if
	 * (authorizationBean == null) { try {
	 * 
	 * synchronized (waitObject) { if (authorizationBean == null) {
	 * authorizationBean = new AuthorizationBean(); } } } catch (Exception e) {
	 * e.printStackTrace(); } catch (Throwable t) { t.printStackTrace(); }
	 * finally { // waitObject.notifyAll(); }
	 * 
	 * }
	 * 
	 * return authorizationBean; }
	 */

	public static Logger logger = LogManager.getLogger(AuthorizationBean.class);
	private static final long serialVersionUID = -8330521435765966458L;
	public static final String GET_MAX_ROLE_ID = "GET_MAX_ROLE_ID";
	private UserController userController;

	// property For the Role
	private Role role;
	private User user;
	private User loggedInUser;
	private Department department;
	private Feature feature;
	private Department deleteDepartment;
	private Privilege privilege;
	private PrivilegeDetails privilegeDetails;
	List<PrivilegeDetails> pervilegeDetailList;
	private List<User> usersList;
	private List<Role> roleList;
	private List<Department> deptList;
	private boolean mesageFlag;
	private List<String> operations;
	private List<String> userIds;
	private Map<String, String> renderMap = new HashMap<String, String>();
	private Map<String, Object> deleteMap = new HashMap<String, Object>();

	CustomerDao customerDao;

	private ResourceBundle values;

	public AuthorizationBean() {

		init();
	}

	private void init() {
		mesageFlag = false;
		role = new Role();
		user = new User();
		feature = new Feature();
		department = new Department();
		privilege = new Privilege();
		privilegeDetails = new PrivilegeDetails();
		pervilegeDetailList = new ArrayList<PrivilegeDetails>();
		customerDao = new CustomerDao();
		userIds = new ArrayList<String>();

		setOperations(new ArrayList<String>());

		try {
			this.userController = customerDao.loadAllUserPrivilege();

			// setUserIds();
		} catch (Exception e1) {

			e1.printStackTrace();
			logger.error("Unable to load All User Privilege", e1);
		} //
		try {

			usersList = this.userController.getUsers().getUserList();
			roleList = this.userController.getRoles().getRoles();
			deptList = this.userController.getDepartments().getDepartments();
		} catch (Exception e) {

			e.printStackTrace();
			logger.error("Unable to load role List or users List or dept List ", e);

		}

	}

	List<PrivilegeDetails> pervilegeTempDetailList = new ArrayList<PrivilegeDetails>();

	public void updateVisibility(AjaxBehaviorEvent event) {
		String id = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("myId");

		// System.out.println(id);
	}

	/*
	 * public void submitRoles() {
	 * 
	 * Map<String, Object> parmMap;
	 * 
	 * List<Role> list = this.userController.getRoles().getRoles();
	 * 
	 * CustomerDao customerDao = new CustomerDao(); Connection connection =
	 * null; if (!list.isEmpty()) { try { connection = DbUtil.getConnection();
	 * connection.setAutoCommit(false);
	 * 
	 * // TODO: Need To Pass Under Work Flow
	 * 
	 * for (Role role : list) { Map<String, Object> map = new HashMap<String,
	 * Object>(); map.put("role", role.getRole());
	 * customerDao.delete(connection, map, "TruncateRole"); }
	 * 
	 * connection.commit(); } catch (ClassNotFoundException | SQLException e) {
	 * // TODO Auto-generated catch block e.printStackTrace();
	 * 
	 * logger.error("Unable to Truncae the data", e); try {
	 * connection.rollback(); } catch (SQLException e1) {
	 * 
	 * e1.printStackTrace(); logger.error("Unable to Truncae the data", e); } }
	 * finally { try { if (connection != null && !connection.isClosed())
	 * connection.close(); } catch (SQLException e) {
	 * 
	 * e.printStackTrace(); logger.error("Unable to Close Connection", e); } } }
	 * 
	 * for (Role role : roleList) {
	 * 
	 * parmMap = new HashMap<String, Object>(); String roleName = ""; if
	 * (role.getRole() == null) { roleName = ""; } else { roleName =
	 * role.getRole(); } parmMap.put("role", roleName); parmMap.put("roleid",
	 * role.getRoleId()); parmMap.put("discription", role.getDescription());
	 * customerDao.saveRoles(parmMap); }
	 * 
	 * try { this.userController = customerDao.loadAllUserPrivilege(); } catch
	 * (Exception e) { e.printStackTrace(); logger.error(
	 * "Unable to Close Connection", e); } mesageFlag = true; roleList =
	 * this.userController.getRoles().getRoles(); }
	 */

	/*
	 * public void openUser() {
	 * 
	 * System.out.println("openUser"); this.user = new User();
	 * 
	 * String cpmId = BeanUtil.getClientId(FacesContext.getCurrentInstance(),
	 * "adminDialogUser"); RequestContext.getCurrentInstance().update(cpmId); //
	 * onclick="PF('adminDialogUser').show();"
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('adminDialogUser').show()");
	 * 
	 * }
	 */

	/*
	 * public void updateRole(ActionEvent event) {
	 * this.renderMap.put("RoleSubmit", "Update"); List<Role> rolesTemp = null;
	 * try { Roles roles = new CustomerDao().loadAllRoles();
	 * 
	 * rolesTemp = roles.getRoles(); } catch (Exception e) { // TODO
	 * Auto-generated catch block e.printStackTrace(); } this.roleList.clear();
	 * this.roleList.addAll(rolesTemp); String string2 = BeanUtil.getClientId(
	 * FacesContext.getCurrentInstance(), "rolesTable");
	 * RequestContext.getCurrentInstance().update(string2); boolean flag =
	 * false;
	 * 
	 * this.role = (Role) event.getComponent().getAttributes().get("editRole");
	 * String comString = BeanUtil.getClientId(
	 * FacesContext.getCurrentInstance(), "adminDialogRole"); Integer roleId =
	 * role.getRoleId(); String roleString = role.getRole(); Map<String, Object>
	 * map = new HashMap<String, Object>(); map.put("system_role", roleString);
	 * CustomerDao customerDao = new CustomerDao(); try { List<Map<String,
	 * Object>> dataBaseList = customerDao
	 * .getDataBaseList("LoadAllUsersByRole", map); List<String> roleListEdit =
	 * new ArrayList<String>(); for (Map<String, Object> rolName : dataBaseList)
	 * { roleListEdit.add((String) rolName.get("system_role")); }
	 * 
	 * // System.out.println(dataBaseList); if
	 * (roleListEdit.contains(roleString)) {
	 * 
	 * flag = true; EntityType entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
	 * "This Role is already Linked with Othe User You can not Edit"); String
	 * string = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "ErrorDialog"); RequestContext.getCurrentInstance().update(string);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * }
	 * 
	 * } catch (SQLException e) {
	 * 
	 * e.printStackTrace(); }
	 * 
	 * if (!flag) { RequestContext.getCurrentInstance().update(comString); //
	 * System.out.println(event.getComponent().getAttributes() //
	 * .get("editRole")); RequestContext.getCurrentInstance().execute(
	 * "PF('adminDialogRole').show();"); }
	 * 
	 * }
	 */

	public void persistRole(Map<String, Object> dataMap, String actStatus, boolean isNew, boolean isWorkflow,
			Connection connection) throws Exception {
		// persistRole(dataMap, APPROVED, true, false, connection);
		CustomerDao customerDao = new CustomerDao();
		boolean newActivityStatusUpdated = false;
		Map<String, Object> rolesActivityMap = (Map<String, Object>) dataMap.get("activity_data");
		// rolesActivityMap.put("status", dataMap.get("status"));

		// updateVersion(rolesActivityMap);

		if (isNew) {
			String activity_type = (String) dataMap.get("activity_type");
			if (activity_type != null && activity_type.equals("EDIT")) {
				customerDao.update(connection, dataMap, "updateRoles");
			} else {

				persistRoleEntity(dataMap, isWorkflow, customerDao, connection);
			}
			newActivityStatusUpdated = true;

		}
		if (APPROVED.equalsIgnoreCase(actStatus) && !newActivityStatusUpdated) {
			if (dataMap.get("activity_name") != null) {
				if (dataMap.get("activity_name").toString().equalsIgnoreCase("USER_ADMIN")) {
					if (dataMap.get("activity_type").toString().equalsIgnoreCase("NEW")) {
						// rolesActivityMap.put("version",dataMap.get("version"));
						// dataMap.put("activity_data", act_map);
						rolesActivityMap.put("status", APPROVED);
						customerDao.update(connection, rolesActivityMap, "updateRoles");
					} else if (dataMap.get("activity_type").toString().equalsIgnoreCase("EDIT")) {
						rolesActivityMap.put("status", APPROVED);
						Long version = (Long) rolesActivityMap.get("version");
						int intVersion = (int) (version - 1);
						// Long versionDec=version-1;
						rolesActivityMap.put("version", intVersion);
						customerDao.update(connection, rolesActivityMap, "updateRoles");
					} else if (dataMap.get("activity_type").toString().equalsIgnoreCase(DELETE)) {
						Long version = (Long) rolesActivityMap.get("version");
						// int intVersion=version;
						Long versionDec = version - 1;
						rolesActivityMap.put("version", versionDec);
						roleStatusUpdate(connection, rolesActivityMap, customerDao, DELETE, "N");
					}
				}
			} else {
				dataMap.put("status", DELETE);
				Long version = (Long) dataMap.get("version");
				rolesActivityMap.put("version", --version);
				roleStatusUpdate(connection, rolesActivityMap, customerDao, DELETE, "N");
			}
		}
		if (REJECTED.equalsIgnoreCase(actStatus)) {
			rolesActivityMap.put("status", REJECTED);
			/*
			 * String activity_type=(String)dataMap.get("activity_type");
			 * if((activity_type!=null) &&
			 * (activity_type.equalsIgnoreCase("EDIT"))) { Integer
			 * version=(Integer)dataMap.get("version");
			 * rolesActivityMap.put("version", --version);
			 * roleStatusUpdate(connection, dataMap, customerDao, APPROVED,
			 * "Y");
			 * 
			 * }else if((activity_type!=null) &&
			 * (activity_type.equalsIgnoreCase("NEW"))) {
			 * roleStatusUpdate(connection, rolesActivityMap, customerDao,
			 * APPROVED, "Y");
			 * 
			 * } else{
			 */
			roleStatusUpdate(connection, rolesActivityMap, customerDao, REJECTED, "N");
			// }

		}

	}

	public void openUserAssureDlg(ActionEvent event) {
		this.user = (User) event.getComponent().getAttributes().get("deletUser");
		this.deleteMap.put("deleteUser", this.user);
	}

	public void openDepAssureDlg(ActionEvent event) {
		this.department = (Department) event.getComponent().getAttributes().get("deletDep");
		this.deleteMap.put("deleteDepartment", this.department);
	}

	public void openAssureDlg(ActionEvent event) {
		this.role = (Role) event.getComponent().getAttributes().get("deletRole");
		this.deleteMap.put("deleteRole", this.role);

	}

	public void openPrivAssureDlg(ActionEvent event) {
		this.privilegeDetails = (PrivilegeDetails) event.getComponent().getAttributes().get("delet");
		this.deleteMap.put("deletePrivelege", this.privilegeDetails);

	}

	/*
	 * public void deletePrivileg(ActionEvent event) {
	 * 
	 * System.out.println(" in delete role pervilegeDetailList");
	 * this.privilegeDetails = (PrivilegeDetails)
	 * this.deleteMap.get("deletePrivelege");
	 * 
	 * boolean flag = false;
	 * 
	 * for (PrivilegeDetails privilegeDet : pervilegeDetailList) {
	 * 
	 * } for (Iterator iterator = pervilegeDetailList.iterator(); iterator
	 * .hasNext();) { PrivilegeDetails privilegeDet = (PrivilegeDetails)
	 * iterator.next(); if (privilegeDet.equals(privilegeDetails)) {
	 * iterator.remove(); }
	 * 
	 * }
	 * 
	 * String compId = BeanUtil.getClientId(FacesContext.getCurrentInstance(),
	 * "previelegeTable"); RequestContext.getCurrentInstance().update(compId);
	 * 
	 * 
	 * Integer roleId = role.getRoleId(); String roleString = role.getRole();
	 * Map<String, Object> map = new HashMap<String, Object>();
	 * map.put("system_role", roleString); CustomerDao customerDao = new
	 * CustomerDao(); try { List<Map<String, Object>> dataBaseList = customerDao
	 * .getDataBaseList("LoadAllUsersByRole", map); List<String> roleListEdit =
	 * new ArrayList<String>(); for (Map<String, Object> rolName : dataBaseList)
	 * { roleListEdit.add((String) rolName.get("system_role")); }
	 * 
	 * // System.out.println(dataBaseList); if
	 * (roleListEdit.contains(roleString)) {
	 * 
	 * flag = true; EntityType entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
	 * "This Role is already Linked with Othe User You can not Edit"); String
	 * string = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "ErrorDialog"); RequestContext.getCurrentInstance().update(string);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * }
	 * 
	 * } catch (SQLException e) {
	 * 
	 * e.printStackTrace(); }
	 * 
	 * if (!flag) {
	 * 
	 * Map<String, Object> dataMap = new HashMap<String, Object>();
	 * dataMap.put("activity_name", "MODULE_USER_ADMIN");
	 * dataMap.put("activity_type", OPERATION_DELETE); Integer userId =
	 * role.getRoleId(); dataMap.put("roleid", userId); dataMap.put("status",
	 * DELETE); HashMap<String, Object> hashMap = new HashMap<String, Object>();
	 * hashMap.put("roleid", userId); hashMap.put("activity_name",
	 * "MODULE_USER_ADMIN"); hashMap.put("activity_type", OPERATION_DELETE);
	 * hashMap.put("status", DELETE); dataMap.put("activity_data", hashMap);
	 * 
	 * processRole(DELETE, dataMap);
	 * 
	 * UserAdminManager userAdminManager = UserAdminManager
	 * .getAuthorizationManagerSingleTon(); User user = BeanUtil
	 * .getLoginUser(FacesContext.getCurrentInstance());
	 * 
	 * if (userAdminManager.isUserUnderWorkflow(user)) { EntityType entityRole =
	 * (EntityType) FacesContext .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole .setErrorMessage (
	 * "Role is deleted Succes fully for the Approval"); String string3 =
	 * BeanUtil.getClientId( FacesContext.getCurrentInstance(), "ErrorDialog");
	 * RequestContext.getCurrentInstance().update(string3);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * } else { String string = "Role is deleted Succes fully And Approved";
	 * reloadRole(string);
	 * 
	 * }
	 * 
	 * }
	 * 
	 * }
	 *//*
		 * public void deleteRole(ActionEvent event) {
		 * 
		 * System.out.println(" in delete role"); this.role = (Role)
		 * this.deleteMap.get("deleteRole"); boolean flag = false; Integer
		 * roleId = role.getRoleId(); String roleString = role.getRole();
		 * Map<String, Object> map = new HashMap<String, Object>();
		 * map.put("system_role", roleString); CustomerDao customerDao = new
		 * CustomerDao(); try { List<Map<String, Object>> dataBaseList =
		 * customerDao .getDataBaseList("LoadAllUsersByRole", map); List<String>
		 * roleListEdit = new ArrayList<String>(); for (Map<String, Object>
		 * rolName : dataBaseList) { roleListEdit.add((String)
		 * rolName.get("system_role")); }
		 * 
		 * // System.out.println(dataBaseList); if
		 * (roleListEdit.contains(roleString)) {
		 * 
		 * flag = true; EntityType entityRole = (EntityType) FacesContext
		 * .getCurrentInstance().getExternalContext()
		 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
		 * "This Role is already Linked with Othe User You can not Edit");
		 * String string = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "ErrorDialog");
		 * RequestContext.getCurrentInstance().update(string);
		 * 
		 * RequestContext.getCurrentInstance().execute(
		 * "PF('ErrorDialog').show();");
		 * 
		 * }
		 * 
		 * } catch (SQLException e) {
		 * 
		 * e.printStackTrace(); }
		 * 
		 * if (!flag) {
		 * 
		 * Map<String, Object> dataMap = new HashMap<String, Object>();
		 * dataMap.put("activity_name", "MODULE_USER_ADMIN");
		 * dataMap.put("activity_type", OPERATION_DELETE); Integer userId =
		 * role.getRoleId(); dataMap.put("roleid", userId);
		 * dataMap.put("status", DELETE); HashMap<String, Object> hashMap = new
		 * HashMap<String, Object>(); hashMap.put("roleid", userId);
		 * hashMap.put("activity_name", "MODULE_USER_ADMIN");
		 * hashMap.put("activity_type", OPERATION_DELETE); hashMap.put("status",
		 * DELETE); dataMap.put("activity_data", hashMap);
		 * 
		 * processRole(DELETE, dataMap);
		 * 
		 * UserAdminManager userAdminManager = UserAdminManager
		 * .getAuthorizationManagerSingleTon(); User user = BeanUtil
		 * .getLoginUser(FacesContext.getCurrentInstance());
		 * 
		 * if (userAdminManager.isUserUnderWorkflow(user)) { EntityType
		 * entityRole = (EntityType) FacesContext
		 * .getCurrentInstance().getExternalContext()
		 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
		 * "Role is deleted Succes fully for the Approval"); String string3 =
		 * BeanUtil.getClientId( FacesContext.getCurrentInstance(),
		 * "ErrorDialog"); RequestContext.getCurrentInstance().update(string3);
		 * 
		 * RequestContext.getCurrentInstance().execute(
		 * "PF('ErrorDialog').show();");
		 * 
		 * } else { String string = "Role is deleted Succes fully And Approved";
		 * reloadRole(string);
		 * 
		 * } }
		 * 
		 * }
		 */
	/*
	 * public void deleteDepartMent(ActionEvent event) {
	 * 
	 * this.department = (Department) this.deleteMap.get("deleteDepartment");
	 * boolean flag = false; // Integer roleId = department.getRoleId(); String
	 * roleString = department.getDeptName(); Map<String, Object> map = new
	 * HashMap<String, Object>(); map.put("dept_name", roleString); CustomerDao
	 * customerDao = new CustomerDao(); try { List<Map<String, Object>>
	 * dataBaseList = customerDao .getDataBaseList("LoadAllUsersByDeptName",
	 * map); List<String> roleListEdit = new ArrayList<String>(); for
	 * (Map<String, Object> rolName : dataBaseList) { roleListEdit.add((String)
	 * rolName.get("dept_name")); }
	 * 
	 * // System.out.println(dataBaseList); if
	 * (roleListEdit.contains(roleString)) {
	 * 
	 * flag = true; EntityType entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
	 * "This DepartMent is already Linked with Othe User You can not Edit");
	 * String string = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "ErrorDialog"); RequestContext.getCurrentInstance().update(string);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * }
	 * 
	 * } catch (SQLException e) {
	 * 
	 * e.printStackTrace(); }
	 * 
	 * if (!flag) {
	 * 
	 * Map<String, Object> dataMap = new HashMap<String, Object>();
	 * dataMap.put("activity_name", "MODULE_USER_ADMIN");
	 * dataMap.put("activity_type", OPERATION_DELETE); String userId =
	 * department.getDeptId(); dataMap.put("dept_id", userId);
	 * dataMap.put("status", DELETE); HashMap<String, Object> hashMap = new
	 * HashMap<String, Object>(); hashMap.put("dept_id", userId);
	 * hashMap.put("activity_name", "MODULE_USER_ADMIN");
	 * hashMap.put("activity_type", OPERATION_DELETE); hashMap.put("status",
	 * DELETE); dataMap.put("activity_data", hashMap);
	 * 
	 * processDepartMent(DELETE, dataMap);
	 * 
	 * UserAdminManager userAdminManager = UserAdminManager
	 * .getAuthorizationManagerSingleTon(); User user = BeanUtil
	 * .getLoginUser(FacesContext.getCurrentInstance());
	 * 
	 * if (userAdminManager.isUserUnderWorkflow(user)) {
	 * 
	 * EntityType entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole .setErrorMessage(
	 * "Role is submited Succes fully for the Approval"); String string3 =
	 * BeanUtil.getClientId( FacesContext.getCurrentInstance(), "ErrorDialog");
	 * RequestContext.getCurrentInstance().update(string3);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * } else {
	 * 
	 * this.deptList.clear(); Departments departments; try { departments =
	 * customerDao.loadAllDepartMents(); this.deptList =
	 * departments.getDepartments(); } catch (Exception e) { // TODO
	 * Auto-generated catch block e.printStackTrace(); }
	 * 
	 * String string2 = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "departments"); RequestContext.getCurrentInstance().update(string2);
	 * EntityType entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext()
	 * .getSessionMap().get("entityType"); entityRole.setErrorMessage(
	 * "Department is deleted Succesfully"); String string3 =
	 * BeanUtil.getClientId( FacesContext.getCurrentInstance(), "ErrorDialog");
	 * RequestContext.getCurrentInstance().update(string3);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();"); }
	 * 
	 * } }
	 */

	/*
	 * public void deleteUser(ActionEvent event) {
	 * 
	 * this.user = (User) this.deleteMap.get("deleteUser"); Map<String, Object>
	 * dataMap = new HashMap<String, Object>(); dataMap.put("activity_name",
	 * "MODULE_USER_ADMIN"); dataMap.put("activity_type", OPERATION_DELETE);
	 * String userId = user.getUserId(); dataMap.put("user_id", userId);
	 * dataMap.put("status", DELETE); HashMap<String, Object> hashMap = new
	 * HashMap<String, Object>(); hashMap.put("user_id", userId);
	 * hashMap.put("activity_name", "MODULE_USER_ADMIN");
	 * hashMap.put("activity_type", OPERATION_DELETE); hashMap.put("status",
	 * DELETE); dataMap.put("activity_data", hashMap); try {
	 * processUsers(DELETE, dataMap); } catch (Exception e) { EntityType
	 * entityRole = (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext().getSessionMap()
	 * .get("entityType"); entityRole.setErrorMessage("Un Able to Delete User");
	 * String string3 = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "ErrorDialog"); RequestContext.getCurrentInstance().update(string3); }
	 * UserAdminManager userAdminManager = UserAdminManager
	 * .getAuthorizationManagerSingleTon(); User user =
	 * BeanUtil.getLoginUser(FacesContext.getCurrentInstance());
	 * 
	 * if (userAdminManager.isUserUnderWorkflow(user)) { EntityType entityRole =
	 * (EntityType) FacesContext
	 * .getCurrentInstance().getExternalContext().getSessionMap()
	 * .get("entityType"); entityRole .setErrorMessage(
	 * "User is submited Succes fully for the Approval"); String string3 =
	 * BeanUtil.getClientId( FacesContext.getCurrentInstance(), "ErrorDialog");
	 * RequestContext.getCurrentInstance().update(string3);
	 * 
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('ErrorDialog').show();");
	 * 
	 * } else { String deString = "User is Deleted Succesfully";
	 * reloadUsers(deString); }
	 * 
	 * }
	 */

	public void persistUsers(Map<String, Object> dataMap, String actStatus, boolean isNew, boolean isWorkflow,
			Connection connection) throws Exception {

		CustomerDao customerDao = new CustomerDao();
		boolean newActivityStatusUpdated = false;
		Map<String, Object> usersActivityMap = (Map<String, Object>) dataMap.get("activity_data");

		// usersActivityMap.put("version",dataMap.get("version"));

		if (isNew) {
			String activity_type = (String) dataMap.get("activity_type");
			if (activity_type != null && activity_type.equals("NEW")) {
				customerDao.update(connection, dataMap, "updateUsers");

			} else {
				persistUserEntity(dataMap, isWorkflow, customerDao, connection);
			}
			newActivityStatusUpdated = true;

		}
		if (APPROVED.equalsIgnoreCase(actStatus) && !newActivityStatusUpdated) {
			if (dataMap.get("activity_name") != null) {
				if (dataMap.get("activity_name").toString().equalsIgnoreCase("USER_ADMIN")) {
					if (dataMap.get("activity_type").toString().equalsIgnoreCase("NEW")) {
						usersActivityMap.put("status", APPROVED);
						customerDao.update(connection, usersActivityMap, "updateUsers");
					} else if (dataMap.get("activity_type").toString().equalsIgnoreCase(
							"EDIT")) {/*
										 * usersActivityMap.put("status",
										 * APPROVED);
										 * customerDao.update(connection,
										 * usersActivityMap, "updateUsers");
										 */
						usersActivityMap.put("status", APPROVED);
						// Integer version = (Integer)
						// usersActivityMap.get("version");
						// int intVersion = (int) (version - 1);
						// Long versionDec=version-1;
						Long version = (Long) usersActivityMap.get("version");
						int versionInt = (int) (version - 1);
						usersActivityMap.put("version", versionInt);
						customerDao.update(connection, usersActivityMap, "updateUsers");

					} else {
						Long version = (Long) usersActivityMap.get("version");
						usersActivityMap.put("version", --version);
						dataMap.put("status", DELETE);
						usersActivityMap.put("status", DELETE);
						userStatusUpdate(connection, usersActivityMap, customerDao, DELETE, "N");

					}
				}
			} else {
				dataMap.put("status", DELETE);
				Long version = (Long) dataMap.get("version");
				// --version;
				usersActivityMap.put("version", --version);
				usersActivityMap.put("status", DELETE);
				userStatusUpdate(connection, usersActivityMap, customerDao, DELETE, "N");
			}
		}
		if (REJECTED.equalsIgnoreCase(actStatus)) {
			usersActivityMap.put("status", REJECTED);
			userStatusUpdate(connection, usersActivityMap, customerDao, REJECTED, "N");

			/*
			 * String activity_type=(String)dataMap.get("activity_type");
			 * if((activity_type!=null) &&
			 * (activity_type.equalsIgnoreCase("EDIT"))) { Integer
			 * version=(Integer)dataMap.get("version");
			 * usersActivityMap.put("version", --version);
			 * roleStatusUpdate(connection, usersActivityMap, customerDao,
			 * APPROVED, "Y"); } else{ roleStatusUpdate(connection,
			 * usersActivityMap, customerDao, REJECTED, "N"); }
			 */

		}

	}

	private void roleStatusUpdate(Connection connection, Map<String, Object> dataMap, CustomerDao customerDao2,
			String approved, String string) {
		dataMap.put("status", approved);
		dataMap.put("active_index", string);
		try {
			customerDao.update(connection, dataMap, "RoleStatusUpdate");
		} catch (Exception e) {

			e.printStackTrace();
		}

	}

	private void userStatusUpdate(Connection connection, Map<String, Object> dataMap, CustomerDao customerDao2,
			String approved, String string) {
		dataMap.put("status", approved);
		dataMap.put("active_index", string);
		try {
			customerDao.update(connection, dataMap, "UserStatusUpdate");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	private void persistRoleEntity(Map<String, Object> dataMap, boolean isWorkflow, CustomerDao customerDao2,
			Connection connection) {
		if (isWorkflow) {
			dataMap.put("status", PENDING_APPROVAL);

		} else {
			dataMap.put("status", APPROVED);
		}
		customerDao.saveRoles(dataMap);

	}

	private void persistUserEntity(Map<String, Object> dataMap, boolean isWorkflow, CustomerDao customerDao2,
			Connection connection) throws Exception {
		if (isWorkflow) {
			dataMap.put("status", PENDING_APPROVAL);

		} else {
			dataMap.put("status", APPROVED);
		}

		customerDao.insertData(connection, dataMap, "saveUsers");
	}

	public void saveRoles(Map<String, Object> resultMap, User user) {

		boolean flag = false;
		Map<String, Object> parmMap = new HashMap<String, Object>();
		String userSelectedBusinessArea = (String) resultMap.get("userSelectedBusinessArea");
		String userSelectedRecon = (String) resultMap.get("userSelectedRecon");
		/* String data */String action = (String) resultMap.get("action");
		List<Map<String, Object>> resultDataList = (List) resultMap.get(SELECTED_RECORDS);
		for (Map<String, Object> requestedMap : resultDataList) {
			parmMap.put("roleid", requestedMap.get("roleid"));
			parmMap.put("role", requestedMap.get("role"));
			parmMap.put("discription", requestedMap.get("discription"));
			parmMap.put("status", requestedMap.get("status"));
			parmMap.put("active_index", requestedMap.get("active_index"));
			parmMap.put("version", requestedMap.get("version"));
			parmMap.put("created_on", requestedMap.get("created_on"));
			parmMap.put("updated_on", requestedMap.get("updated_on"));
		}
		// parmMap.put(TABLE_NAME,resultMap.get(TABLE_NAME));
		parmMap.put(DS_NAME, resultMap.get(DS_NAME));

		String roleRole = this.role.getRole();
		if (roleRole == null || roleRole.equals("")) {

			/*
			 * RequestContext.getCurrentInstance().execute(
			 * "PF('ErrorDialog').show();"); flag = true; return;
			 */

		}

		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
		} catch (Exception e) {

		}
		// action = SUBMIT;
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();

		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.putAll(parmMap);
		dataMap.put("activity_data", parmMap);
		dataMap.put("version", parmMap.get("version"));

		processRole(user, action, dataMap, userSelectedBusinessArea, userSelectedRecon);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			// write alert that successfully submited for approval
		} else {
			// String string = "Role is submited Succes fully And Approved";
			// reloadRole(string);

		}

		if (!flag) {
			/*
			 * RequestContext.getCurrentInstance().execute(
			 * "PF('adminDialogRole').hide()");
			 */
		}

		/*
		 * String componentId = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "rolesTable");
		 * 
		 * RequestContext.getCurrentInstance().update(componentId);
		 * 
		 * onclick="PF('adminDialogRole').hide()"
		 */

	}

	private boolean processRole(User user, String action, Map<String, Object> dataMap, String userSelectedBArea,
			String userSelectedRecon) {
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
		} catch (Exception e) {

		}
		System.out.println(dataMap);
		setUpRoleDataToPersist(action, dataMap);
		System.out.println(dataMap);
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();

		if (userAdminManager.isUserUnderWorkflow(user)) {
			String activityStatus = "";
			if (SAVE.equalsIgnoreCase(action) || action.equalsIgnoreCase(UPDATE) || action.equalsIgnoreCase(DELETE)) {
				activityStatus = PENDING_APPROVAL;
			} else {
				activityStatus = DRAFT;
			}

			try {
				String comments = "";
				if (action.equalsIgnoreCase(SAVE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("discription");
					userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_NEW, dataMap, activityStatus, comments);
					persistRole(mapTemp, PENDING_APPROVAL, true, true, connection);
				} else if (action.equalsIgnoreCase(DELETE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("discription");
					userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_DELETE, dataMap, activityStatus, comments);
					mapTemp.put("status", PENDING_APPROVAL);
					persistRole(mapTemp, PENDING_APPROVAL, false, true, connection);
				} else {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("discription");
					userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_EDIT, dataMap, activityStatus, comments);
					/*
					 * Map modifyMap=(Map) mapTemp.get("activity_data"); Long
					 * version=(Long)modifyMap.get("version"); int
					 * intVersion=(int) (version-1); // Long
					 * versionDec=version-1; modifyMap.put("version",
					 * intVersion); System.out.println(modifyMap);
					 */
					persistRole(mapTemp, PENDING_APPROVAL, true, true, connection);
				}
			} catch (Exception e) {
				logger.error("Unable to process through workflow");
				e.printStackTrace();
			}

		} else {
			try {
				if (action.equalsIgnoreCase(DELETE)) {
					persistRole(dataMap, APPROVED, false, false, connection);
				} else if (action.equalsIgnoreCase(UPDATE)) {
					Map rolesMap = (Map) dataMap.get("activity_data");
					rolesMap.put("status", APPROVED);
					Long version = (Long) rolesMap.get("version");
					int versionInt = (int) (version - 1);
					// Integer versionUpdate=new Integer((int) versionInt);
					rolesMap.put("version", versionInt);
					customerDao.update(connection, rolesMap, "updateRoles");
				} else {
					persistRole(dataMap, APPROVED, true, false, connection);
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
		try {
			if (connection != null)
				connection.commit();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}

	private boolean processUsers(User user, String action, Map<String, Object> dataMap, String userSelectedBusinessArea,
			String userSelectedRecon) throws Exception {
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
		} catch (Exception e) {

		}
		setUpUserDataToPersist(action, dataMap);
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();

		if (userAdminManager.isUserUnderWorkflow(user)) {
			String activityStatus = "";
			if (SAVE.equalsIgnoreCase(action) || action.equalsIgnoreCase(UPDATE) || action.equalsIgnoreCase(DELETE)) {
				activityStatus = PENDING_APPROVAL;
			} else {
				activityStatus = DRAFT;
			}

			try {
				String comments = "";
				if (action.equalsIgnoreCase(SAVE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("branch_location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_NEW, dataMap, activityStatus, comments);
					persistUsers(mapTemp, PENDING_APPROVAL, true, true, connection);
				} else if (action.equalsIgnoreCase(DELETE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("branch_location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_DELETE, dataMap, activityStatus, comments);
					mapTemp.put("status", PENDING_APPROVAL);
					persistUsers(mapTemp, PENDING_APPROVAL, false, true, connection);
				} else {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("branch_location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_EDIT, dataMap, activityStatus, comments);
					/*
					 * Map modifyMap=(Map) mapTemp.get("activity_data"); Long
					 * version=(Long)modifyMap.get("version"); int
					 * intVersion=(int) (version-1); // Long
					 * versionDec=version-1; modifyMap.put("version",
					 * intVersion); mapTemp.put("version", intVersion);
					 */
					persistUsers(mapTemp, PENDING_APPROVAL, true, true, connection);

					/*
					 * persistRole(dataMap, PENDING_APPROVAL, true, true,
					 * connection);
					 */
				}

			} catch (Exception e) {
				logger.error("Unable to process through workflow");
				e.printStackTrace();
			}

		}

		else {

			try {
				if (action.equalsIgnoreCase(DELETE)) {
					persistUsers(dataMap, APPROVED, false, false, connection);
				} else if (action.equalsIgnoreCase(UPDATE)) {
					Map usersMap = (Map) dataMap.get("activity_data");
					usersMap.put("status", APPROVED);
					// Integer version = (Integer) usersMap.get("version");
					// int versionInt = (int) (version - 1);
					// Integer versionUpdate=new Integer((int) versionInt);
					Long version = (Long) usersMap.get("version");
					int versionInt = (int) (version - 1);
					usersMap.put("version", versionInt);
					
					String account_status=usersMap.get("account_status").toString();
					
					int serverPort=(int) usersMap.get("serverPort");
					String contextPath= (String) usersMap.get("contextPath");
					String user_id=(String) usersMap.get("user_id");
					String email=(String) usersMap.get("email_id");
					boolean updateStatus = customerDao.update(connection, usersMap, "updateUsers");
					if(updateStatus==true){
						if(account_status.equals("UNLOCKED")){
							ForgotPassword forgotPassword= new ForgotPassword();
							
						
							forgotPassword.processMail(serverPort,contextPath, user_id,  email,account_status);
							System.out.println("processing mail ......");
						}
					}
				} else {
					persistUsers(dataMap, APPROVED, true, false, connection);
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			/*
			 * try { System.out.println("Not Work Flow");
			 * if(action.equalsIgnoreCase(DELETE)){ persistUsers(dataMap,
			 * APPROVED, false, false, connection); } else{
			 * persistUsers(dataMap, APPROVED, true, false, connection); } }
			 * catch (Exception e) { // TODO Auto-generated catch block
			 * e.printStackTrace(); }
			 */

		}
		try {
			if (connection != null)
				connection.commit();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}

	private void setUpDepartmentDataToPersist(String action, Map<String, Object> dataMap) {
		Map<String, Object> map = (Map<String, Object>) dataMap.get("activity_data");

		updateVersion(map);
		updateVersion(dataMap);

		dataMap.put(PERSIST_CLASS, DEPARTMENT_CLASS_NAME);
		map.put(PERSIST_CLASS, DEPARTMENT_CLASS_NAME);
	}

	private void setUpRoleDataToPersist(String action, Map<String, Object> dataMap) {
		Map<String, Object> map = (Map<String, Object>) dataMap.get("activity_data");
		updateVersion(map);
		updateVersion(dataMap);

		map.put(PERSIST_CLASS, ROLES_CLASS_NAME);
		dataMap.put(PERSIST_CLASS, ROLES_CLASS_NAME);

	}

	private void setUpUserDataToPersist(String action, Map<String, Object> dataMap) {
		Map<String, Object> map = (Map<String, Object>) dataMap.get("activity_data");

		updateVersion(map);
		updateVersion(dataMap);
		dataMap.put(PERSIST_CLASS, USERS_CLASS_NAME);
		map.put(PERSIST_CLASS, USERS_CLASS_NAME);
	}

	private void setUpPrivilegeDataToPersist(String action, Map<String, Object> dataMap1) {
		Map<String, Object> map = (Map<String, Object>) dataMap1.get("activity_data");
		updatePrivVersion(map);
		updatePrivVersion(dataMap1);
		dataMap1.put(PERSIST_CLASS, PRIVILEGE_CLASS_NAME);
		map.put(PERSIST_CLASS, PRIVILEGE_CLASS_NAME);
	}

	public void updateVersion(Map<String, Object> data) {
		Long version = (Long) data.get("version");
		if (version == null) {
			data.put("version", 1);
		} else {
			version = version + 1;
			data.put("version", version);
		}
	}

	public void updatePrivVersion(Map<String, Object> data) {
		Integer version = (Integer) data.get("version");
		if (version == null) {
			data.put("version", 1);
		} else {
			version = version + 1;
			data.put("version", version);
		}
	}

	public void addOperationsValueChangeListner(ValueChangeEvent event) {

		if (event.getOldValue().equals("OnBoard")) {
			operations.clear();
			operations.add("New");
			operations.add("Edit");
			operations.add("View");
		} else {
			operations.clear();
			operations.add("Activity Maker");
			operations.add("Activity View");

		}
		// operationId

	}

	public void addOperationsEditByModule(AjaxBehaviorEvent event) {
		// /authorizationBean.privilegeDetails.operation
		// System.out.println(event.getComponent().getAttributes().get("value"));
		operations = new ArrayList<String>();
		// System.out.println(this.privilegeDetails.getModule());
		if (event.getComponent().getAttributes().get("value").equals("OnBoard")) {
			operations.clear();
			operations.add("New");
			operations.add("Edit");
			operations.add("View");
		} else {
			operations.clear();
			operations.add("Activity Maker");
			operations.add("Activity View");

		}
		// operationId

	}

	public void addOperationsByModule() {
		// /authorizationBean.privilegeDetails.operation
		operations = new ArrayList<String>();
		// System.out.println(this.privilegeDetails.getModule());
		if (this.privilegeDetails.getModule().equals("OnBoard")) {
			operations.clear();
			operations.add("New");
			operations.add("Edit");
			operations.add("View");
		} else {
			operations.clear();
			operations.add("Activity Maker");
			operations.add("Activity View");

		}
		// operationId

	}

	public void submitDepartments() {

		List<Department> list = this.userController.getDepartments().getDepartments();

		CustomerDao customerDao = new CustomerDao();
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);

			// TODO: Need To Pass Under Work Flow
			for (Department dept : list) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("dept_id", dept.getDeptId());
				customerDao.delete(connection, map, "TruncateDepartment");
			}

			connection.commit();
		} catch (ClassNotFoundException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("Unable to Truncate Department", e);
			try {
				connection.rollback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
				logger.error("Unable to RollBack Operation", e);
			}
		} finally {
			try {
				if (connection != null && !connection.isClosed())
					connection.close();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				logger.error("Unable to Close Connection", e);
			}
		}
		Map<String, Object> parmMap;
		for (Department department : deptList) {
			parmMap = new HashMap<String, Object>();
			parmMap.put("dept_name", department.getDeptName());
			parmMap.put("category", department.getDeptCategory());
			parmMap.put("dept_id", department.getDeptId());

			parmMap.put("location", department.getDeptLocation());

			customerDao.saveDepartments(parmMap);
		}

		try {
			this.userController = customerDao.loadAllUserPrivilege();// lUserPrivilege();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("Unable to load All User Privilege", e);
		}
		this.deptList = this.getUserController().getDepartments().getDepartments();
	}

	public void saveDepartments(Map<String, Object> resultMap, User user) {

		boolean flag = false;
		Map<String, Object> deptparmMap = new HashMap<String, Object>();
		String userSelectedBusinessArea = (String) resultMap.get("userSelectedBusinessArea");
		String userSelectedRecon = (String) resultMap.get("userSelectedRecon");
		String action = (String) resultMap.get("action");
		List<Map<String, Object>> resultDataList = (List) resultMap.get(SELECTED_RECORDS);
		for (Map<String, Object> requestedMap : resultDataList) {
			String deptName = (String) requestedMap.get("dept_name");
			deptparmMap.put("dept_id", requestedMap.get("dept_id"));
			deptparmMap.put("dept_name", requestedMap.get("dept_name"));
			deptparmMap.put("category", requestedMap.get("category"));
			deptparmMap.put("location", requestedMap.get("location"));
			deptparmMap.put("status", requestedMap.get("status"));
			deptparmMap.put("active_index", requestedMap.get("active_index"));
			deptparmMap.put("version", requestedMap.get("version"));
			deptparmMap.put("created_on", requestedMap.get("created_on"));
			deptparmMap.put("updated_on", requestedMap.get("updated_on"));
			if (deptName == null || deptName.equals("")) {
				logger.error("ERROR:-  DEPARTMENT NAME IS NULL...");
				logger.trace("ERROR:-  DEPARTMENT NAME IS NULL...");
				return;
			}

		}
		// deptparmMap.put(TABLE_NAME,resultMap.get(TABLE_NAME));
		deptparmMap.put(DS_NAME, resultMap.get(DS_NAME));
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		Map<String, Object> parmMap = new HashMap<String, Object>();

		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.putAll(deptparmMap);
		dataMap.put("activity_data", deptparmMap);
		dataMap.put("version", deptparmMap.get("version"));
		boolean processDepartMent = processDepartMent(user, action, dataMap, userSelectedBusinessArea,
				userSelectedRecon);
	}

	private boolean processDepartMent(User user, String action, Map<String, Object> dataMap,
			String userSelectedBusinessArea, String userSelectedRecon) {

		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
		} catch (Exception e) {
			logger.error("ERROR:-- " + e.getMessage());
			logger.trace("ERROR:-- " + e.getMessage());
		}
		setUpDepartmentDataToPersist(action, dataMap);
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		if (userAdminManager.isUserUnderWorkflow(user)) {
			String activityStatus = "";
			if (SAVE.equalsIgnoreCase(action) || action.equalsIgnoreCase(SAVE) || action.equalsIgnoreCase(UPDATE)
					|| action.equalsIgnoreCase(DELETE)) {
				activityStatus = PENDING_APPROVAL;
			} else {
				activityStatus = DRAFT;
			}

			try {
				String comments = "";
				if (action.equalsIgnoreCase(SAVE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_NEW, dataMap, activityStatus, comments);
					persistDepartment(mapTemp, PENDING_APPROVAL, true, true, connection);
				} else if (action.equalsIgnoreCase(DELETE)) {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_DELETE, dataMap, activityStatus, comments);
					mapTemp.put("status", PENDING_APPROVAL);
					persistDepartment(mapTemp, PENDING_APPROVAL, false, true, connection);
				} else {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					comments = (String) mapTemp.get("location");
					userAdminManager.createActivity(connection, user, userSelectedBusinessArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_EDIT, dataMap, activityStatus, comments);
					/*
					 * Map modifyMap=(Map) mapTemp.get("activity_data"); Long
					 * version=(Long)modifyMap.get("version"); int
					 * intVersion=(int) (version-1); // Long
					 * versionDec=version-1; modifyMap.put("version",
					 * intVersion); System.out.println(modifyMap);
					 */
					persistDepartment(mapTemp, PENDING_APPROVAL, true, true, connection);
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				logger.error("ERROR:-- " + e.getMessage());
				logger.trace("ERROR:-- " + e.getMessage());
				// e.printStackTrace();
			}

		} else {/*
				 * try { System.out.println("Department is not under Work Flow"
				 * ); if(action.equalsIgnoreCase(DELETE)){
				 * persistDepartment(dataMap, APPROVED, false, false,
				 * connection); } else{ persistDepartment(dataMap, APPROVED,
				 * true, false, connection); } } catch (Exception e) { // TODO
				 * Auto-generated catch block e.printStackTrace(); }
				 * 
				 * 
				 */
			try {
				if (action.equalsIgnoreCase(DELETE)) {
					persistDepartment(dataMap, APPROVED, false, false, connection);
				} else if (action.equalsIgnoreCase(UPDATE)) {
					Map depatrmentsMap = (Map) dataMap.get("activity_data");
					depatrmentsMap.put("status", APPROVED);
					Long version = (Long) depatrmentsMap.get("version");
					int versionInt = (int) (version - 1);
					// Integer versionUpdate=new Integer((int) versionInt);
					depatrmentsMap.put("version", versionInt);
					customerDao.update(connection, depatrmentsMap, "updateDeparments");
				} else {
					persistDepartment(dataMap, APPROVED, true, false, connection);
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
		try {
			if (connection != null)
				connection.commit();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}

	public void persistDepartment(Map<String, Object> dataMap, String actStatus, boolean isNew, boolean isWorkflow,
			Connection connection) throws Exception {

		CustomerDao customerDao = new CustomerDao();
		boolean newActivityStatusUpdated = false;
		Map<String, Object> deptActivityMap = (Map<String, Object>) dataMap.get("activity_data");
System.out.println(dataMap.get("activity_type"));
		if (isNew) {
			String activity_type = (String) dataMap.get("activity_type");
			if (activity_type != null && activity_type.equals("EDIT")) {
				customerDao.update(connection, dataMap, "updateDeparments");
			} else {
				persistDepartmentEntity(dataMap, isWorkflow, customerDao, connection);
			}
			newActivityStatusUpdated = true;

		}
		if (APPROVED.equalsIgnoreCase(actStatus) && !newActivityStatusUpdated) {
			if (dataMap.get("activity_name") != null) {
				if (dataMap.get("activity_name").toString().equalsIgnoreCase("USER_ADMIN")) {
					if (dataMap.get("activity_type").toString().equalsIgnoreCase(OPERATION_NEW)) {
						// dataMap.put("status", APPROVED);
						deptActivityMap.put("status", APPROVED);
						// deptActivityMap.put("version",dataMap.get("version"));
						// dataMap.put("activity_data", act_map);
						customerDao.update(connection, deptActivityMap, "updateDeparments");
					} else if (dataMap.get("activity_type").toString().equalsIgnoreCase(OPERATION_EDIT)) {
						// dataMap.put("status", APPROVED);
						deptActivityMap.put("status", APPROVED);
						Long version = (Long) deptActivityMap.get("version");
						int intVersion = (int) (version - 1);
						// Long versionDec=version-1;
						deptActivityMap.put("version", intVersion);
						// deptActivityMap.put("version",dataMap.get("version"));
						// dataMap.put("activity_data", act_map);
						customerDao.update(connection, deptActivityMap, "updateDeparments");
					} else {
						Long version = (Long) deptActivityMap.get("version");
						// --version;
						deptActivityMap.put("version", --version);
						departmentStatusUpdate(connection, deptActivityMap, customerDao, DELETE, "N");
					}
				}
			} else {
				dataMap.put("status", DELETE);
				Long version = (Long) dataMap.get("version");
				// --version;
				deptActivityMap.put("version", --version);
				departmentStatusUpdate(connection, deptActivityMap, customerDao, DELETE, "N");
			}
		}
		if (REJECTED.equalsIgnoreCase(actStatus)) {
			deptActivityMap.put("status", REJECTED);

			/*
			 * //departmentStatusUpdate(connection, dataMap, customerDao,
			 * APPROVED,"N");
			 */
			// String activity_type=(String)dataMap.get("activity_type");
			// if((activity_type!=null) &&
			// (activity_type.equalsIgnoreCase("EDIT")))
			// {
			/*
			 * Integer version=(Integer)dataMap.get("version");
			 * deptActivityMap.put("version", --version);
			 */
			// roleStatusUpdate(connection, dataMap, customerDao, APPROVED,
			// "Y");
			// }
			// else{
			departmentStatusUpdate(connection, deptActivityMap, customerDao, REJECTED, "N");
			// }

		}

	}

	private void departmentStatusUpdate(Connection connection, Map<String, Object> dataMap, CustomerDao customerDao2,
			String approved, String string) {
		dataMap.put("status", approved);
		dataMap.put("active_index", string);
		try {
			customerDao.update(connection, dataMap, "DepartmentStatusUpdate");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	private void persistDepartmentEntity(Map<String, Object> dataMap, boolean isWorkflow, CustomerDao customerDao2,
			Connection connection) {
		if (isWorkflow) {
			dataMap.put("status", PENDING_APPROVAL);

		} else {
			dataMap.put("status", APPROVED);
		}
		customerDao.saveDepartments(dataMap);

	}

	public void submitFeatures() {
		Map<String, Object> parmMap;
		for (Feature feature : tempFeatureList) {
			parmMap = new HashMap<String, Object>();
			parmMap.put("feature", feature.getFeature());
			parmMap.put("discription", feature.getFeatureDiscription());
			parmMap.put("feature_id", feature.getFeatureId());
			parmMap.put("sno", feature.getSno());

			customerDao.saveFeatures(parmMap);
		}
		tempFeatureList.clear();
		try {
			this.userController = customerDao.loadAllUserPrivilege();// loadAllUserPrivilege();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("Unable to load All User Privilege", e);

		}
	}

	List<Feature> tempFeatureList = new ArrayList<Feature>();

	public void saveFeatures() {
		List<Feature> featureList = this.getUserController().getFeatures().getFeatures();
		int sno = 0;
		if (!featureList.isEmpty()) {
			Feature temFeatur = featureList.get(featureList.size() - 1);
			sno = temFeatur.getSno();
		}
		sno++;
		Feature tempFeature = new Feature();
		tempFeature.setSno(sno);
		tempFeature.setFeature(feature.getFeature());
		tempFeature.setFeatureDiscription(feature.getFeatureDiscription());
		tempFeature.setFeatureId(feature.getFeatureId());
		tempFeatureList.add(tempFeature);
		featureList.add(tempFeature);
	}

	public void submitUsers() {

		/*
		 * EntityType entityRole = (EntityType)
		 * FacesContext.getCurrentInstance()
		 * .getExternalContext().getSessionMap().get("entityType");
		 * entityRole.setMessageFlag(true); Tabs tabs = (Tabs)
		 * FacesContext.getCurrentInstance()
		 * .getExternalContext().getSessionMap().get("tabs");
		 * tabs.setUserAdminTabFlag(false); String comString =
		 * BeanUtil.getClientId( FacesContext.getCurrentInstance(),
		 * "formUsers"); RequestContext.getCurrentInstance().update(comString);
		 */
		Map<String, Object> parmMap;
		List<User> list = this.userController.getUsers().getUserList();

		CustomerDao customerDao = new CustomerDao();
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);

			// TODO: Need To Pass Under Work Flow
			for (User user : list) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("user_id", user.getUserId());
				customerDao.delete(connection, map, "TruncateUsers");
			}

			connection.commit();
		} catch (ClassNotFoundException | SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("Unable to Truncate Users", e);
			try {
				connection.rollback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
				logger.error("Unable to RollBack Operation", e);
			}
		} finally {
			try {
				if (connection != null && !connection.isClosed())
					connection.close();
			} catch (SQLException e) {

				e.printStackTrace();
				logger.error("Unable to Close Connection", e);
			}
		}

		if (!usersList.isEmpty())
			try {
				connection = DbUtil.getConnection();
				connection.setAutoCommit(false);
				for (User user : usersList) {
					parmMap = new HashMap<String, Object>();
					parmMap.put("user_id", user.getUserId());
					parmMap.put("user_name", user.getUserName());
					parmMap.put("email_id", user.getEmailId());
					parmMap.put("phon_number", user.getPhonNo());
					parmMap.put("dept_name", user.getDeptName());
					user.getSelectedReporting();

					String smsNotification = "";
					if (user.getSmsNotification()) {
						smsNotification = "Y";
					} else {
						smsNotification = "N";
					}
					String emailNotification = "";
					if (user.getEmailNotification()) {
						emailNotification = "Y";
					} else {
						emailNotification = "N";
					}

					parmMap.put("reporting", user.getReporting());
					parmMap.put("approval_role", user.getApprovalRole());
					parmMap.put("system_role", user.getSystemRole());
					parmMap.put("email_notification", emailNotification);
					parmMap.put("sms_notification", smsNotification);
					parmMap.put("branch_location", user.getBranchLocation());
					parmMap.put("approval_department", user.getApprovalDepartment());
					parmMap.put("version", 1);
					customerDao.insertData(connection, parmMap, "saveUsers");
				}
				connection.commit();
			} catch (Exception e) {
				e.printStackTrace();
				try {
					connection.rollback();
				} catch (SQLException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
					logger.error("Unable to Roll Back Operation", e1);
				}
			} finally {
				try {
					if (connection != null && !connection.isClosed())
						connection.close();
				} catch (SQLException e) {

					e.printStackTrace();
					logger.error("Unable to Close Operation", e);
				}
			}

		try {
			this.userController = customerDao.loadAllUserPrivilege();
		} catch (Exception e) {

			e.printStackTrace();
			logger.error("Unable to load All User Privilege", e);
		}
		usersList = this.userController.getUsers().getUserList();

	}

	List<User> userList = new ArrayList<User>();

	public boolean saveUsers(Map<String, Object> resultMap, User users) {
		Map<String, Object> usersParmMap = new HashMap<String, Object>();
		String userSelectedBusinessArea = (String) resultMap.get("userSelectedBusinessArea");
		String userSelectedRecon = (String) resultMap.get("userSelectedRecon");
		String actionStatus = (String) resultMap.get("action");
		List<Map<String, Object>> resultDataList = (List) resultMap.get(SELECTED_RECORDS);
		
		for (Map<String, Object> requestedMap : resultDataList) {
			String given_password = (String) requestedMap.get("password");
			String ldapUser = (String) requestedMap.get("isLdapUser");
				
			/*if (!validatePasswordPolicy(password)) {
				return false;
			}*/
			
			/////////// START  ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
					
				String passwordHash = "";
				if(ldapUser.equals("N")) {
					try {
						 passwordHash = PasswordGeneratorUtil.generateStorngPasswordHash(given_password);
					} catch (NoSuchAlgorithmException  | InvalidKeySpecException e) {
						e.printStackTrace();
					}
				}
					String password = passwordHash;
			
	        /////////// END  ADDED PASSWORD ENCRYPTION ON 25oct, 2018 /////////////
			usersParmMap.put("serverPort", requestedMap.get("serverPort"));
			usersParmMap.put("contextPath", requestedMap.get("contextPath"));
			
			usersParmMap.put("user_id", requestedMap.get("user_id"));
			usersParmMap.put("user_name", requestedMap.get("user_name"));
		
			usersParmMap.put("email_id", requestedMap.get("email_id"));
			usersParmMap.put("phon_number", requestedMap.get("phon_number"));

			if (actionStatus.equalsIgnoreCase(SAVE)) {
				//Sha256PasswordEncription encription= new Sha256PasswordEncription();
				//String passwordEncription=encription.PasswordEncription(password);
				usersParmMap.put("password", password);
			}
			usersParmMap.put("isLdapUser", requestedMap.get("isLdapUser"));
			usersParmMap.put("account_status", requestedMap.get("account_status"));
			usersParmMap.put("dept_name", requestedMap.get("dept_name"));
			usersParmMap.put("approval_department", requestedMap.get("dept_name"));

			List reportings = null;
			String reportingValue = "";
			if (requestedMap.get("reporting") != null) {
				if (actionStatus.equalsIgnoreCase(DELETE)) {
					String reportingTO = (String) requestedMap.get("reporting");
					usersParmMap.put("reporting", reportingTO);

				} else {
					/*
					 * String reportingTO =(String)
					 * requestedMap.get("reporting");
					 * usersParmMap.put("reporting",reportingTO);
					 */
					reportings = (List) requestedMap.get("reporting");
					for (Object object : reportings) {
						String reportingTemp = (String) object;
						reportingValue = reportingValue + reportingTemp + ",";
						String reportingSubstring = reportingValue.substring(0, reportingValue.length() - 1);
						usersParmMap.put("reporting", reportingSubstring);

					}
				}

			} else {
				usersParmMap.put("reporting", requestedMap.get("reporting"));
			}

			// System.out.println(reportingValue+"reportingValuereportingValuereportingValuereportingValue");
			// String reportingSubstring = reportingValue.substring(0,
			// reportingValue.length()-1);
			// System.out.println(reportingSubstring+"pravinpravinpravinpravinpravinpravinpravin");

			usersParmMap.put("system_role", requestedMap.get("system_role"));
			/*
			 * usersParmMap.put("email_notification",requestedMap.get(
			 * "email_notification"));
			 * usersParmMap.put("sms_notification",requestedMap.get(
			 * "sms_notification"));
			 */
			usersParmMap.put("branch_location", requestedMap.get("branch_location"));
			usersParmMap.put("status", requestedMap.get("status"));
			usersParmMap.put("active_index", requestedMap.get("active_index"));
			usersParmMap.put("version", requestedMap.get("version"));
			usersParmMap.put("created_on", requestedMap.get("created_on"));
			usersParmMap.put("updated_on", requestedMap.get("updated_on"));

			usersParmMap.put("pwd_exp_date", requestedMap.get("pwd_exp_date"));
			usersParmMap.put("approval_role", requestedMap.get("approval_role"));
		}

		// usersParmMap.put(TABLE_NAME,resultMap.get(TABLE_NAME));
		usersParmMap.put(DS_NAME, resultMap.get(DS_NAME));

		// int sno = 0;

		// sno++;
		// usersParmMap.put("", sno);// setSno(sno);
		// String reporting = "";

		/*
		 * if (actionStatus == null) { actionStatus = SUBMIT; } else {
		 * actionStatus = SAVE; }
		 */
		/*
		 * if(actionStatus.equalsIgnoreCase(SAVE)){ String smsNotification = "";
		 * if ((boolean)usersParmMap.get("sms_notification")==true) {
		 * smsNotification = "Y"; usersParmMap.put("sms_notification",
		 * smsNotification); } else { smsNotification = "N";
		 * usersParmMap.put("sms_notification", smsNotification); } String
		 * emailNotification = ""; if
		 * ((boolean)usersParmMap.get("email_notification")==true) {
		 * emailNotification = "Y"; usersParmMap.put("email_notification",
		 * emailNotification); } else { emailNotification = "N";
		 * usersParmMap.put("email_notification", emailNotification); }
		 * 
		 * 
		 * }
		 */
		/*
		 * else{ usersParmMap.put("email_notification",
		 * requestedMap.get("sms_notification"));
		 * usersParmMap.put("sms_notification", smsNotification); }
		 */

		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap.putAll(usersParmMap);
		dataMap.put("activity_data", usersParmMap);
		dataMap.put("version", usersParmMap.get("version"));
		// dataMap.put("username", this.user.getUserId());

		try {
			processUsers(users, actionStatus, dataMap, userSelectedBusinessArea, userSelectedRecon);
		} catch (Exception e1) {

		}
		return true;

	}

	public boolean validatePasswordPolicy(String pass) {

		if (pass != null) {
			List<Map<String, Object>> moduleNameList = new ArrayList<Map<String, Object>>();
			// AscentWebMetaInstance ascentWebMetaInstance =
			// AscentWebMetaInstance.getInstance();
			try {
				Map<String, Object> params = new HashMap<String, Object>();
				AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
				Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf("SELCT_PASS_POLICY");
				LoadRegulator loadRegulator = new LoadRegulator();
				moduleNameList = loadRegulator.loadCompleteData(params, queryConf);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			Map<String, Object> passMap = null;
			if (!moduleNameList.isEmpty()) {
				passMap = moduleNameList.get(0);
			} else {
				passMap = new HashMap<String, Object>();
			}
			int max = (int) passMap.get("maxLength");
			int min = (int) passMap.get("minLength");
			String isSpecialCharsAllowed = (String) passMap.get("isSpecialCharsAllowed");
			String isUpperCaseAllowed = (String) passMap.get("isUpperCaseAllowed");
			String isNumbersAllowed = (String) passMap.get("isNumbersAllowed");
			String specialChars = (String) passMap.get("specialChars");

			int totalSpecChars = 0;
			int totalUpperChars = 0;
			int totalNumChars = 0;
			if (min == 0 || max <= 0 || isSpecialCharsAllowed == null || isUpperCaseAllowed == null
					|| isNumbersAllowed == null || specialChars == null) {

				return false;
			}
			if (pass.length() < min) {

				return false;
			}
			if (pass.length() > max) {

				return false;
			}
			for (char character : pass.toCharArray()) {
				if (Character.isUpperCase(character)) {
					totalUpperChars++;
				} else if (Character.isDigit(character)) {
					totalNumChars++;
				} else if (Character.isSpaceChar(character)) {

				} else if (Character.isLetter(character)) {

				} else {
					boolean isSpecialExist = false;
					for (char specialChar : specialChars.toCharArray()) {
						if (character == specialChar) {
							isSpecialExist = true;
							break;
						}
					}
					if (!isSpecialExist) {

						return false;
					}
					totalSpecChars++;
				}
			}
			if (isUpperCaseAllowed.equalsIgnoreCase("Y") && totalUpperChars == 0) {

				return false;
			}
			if (isNumbersAllowed.equalsIgnoreCase("Y") && totalNumChars == 0) {

				return false;
			}
			if (isSpecialCharsAllowed.equalsIgnoreCase("Y") && totalSpecChars == 0) {

				return false;
			}
		}
		return true;
	}

	//
	private void reloadUsers(String message) {
		this.usersList.clear();

		List<Role> rolesTemp = null;
		try {
			Users users = customerDao.loadAllUsers();

			this.usersList = users.getUserList();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		/*
		 * String string2 = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "usersTable");
		 * RequestContext.getCurrentInstance().update(string2);
		 * 
		 * EntityType entityRole = (EntityType)
		 * FacesContext.getCurrentInstance()
		 * .getExternalContext().getSessionMap().get("entityType");
		 * entityRole.setErrorMessage(message); String string =
		 * BeanUtil.getClientId(FacesContext.getCurrentInstance(),
		 * "ErrorDialog");
		 */
		/* RequestContext.getCurrentInstance().update(string); */

		/*
		 * RequestContext.getCurrentInstance()
		 * .execute("PF('ErrorDialog').show();");
		 */
		/*
		 * String componentId = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "usersTable");
		 * RequestContext.getCurrentInstance().update(componentId);
		 */
	}

	/*
	 * private void showErrorDialog(String errorMessage) { EntityType entityRole
	 * = (EntityType) FacesContext.getCurrentInstance()
	 * .getExternalContext().getSessionMap().get("entityType");
	 * entityRole.setErrorMessage(errorMessage); String compId =
	 * BeanUtil.getClientId(FacesContext.getCurrentInstance(), "ErrorDialog");
	 * RequestContext.getCurrentInstance().update(compId);
	 * RequestContext.getCurrentInstance()
	 * .execute("PF('ErrorDialog').show();"); }
	 */

	public void onRowEditPriv(RowEditEvent event) {
		// System.out.println(event.getObject() + " ********");
	}

	public void onEditUser(ActionEvent event) {

		this.renderMap.put("UpdateUser", "UPDATE");
		this.user = (User) event.getComponent().getAttributes().get("EditUser");
		/* System.out.println(user); */
		String userId = user.getUserId();
		List<User> list = this.userController.getUsers().getUserList();
		for (User user2 : list) {
			/* System.out.println(user2.getPhonNo()); */
		}
		try {
			userIds.clear();
			Map<String, Object> paramValues = new HashMap<String, Object>();
			paramValues.put("dept_name", user.getDeptName());
			List<Object> userIdList = customerDao.getDataListByField("GetUsersIdByReporting", "user_id", paramValues);
			for (Object object : userIdList) {
				String userId1 = (String) object;
				userIds.add(userId1);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		/*
		 * String adminDialogUser = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "adminDialogUser");
		 * RequestContext.getCurrentInstance().update(adminDialogUser);
		 * RequestContext.getCurrentInstance().execute(
		 * "PF('adminDialogUser').show();");
		 * 
		 * String dataTableId = BeanUtil.getClientId(
		 * FacesContext.getCurrentInstance(), "usersTable");
		 * RequestContext.getCurrentInstance().update(dataTableId);
		 */

	}

	public void onRowEditDepartment(RowEditEvent event) {

		Department department = (Department) event.getObject();
		Map<String, Object> parmMap;

		parmMap = new HashMap<String, Object>();
		parmMap.put("dept_name", department.getDeptName());
		parmMap.put("dept_id", department.getDeptId());
		parmMap.put("category", department.getDeptCategory());

		parmMap.put("location", department.getDeptLocation());

		Connection connection = DbUtil.getConnection();
		try {
			connection.setAutoCommit(false);
			customerDao.updateDeparments(connection, parmMap);
			connection.commit();
		} catch (ClassNotFoundException | SQLException e) {
			// TODO Auto-generated catch block clearOldUser onRowEditDepartment
			e.printStackTrace();
			try {
				connection.rollback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			logger.error("Unable to Update Deparments", e);
		} finally {
			try {
				connection.close();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}

	/*
	 * public void updatePrivielegeId() { privilegeDetails=new
	 * PrivilegeDetails(); privilegeDetails.setPid(privilege.getPrivilegeId());
	 * EntityType entityRole = (EntityType) FacesContext.getCurrentInstance()
	 * .getExternalContext().getSessionMap().get("entityType"); String
	 * componentId = BeanUtil.getClientId( FacesContext.getCurrentInstance(),
	 * "pirivileId"); String
	 * comidDlg=BeanUtil.getClientId(FacesContext.getCurrentInstance(),
	 * "previlegeDetails");
	 * RequestContext.getCurrentInstance().update(componentId);
	 * RequestContext.getCurrentInstance().update(comidDlg);
	 * RequestContext.getCurrentInstance().execute(
	 * "PF('previlegeDetails').show()"); }
	 */

	List<PrivilegeDetails> tempList = new ArrayList<PrivilegeDetails>();

	private void persistPrivilegeEntity(Map<String, Object> dataMap, boolean isWorkflow, CustomerDao customerDao2,
			Connection connection) {
		if (isWorkflow) {
			dataMap.put("status", PENDING_APPROVAL);

		} else {
			dataMap.put("status", APPROVED);
		}
		customerDao.savePrevielege(dataMap);

	}

	public void savePrevilegeDetails(Map<String, Object> recordMap, User user, String userSelectedBArea,
			String userSelectedRecon) {

		List<Privilege> privList = null;

		Connection connection = null;
		connection = DbUtil.getConnection();

		try {
			String action = "";
			String data = SUBMIT;
			if (data != null && data.equalsIgnoreCase("Update")) {
				action = UPDATE;
			} else {
				action = SUBMIT;
			}

			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();

			Map<String, Object> privilegeMap = new HashMap<String, Object>();

			Map<String, Object> dataMap = new HashMap<String, Object>();

			int sno = 1;
			Map<String, Object> paramValues1 = new HashMap<String, Object>();
			paramValues1.put("role", (String) recordMap.get("selectedRole"));
			Map<String, Object> supervisorPendingItm = customerDao.getDataBaseMap("getMaxVersionFromPrevileges",
					paramValues1);

			List<Map<String, Object>> privilageRecord = (List<Map<String, Object>>) recordMap.get(SELECTED_RECORDS);
			privilegeMap.put("role", (String) recordMap.get("selectedRole"));

			privilegeMap.put("privilegeId", (String) recordMap.get("privilegeId"));
			privilegeMap.put("sno", sno++);
			privilegeMap.put("version", supervisorPendingItm.get("max"));
			privilegeMap.put("status", "");

			ByteArrayOutputStream bos = new ByteArrayOutputStream();
			ObjectOutputStream oos = new ObjectOutputStream(bos);
			oos.writeUnshared(privilageRecord);
			oos.flush();
			oos.close();

			byte[] data1 = bos.toByteArray();
			privilegeMap.put("privilge_details", data1);
			privilegeMap.put("activity_type", OPERATION_NEW);
			privilegeMap.put(DS_NAME, recordMap.get(DS_NAME));
			// privilegeMap.put("userSelectedBArea", userSelectedBArea);
			// privilegeMap.put("userSelectedRecon", userSelectedRecon);
			dataMap.putAll(privilegeMap);
			dataMap.put("activity_data", privilegeMap);
			dataMap.put("version", supervisorPendingItm.get("max"));
			processPrievilege(user, action, dataMap, userSelectedBArea, userSelectedRecon);

		} catch (Exception exception) {
			// System.out.println(exception);
		}

	}

	public void persistPrivilege(Map<String, Object> dataMap, String actStatus, boolean isNew, boolean isWorkflow,
			Connection connection) throws Exception {

		CustomerDao customerDao = new CustomerDao();
		boolean newActivityStatusUpdated = false;

		if (isNew) {
			String activity_type = (String) dataMap.get("activity_type");
			if (activity_type != null && activity_type.equals(DELETE)) {

			} else {
				// System.out.println(activity_type+" activity_type");
				persistPrivilegeEntity(dataMap, isWorkflow, customerDao, connection);
			}
			newActivityStatusUpdated = true;

		}
		if (APPROVED.equalsIgnoreCase(actStatus) && !newActivityStatusUpdated) {
			if (dataMap.get("activity_name") != null) {
				if (dataMap.get("activity_name").toString().equalsIgnoreCase("USER_ADMIN")) {
					if (dataMap.get("activity_type").toString().equalsIgnoreCase(OPERATION_DELETE)) {
						dataMap.put("status", DELETE);
						// customerDao.update(connection, dataMap,
						// "updateRoles");
					} else {
						String object = (String) dataMap.get("privilegeId");
						privilegeStatusUpdate(connection, dataMap, customerDao, APPROVED, "Y");
					}
				}
			} else {
				// dataMap.put("status", APPROVED);
				privilegeStatusUpdate(connection, dataMap, customerDao, APPROVED, "Y");
			}
		}
		if (REJECTED.equalsIgnoreCase(actStatus)) {
			dataMap.put("status", REJECTED);
			privilegeStatusUpdate(connection, dataMap, customerDao, APPROVED, "N");

		}

	}

	private void privilegeStatusUpdate(Connection connection, Map<String, Object> dataMap, CustomerDao customerDao2,
			String approved, String string) {
		dataMap.put("status", approved);
		dataMap.put("active_index", string);
		Map<String, Object> object = (Map<String, Object>) dataMap.get("activity_data");
		object.put("status", APPROVED);
		try {
			customerDao.update(connection, dataMap, "privilegeStatusUpdate");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	public void updatePrivilege(ActionEvent event) {
		this.privilegeDetails = (PrivilegeDetails) event.getComponent().getAttributes().get("editPriv");
		this.operations.clear();
		if (privilegeDetails.getModule().equalsIgnoreCase("OnBoard")) {

			this.operations.add("New");
			this.operations.add("Edit");
			this.operations.add("View");
		} else if (privilegeDetails.getModule().equalsIgnoreCase("Project Administrator")) {
			this.operations.add("Activity Maker");
			this.operations.add("Activity View");
		} else if (privilegeDetails.getModule().equalsIgnoreCase("User Administration")) {
			this.operations.add("Activity Maker");
			this.operations.add("Activity View");
		}
		renderMap.put("editPriv", "EDIT");
		deleteMap.put("privilegeDetails", this.privilegeDetails);
		PrivilegeDetails privilegeDetailsTemp = new PrivilegeDetails();
		/*
		 * privilegeDetailsTemp.setAccesibility(privilegeDetails.getAccesibility
		 * ());
		 * privilegeDetailsTemp.setDescription(privilegeDetails.getDescription()
		 * ); privilegeDetailsTemp.setModule(privilegeDetails.getModule());
		 * privilegeDetailsTemp.setOperation(privilegeDetails.getOperation());
		 * privilegeDetailsTemp.setPid(privilegeDetails.getPid());
		 * privilegeDetailsTemp.setSno(privilegeDetails.getSno());
		 * privilegeDetailsTemp.setStatus(privilegeDetails.getStatus());
		 * privilegeDetailsTemp.setVersion(privilegeDetails.getVersion());
		 * privilegeDetailsTemp.setDescription(privilegeDetails.getDescription()
		 * ); deleteMap.put("privilegeDetailsTemp", privilegeDetailsTemp);
		 * 
		 * this.privilege.setVersion(privilegeDetails.getVersion());
		 * RequestContext.getCurrentInstance().update(string);
		 * RequestContext.getCurrentInstance().execute(
		 * "PF('previlegeDetails').show();");
		 */

	}

	private boolean processPrievilege(User user, String action, Map<String, Object> dataMap, String userSelectedBArea,
			String userSelectedRecon) {
		System.out.println("process Preivilage ...&&&&&&");
		Connection connection = null;
		try {
			connection = DbUtil.getConnection();
			connection.setAutoCommit(false);
		} catch (Exception e) {

		}
		setUpPrivilegeDataToPersist(action, dataMap);
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		if (userAdminManager.isUserUnderWorkflow(user)) {
			String activityStatus = "";
			if (SUBMIT.equalsIgnoreCase(action) || action.equalsIgnoreCase(UPDATE)) {
				activityStatus = PENDING_APPROVAL;
			} else {
				activityStatus = DRAFT;
			}

			try {
				if (action.equalsIgnoreCase(SUBMIT)) {
					Map<String, Object> mapInnerTemp = (Map<String, Object>) dataMap.get("activity_data");
					mapInnerTemp.put("status", PENDING_APPROVAL);
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					userAdminManager.createActivity(connection, user, userSelectedBArea, userSelectedRecon,
							MODULE_USER_ADMIN, OPERATION_NEW, dataMap, activityStatus, activityStatus);
					persistPrivilege(mapTemp, PENDING_APPROVAL, true, true, connection);
				} else {
					Map<String, Object> mapTemp = new HashMap<String, Object>(dataMap);
					userAdminManager.createActivity(connection, user, MODULE_USER_ADMIN, OPERATION_EDIT, activityStatus,
							activityStatus, dataMap, activityStatus, activityStatus);
					persistPrivilege(mapTemp, PENDING_APPROVAL, true, true, connection);

					/*
					 * persistRole(dataMap, PENDING_APPROVAL, true, true,
					 * connection);
					 */
				}
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		} else {
			try {
				persistPrivilege(dataMap, APPROVED, true, false, connection);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}
		try {
			if (connection != null)
				connection.commit();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return false;
	}

	private void privilegeError(String errorMsg, String id) {
		/*
		 * EntityType entityRole = (EntityType)
		 * FacesContext.getCurrentInstance()
		 * .getExternalContext().getSessionMap().get("entityType");
		 * entityRole.setErrorMessage(errorMsg); String compId =
		 * BeanUtil.getClientId(FacesContext.getCurrentInstance(), id);
		 * RequestContext.getCurrentInstance().update(compId); //
		 * PF('previlegeDetails').show(); String dlgName = "PF('" + id +
		 * "').show();"; RequestContext.getCurrentInstance().execute(dlgName);
		 */
	}

	List<List<PrivilegeDetails>> megaList = new ArrayList<List<PrivilegeDetails>>();
	Map<Integer, PrivilegeDetails> privelegeBackUp = new HashMap<Integer, PrivilegeDetails>();

	public void saveToDataTable(ActionEvent actionEvent) {

		String moduleString = privilegeDetails.getModule();
		String operationString = privilegeDetails.getOperation();
		if (moduleString == null || moduleString.equals("")) {
			privilegeError("Please select  module", "PrivilegeError");
			return;

		}
		if (operationString == null || operationString.equals("")) {
			privilegeError("Please enter  operation", "PrivilegeError");
			return;
		}
		if (operationString == null || operationString.equals("")) {
			privilegeError("Please enter  operation", "PrivilegeError");
			return;
		}

		int sno = 1;
		privilegeDetails.setSno(sno++);
		String string = renderMap.get("editPriv");
		if (string == null) {
			string = "";
		}
		PrivilegeDetails details = null;
		/*
		 * if (string != null && string.equals("EDIT")) { details =
		 * (PrivilegeDetails) } else {
		 */
		details = new PrivilegeDetails();
		/* } */
		boolean flag = false;
		for (Iterator iterator = pervilegeDetailList.iterator(); iterator.hasNext();) {
			PrivilegeDetails department = (PrivilegeDetails) iterator.next();
			if (department.getOperation().equals(privilegeDetails.getOperation())
					&& privilegeDetails.getModule().equals(privilegeDetails.getModule())) {

				flag = true;
				break;
			}

		}

		if (flag) {
			List<PrivilegeDetails> pervilegeDetailListe = new ArrayList<PrivilegeDetails>(pervilegeDetailList);
			List<PrivilegeDetails> pervilegeDetailListeTemp = new ArrayList<PrivilegeDetails>();
			// closePrivDialog();

			/*
			 * for (PrivilegeDetails privilegeDetails : pervilegeDetailList) {
			 * for (PrivilegeDetails privilegeDetails2 : pervilegeDetailListe) {
			 * if(!privilegeDetails2.getOperation().equals(privilegeDetails.
			 * getOperation())){ pervilegeDetailListeTemp.add(privilegeDetails);
			 * break; } } }
			 */
			/* pervilegeDetailList.clear(); */
			for (Map.Entry<Integer, PrivilegeDetails> entry : privelegeBackUp.entrySet()) {
				pervilegeDetailList.add(entry.getValue());
				// System.out.println(entry.getKey() + "/" + entry.getValue());
			}

			/*
			 * details=(PrivilegeDetails) deleteMap.get("privilegeDetailsTemp");
			 * iterator.remove();
			 * 
			 * 
			 * this.pervilegeDetailList.add(details);
			 */

			privilegeError("operation already exist", "PrivilegeError");
			return;
		}

		String allowed = "";
		if (privilegeDetails.getAccesibility() != null && privilegeDetails.getAccesibility().equals("Allowed")) {
			allowed = "Y";
		} else {
			allowed = "N";
		}
		details.setAccesibility(allowed);
		details.setDescription(privilegeDetails.getDescription());
		details.setModule(privilegeDetails.getModule());
		details.setOperation(privilegeDetails.getOperation());
		details.setPid(privilegeDetails.getPid());
		details.setSno(privilegeDetails.getSno());
		details.setVersion(privilegeDetails.getVersion());
		details.setStatus(privilegeDetails.getStatus());
		int no = pervilegeDetailList.size();
		privelegeBackUp.put(no, details);
		if (string != null && !string.equals("EDIT"))
			pervilegeDetailList.add(details);
		tempList.add(details);

		renderMap.put("editPriv", "");
		deleteMap.put("privilegeDetails", new PrivilegeDetails());

		/*
		 * onclick="PF('previlegeDetails').hide();"
		 * 
		 * System.out.println("table id " + componentDataTableId);
		 */

	}

	public void removeUser(ActionEvent event) {
		User deleteAdmin = (User) event.getComponent().getAttributes().get("deletRole");
		// BankDetails bank = (BankDetails)
		// event.getComponent().getAttributes().get("deleteBnk");
		CustomerDao cDao = new CustomerDao();
		if (deleteAdmin != null) {
			// userController.roles.roles
			List<Role> admins = this.userController.getRoles().getRoles();
			admins.remove(deleteAdmin);
			FacesMessage msg = new FacesMessage("Admin Details deleted.", deleteAdmin.getUserId());

		}
	}

	public void removeAdmin(ActionEvent event) {
		Role deleteAdmin = (Role) event.getComponent().getAttributes().get("deletRole");
		// BankDetails bank = (BankDetails)
		// event.getComponent().getAttributes().get("deleteBnk");
		CustomerDao cDao = new CustomerDao();
		if (deleteAdmin != null) {
			// userController.roles.roles
			List<Role> admins = this.userController.getRoles().getRoles();
			admins.remove(deleteAdmin);
			FacesMessage msg = new FacesMessage("Admin Details deleted.", deleteAdmin.getRole());
			Map<String, Object> params = new HashMap<String, Object>();
			params.put(values.getString("adminRole.role.column"), deleteAdmin.getRole());
			Connection connection = null;
			try {
				connection = DbUtil.getConnection();
				connection.setAutoCommit(false);
				cDao.delete(connection, params, "deleteRole");
				connection.commit();
			} catch (ClassNotFoundException | SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				try {
					connection.rollback();
				} catch (SQLException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
					logger.error("Unable to Roll Back Operation", e1);
				}
			} finally {
				try {
					if (connection != null && !connection.isClosed()) {
						connection.close();
					}
				} catch (SQLException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					logger.error("Unable to Close Connection", e);
				}
			}
			try {
				this.userController = customerDao.loadAllUserPrivilege();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				logger.error("Unable to load All User Privilege", e);
			}

			// }
			// }
		}
	}

	public void clearOldAdmin() {
		role = new Role();
	}

	public void clearOldUser() {
		user = new User();
	}

	public UserController getUserController() {
		return userController;
	}

	public void setUserController(UserController userController) {
		this.userController = userController;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public Department getDepartment() {
		return department;
	}

	public void setDepartment(Department department) {
		this.department = department;
	}

	public Department getDeleteDepartment() {
		return deleteDepartment;
	}

	public void setDeleteDepartment(Department deleteDepartment) {
		this.deleteDepartment = deleteDepartment;
	}

	public Privilege getPrivilege() {
		return privilege;
	}

	public void setPrivilege(Privilege privilege) {
		this.privilege = privilege;
	}

	public PrivilegeDetails getPrivilegeDetails() {
		return privilegeDetails;
	}

	public void setPrivilegeDetails(PrivilegeDetails privilegeDetails) {
		this.privilegeDetails = privilegeDetails;
	}

	public List<PrivilegeDetails> getPervilegeDetailList() {

		return pervilegeDetailList;
	}

	public void setPervilegeDetailList(List<PrivilegeDetails> pervilegeDetailList) {
		this.pervilegeDetailList = pervilegeDetailList;
	}

	public User getLoggedInUser() {
		return loggedInUser;
	}

	public void setLoggedInUser(User loggedInUser) {
		this.loggedInUser = loggedInUser;
	}

	public Feature getFeature() {
		return feature;
	}

	public void setFeature(Feature feature) {
		this.feature = feature;
	}

	public ResourceBundle getValues() {
		return values;
	}

	public void setValues(ResourceBundle values) {
		this.values = values;
	}

	public List<User> getUsersList() {
		return usersList;
	}

	public void setUsersList(List<User> usersList) {
		this.usersList = usersList;
	}

	public List<Role> getRoleList() {
		return roleList;
	}

	public void setRoleList(List<Role> roleList) {
		this.roleList = roleList;
	}

	public List<Department> getDeptList() {
		return deptList;
	}

	public void setDeptList(List<Department> deptList) {
		this.deptList = deptList;
	}

	public boolean isMesageFlag() {
		return mesageFlag;
	}

	public void setMesageFlag(boolean mesageFlag) {
		this.mesageFlag = mesageFlag;
	}

	public List<String> getOperations() {
		return operations;
	}

	public void setOperations(List<String> operations) {
		this.operations = operations;
	}

	public List<String> getUserIds() {
		return userIds;
	}

	public void setUserIds(List<String> userIds) {
		this.userIds = userIds;
	}

	public Map<String, String> getRenderMap() {
		return renderMap;
	}

	public void setRenderMap(Map<String, String> renderMap) {
		this.renderMap = renderMap;
	}

	// savePrevielege

}
