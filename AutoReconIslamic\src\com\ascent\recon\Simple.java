package com.ascent.recon;

import java.io.IOException;


import java.io.IOException;

import java.io.*;

import javax.servlet.ServletException;

import javax.servlet.http.HttpServlet;

import javax.servlet.http.HttpServletRequest;

import javax.servlet.http.HttpServletResponse;

import java.sql.*;

import com.lowagie.text.*;

import java.util.*;

import com.lowagie.text.pdf.PdfWriter;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Servlet implementation class Simple
 */
@WebServlet("/Simple")
public class Simple extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public Simple() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		  OutputStream out = null;

	       

	        

	        Document document = new Document(PageSize.A4, 50, 350, 50, 50);

	       

	        

	        try {

	            response.setContentType("application/pdf");

	            PdfWriter.getInstance(document, response.getOutputStream());

	            document.open();
	            Paragraph paragraph = new Paragraph("View Data");
	            

	            document.add(paragraph);

	            

	        } catch (Exception e) {

	            throw new ServletException("Exception in PDF  Servlet", e);

	        } finally {

	            document.close();

	        }

	        document.close();

	    }
	}


