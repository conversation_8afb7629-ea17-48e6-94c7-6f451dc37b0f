package com.ascent.persistance;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DbCursor {
	private static Logger logger = LogManager.getLogger(DbCursor.class.getName());
	private int noColumns;
	private ResultSet resultSet;
	private PreparedStatement loadPstmt;
	private ResultSetMetaData resultSetMetaData;
	private List<String> columnNames;
	private int batchSize = 50;
	List<Map<String, Object>> records = new ArrayList<Map<String, Object>>();

	public DbCursor(int noColumns, int batchSize, PreparedStatement loadPstmt, ResultSet resultSet,
			ResultSetMetaData resultSetMetaData, List<String> columnNames) {
		this.noColumns = noColumns;
		this.batchSize = batchSize;
		this.resultSet = resultSet;
		this.loadPstmt = loadPstmt;
		this.resultSetMetaData = resultSetMetaData;
		this.columnNames = columnNames;
	}

	public DbCursor(int noColumns, PreparedStatement loadPstmt, ResultSet resultSet,
			ResultSetMetaData resultSetMetaData, List<String> columnNames) {

		this.noColumns = noColumns;

		this.resultSet = resultSet;
		this.loadPstmt = loadPstmt;
		this.resultSetMetaData = resultSetMetaData;
		this.columnNames = columnNames;

	}

	public DbCursor(int noColumns, int batchSize, ResultSet resultSet, ResultSetMetaData resultSetMetaData,
			List<String> columnNames) {
		this.noColumns = noColumns;
		this.batchSize = batchSize;
		this.resultSet = resultSet;

		this.resultSetMetaData = resultSetMetaData;
		this.columnNames = columnNames;
	}

	public DbCursor(int noColumns, ResultSet resultSet, ResultSetMetaData resultSetMetaData, List<String> columnNames) {

		this.noColumns = noColumns;

		this.resultSet = resultSet;

		this.resultSetMetaData = resultSetMetaData;
		this.columnNames = columnNames;

	}

	public List<Map<String, Object>> getNextBatch() throws SQLException {

		List<Map<String, Object>> records = new ArrayList<Map<String, Object>>();
		boolean flag = false;
		try {
			if (!resultSet.isClosed()) {
				flag = resultSet.next();
				while (flag && records.size() < batchSize) {

					// System.out.println(resultSet.getInt(1) + " imp");
					Map<String, Object> record = new HashMap<String, Object>();

					for (int i = 0; i < noColumns; i++) {
						record.put(columnNames.get(i), resultSet.getObject(i + 1));
					}
					records.add(record);
					if (records.size() < batchSize) {
						flag = resultSet.next();
					}
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			if (!flag) {
				try {
					if (resultSet != null && !resultSet.isClosed()) {
						resultSet.close();
					}
				} catch (Exception e) {
					e.printStackTrace();
				} catch (Throwable e) {
					e.printStackTrace();
				}

				try {
					if (loadPstmt != null && !loadPstmt.isClosed()) {
						loadPstmt.close();
					}
				} catch (Exception e) {
					e.printStackTrace();
				} catch (Throwable e) {
					e.printStackTrace();
				}
			}

		}

		return records;
	}

}
