package com.ascent.recon;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

public class InsertData1 {

	public InsertData1() {
		// TODO Auto-generated constructor stub
	}

	public static void main(String[] args) throws ClassNotFoundException, SQLException {
		List<Object> list=new ArrayList<Object>();
		
		
		Map<String,Object> dataMap=new HashMap<String,Object>();
		
		dataMap.put("name", "pallavi");
		
		dataMap.put("age", 23);
		String tableName="Test";

		
		 Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
		 Connection con=DriverManager.getConnection("*****************************************************************");
		 System.out.println("connection ok");
		 String query=" INSERT INTO " + " " + tableName + "(";
	String value="";
	Object val=null;
		System.out.println(dataMap.size());
		Iterator itr=dataMap.keySet().iterator();
		for(int f=0;f< dataMap.keySet().size();f++){
			String key=(String) itr.next();
			 val=dataMap.get(key);
			System.out.println(val);
			
			if(f<dataMap.keySet().size()-1){
				query=query.concat(key +  " , ");
			}
			else{
				//value=value.concat(val);
				query=query.concat(key  + " ) VALUES (");
			}
			
		}
		
		
		System.out.println(" sql query :"+   query);
		
/*	PreparedStatement	preparedStatement = con.prepareStatement(query);

		int affectedRows = preparedStatement.executeUpdate();
		System.out.println(affectedRows);
	
		System.out.println("inserted data");*/
	
		// TODO Auto-generated method stub

	}
	public static void insertData(Map dataMap, String tableName) throws SQLException, ClassNotFoundException {
		 Connection con=null;
		 Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
		 con=DriverManager.getConnection("*****************************************************************");
		StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
		StringBuilder placeholders = new StringBuilder();

		for (Iterator<String> iter = dataMap.keySet().iterator(); iter.hasNext();) {
		    sql.append(iter.next());
		    placeholders.append("?");

		    if (iter.hasNext()) {
		        sql.append(",");
		        placeholders.append(",");
		    }
		}

		sql.append(") VALUES (").append(placeholders).append(")");
	PreparedStatement	preparedStatement = con.prepareStatement(sql.toString());
		int i = 0;

		for (Object value : dataMap.values()) {
		    preparedStatement.setObject(i++, value);
		}

		int affectedRows = preparedStatement.executeUpdate();
		System.out.println(affectedRows);
	}
	
	

}
