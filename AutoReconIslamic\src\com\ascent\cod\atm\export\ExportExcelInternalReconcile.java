package com.ascent.cod.atm.export;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExportExcelInternalReconcile {
	
	private static Logger logger = LogManager.getLogger(ExportExcelExternalSuppress.class.getName());
	
	public static void main(String[] args) throws IOException {
	//	exportExel();
	}

	public static String exportExcel3(List<Map<String, Object>> internalrecondataList) throws IOException {
	/*public static String exportExcel1(List<Map<String, Object>> unmatchList,String department) throws IOException {*/
		Date date = new Date();
		//String fileRec=recon.replace("/", "_");
		/*String fileRec=department.replace("/", "_");*/
		String fileName = String.format("ATMInternalReconcileData.xlsx", date);
		/*String fileName = String.format("kaushal_"+fileRec+".xlsx", department, date);
	*/
		

		  List<Map<String,Object>> unmatchList1 = new ArrayList<Map<String,Object>>();
			for(Map map11 : internalrecondataList){
			Map<String, Object> dataMapList = new LinkedHashMap<String, Object>();
			
			
		    dataMapList.put("TRAN ID",map11.get("TRAN ID"));
		    dataMapList.put("TRAN PARTICULAR",map11.get("TRAN PARTICULAR"));
		    dataMapList.put("TRAN CRNCY CODE",map11.get("TRAN CRNCY CODE"));
		    dataMapList.put("CUSTOMER ACCT",map11.get("CUSTOMER ACCT"));
		    dataMapList.put("TRAN DATE",map11.get("TRAN DATE"));
		    dataMapList.put("VALUE DATE",map11.get("VALUE DATE"));
		    dataMapList.put("CUSTOMER ACCT",map11.get("CUSTOMER ACCT"));
		    dataMapList.put("ATM ACCOUNT",map11.get("ATM ACCOUNT"));
		    dataMapList.put("AMOUNT",map11.get("AMOUNT"));
			dataMapList.put("REF AMT",map11.get("REF AMT"));
			dataMapList.put("REF CRNCY CODE",map11.get("REF CRNCY CODE"));
			dataMapList.put("TRAN REMARKS",map11.get("TRAN REMARKS"));
			dataMapList.put("REF CRNCY CODE",map11.get("REF AMT"));
			dataMapList.put("DRCR",map11.get("REF CRNCY CODE"));
			dataMapList.put("RECON STATUS",map11.get("RECON STATUS"));
			 unmatchList1.add(dataMapList);
		   }
		
		String filePath =  "D:/Dofar/COD/ATM/INTERNAL_EXTERNAL_RECONCILE/"+fileName;
	    Set<String> columnNamesSet = unmatchList1.get(0).keySet();
		String[] columnNames = columnNamesSet.stream().toArray(String[] ::new);
		
		/*System.out.println("Before: "+columnNamesSet);
		
		ArrayList<String> myList = new ArrayList<String>(columnNamesSet);
		myList.remove("PERSON");
		myList.remove("business_area");
		
		
		columnNames = myList.toArray(new String[0]);
		
		System.out.println("After: "+Arrays.toString(columnNames));
		System.out.println(myList);
		*/
		Workbook workbook = new XSSFWorkbook();
		Sheet sheet = workbook.createSheet("Contacts");

		Font headerFont = workbook.createFont();
		CellStyle headerCellStyle = workbook.createCellStyle();
		headerCellStyle.setFont(headerFont);
		
		/*Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.RED.getIndex());

*/
		// Create a Row
		Row headerRow = sheet.createRow(0);
		
		
		for (int i = 0; i < columnNames.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columnNames[i]); 
			cell.setCellStyle(headerCellStyle);
		}
	
		// Create Other rows and cells with contacts data
		int rowNum = 1;

		// for (Contact contact : contacts) {
		for (int i = 0; i < unmatchList1.size(); i++) {
			Map<String, Object> map = unmatchList1.get(i);
			
			int count = 0;
			Row row = sheet.createRow(rowNum++);

			for (Map.Entry<String, Object> entry : map.entrySet()) {
				
			row.createCell(count++).setCellValue(entry.getValue() == null ? "" : entry.getValue().toString());
			}
		}
	
		// Resize all columns to fit the content size
		for (int i = 0; i < columnNames.length; i++) {
			sheet.autoSizeColumn(i);
		}

		// Write the output to a file
		FileOutputStream fileOut = new FileOutputStream(filePath);
		workbook.write(fileOut);
		fileOut.close();
	
		logger.debug("export done...");

		return filePath;
	
		}


}
