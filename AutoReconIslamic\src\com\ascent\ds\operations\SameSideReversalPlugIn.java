package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class SameSideReversalPlugIn extends BasicDataSource implements PagesConstants{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public SameSideReversalPlugIn() {
	}
public DSResponse executeFetch(final DSRequest request)throws Exception
{
	Map<String, Object> result = null;
	DSResponse response=new DSResponse();
	Map requestMap=request.getValues();
	HttpSession httpSession = request.getHttpServletRequest().getSession();

	User user = (User) httpSession.getAttribute("userId");

	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		response.setData(result);
		return response;
	}
	String userId = user.getUserId();
	String userName=user.getUserName();
	String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
	String reconName = (String) httpSession.getAttribute("user_selected_recon");

	List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) requestMap.get("selectedRecords");
	String integrationName = (String) requestMap.get("integrationName");
	String reconTableName=(String)requestMap.get("reconTableName");
	//String action = (String) requestMap.get("action");
	String comments = (String) requestMap.get("comments");
	String reconDataSorce=(String) requestMap.get("reconDataSorce");
	//String moduleName =(String)requestMap.get("moduleName");
	String dsName=(String)requestMap.get("dsName");
	// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
	String centrifugalAmountField= null;
	if(requestMap.get("centrifugalAmount") != null)	centrifugalAmountField= requestMap.get("centrifugalAmount").toString();
	String activity_name= requestMap.get("activity_name").toString();
	String activity_type= requestMap.get("activity_type").toString();
	if (selectedRecords != null) {

		StringBuilder commentSb = new StringBuilder();
		List<Object> workflowIds = new ArrayList<Object>();
		for (Map<String, Object> rec : selectedRecords) {
          if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
				
			}else{
				workflowIds.add(rec.get(SID));
			}
		}
		if (workflowIds.size() > 0) {
			result = new HashMap<String, Object>();
			String commentPrefix = null;
			if (workflowIds.size() == 1) {
				commentSb.append("Selected record with SID ");
			} else if (workflowIds.size() > 1) {
				commentSb.append("Selected records with SIDs ");
			}
			for (Object obj : workflowIds) {
				if (commentSb.length() != 0) {
					commentSb.append(",");
				}
				commentSb.append(obj);
			}
			commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
			updateResultStatus(result, FAILED, commentPrefix);
			response.setData(result);
			return response;
		}
					
	}
	Map<String, Object> paramsMap = new HashMap<String, Object>();
	
	//paramsMap.put(ACTION, action);
	paramsMap.put(USER_ID, userId);
	paramsMap.put(SELECTED_RECORDS, selectedRecords);
	paramsMap.put(INTEGRATION_NAME, integrationName);
	paramsMap.put(BUSINES_AREA, businesArea);
	paramsMap.put(RECON_NAME, reconName);
	paramsMap.put(COMMENTS, comments);
	//paramsMap.put(MODULE, moduleName);
	paramsMap.put(DS_NAME, dsName);
	paramsMap.put("reconDataSorce", reconDataSorce);
	paramsMap.put("activity_name", activity_name);
	paramsMap.put("activity_type", activity_type);
	paramsMap.put("reconTableName", reconTableName);
	
	// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
	if(centrifugalAmountField!=null){
	paramsMap.put("centrifugalAmountField",centrifugalAmountField);
	}

	result = process(paramsMap);

	response.setData(result);
	return response;
}
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}
@SuppressWarnings("finally")
private Map<String, Object> process(Map<String, Object> sameSideReversalArgs) {

	Connection connection = null;
	Map<String, Object> result = null;
	try {
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();

		String userId = (String) sameSideReversalArgs.get(USER_ID);
		String dsName = (String) sameSideReversalArgs.get(DS_NAME);
		sameSideReversalArgs.put(PERSIST_CLASS, SAME_SIDE_REVERSAL_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", sameSideReversalArgs);
	//	String moduleName=(String) sameSideReversalArgs.get(MODULE);
		String integrationName=(String)sameSideReversalArgs.get(INTEGRATION_NAME);
		String reconTableName=(String)sameSideReversalArgs.get("reconTableName");
		
		

		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;

			String businessArea = (String) sameSideReversalArgs.get(BUSINES_AREA);
			String reconName = (String) sameSideReversalArgs.get(RECON_NAME);
			String comments = (String) sameSideReversalArgs.get(COMMENTS);
			String moduleName=(String)sameSideReversalArgs.get(MODULE);
			String activity_name=(String)sameSideReversalArgs.get("activity_name");
			String activity_type=(String)sameSideReversalArgs.get("activity_type");
			
			//String dsName=(String)sameSideReversalArgs.get("dsName");
			String reconDataSorce=(String)sameSideReversalArgs.get("reconDataSorce");
			userAdminManager.createActivity(connection, user, businessArea, reconName, activity_name,
					activity_type, activityDataMap, activityStatus, comments);

			updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
			/*LoadRegulator loadRegulator=new LoadRegulator();
			InsertRegulator insertRegulator=new InsertRegulator();*/
			List<Map<String, Object>> records = (List<Map<String, Object>>) sameSideReversalArgs.get(SELECTED_RECORDS);
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement reconAuditInsertPstmt=null;
			PreparedStatement	stagingUpdatePstmt=null;
			PreparedStatement	reconUpdatePstmt=null;
			String tableNameStg=integrationName+"_STG";
			//String tableNameAudit=integrationName+"_STG_AUDIT";
			//String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			//Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			 /*for(Map<String,Object> selectedrecord:records){
				 selectAuditStmt=connection.prepareStatement(auditSelectQry);
		        	List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		        	
		        	stagingInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
		        	Map paramValueMap=new HashMap();
		        	for(Map rec : auditData){
		        		
		        		paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(stagingInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
		        		
		        	}
				 
		        	int version=Integer.parseInt(selectedrecord.get("VERSION").toString());
					++version;
					String reqComments=user.getUserId()+":"+(String) sameSideReversalArgs.get(COMMENTS);
					
                    long sid=(long) selectedrecord.get("SID");
					
					stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='Y',ACTIVITY_COMMENTS=?,VERSION="+version+" WHERE SID="+sid);
					stagingUpdatePstmt.setObject(1, reqComments);
					
					int rows=	stagingUpdatePstmt.executeUpdate();				 
				 
			 }*/
			
			for(Map<String,Object> selectedrecord:records){

				long sid=(long) selectedrecord.get("SID");
				
				//STG logic
				auditInsertPstmt=connection.prepareStatement("INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?");
				auditInsertPstmt.setObject(1,sid);
				auditInsertPstmt.executeUpdate();
				System.out.println("Record inserted from STG to Audit");
				
	        	int version=Integer.parseInt(selectedrecord.get("VERSION").toString());
				++version;
				String reqComments=user.getUserId()+":"+(String) sameSideReversalArgs.get(COMMENTS);
				
				stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? WHERE SID=?");
				stagingUpdatePstmt.setObject(1, "Y");
				stagingUpdatePstmt.setObject(2, reqComments);
				stagingUpdatePstmt.setObject(3, version);
				stagingUpdatePstmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
				stagingUpdatePstmt.setObject(5, sid);
				stagingUpdatePstmt.executeUpdate();
				
				
				//Exception logic
				if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
					
					/*reconAuditInsertPstmt=connection.prepareStatement("INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?");
					reconAuditInsertPstmt.setObject(1,sid);
					reconAuditInsertPstmt.executeUpdate();
					System.out.println("Record inserted from Recon to Audit");*/
					
					reconUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? WHERE SID=?");
					reconUpdatePstmt.setObject(1, "Y");
					reconUpdatePstmt.setObject(2, reqComments);
					reconUpdatePstmt.setObject(3, version);
					reconUpdatePstmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					reconUpdatePstmt.setObject(5, sid);
					reconUpdatePstmt.executeUpdate();
					
				}
				 
			 }
			
			 updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
		}
		else {
			result = persist(activityDataMap, APPROVED, connection);

		}

	
}
	catch (Exception e){
		e.printStackTrace();
		updateResultStatus(result, FAILED,OPERATION_FAILED );
		
	}
	finally {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	
}



public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {
	// TODO Auto-generated method stub
	


	Map<String, Object> result = new HashMap<String, Object>();
	LoadRegulator loadRegulator=new LoadRegulator();
	InsertRegulator insertRegulator=new InsertRegulator();
	try {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
        String userid=(String) activityDataMap.get("userId");
		String comment=(String) activityDataMap.get("comment");
		Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
		String dsName = (String) activityRecordsMap.get(DS_NAME);
		
		String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		
		String reconDataSource=(String) activityRecordsMap.get("reconDataSorce");
		String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
		//Map<String,Object> DEBITCREDITDETAILS=(Map<String, Object>) activityRecordsMap.get(DEBIT_CREDIT_DETAILS);
		connection = DbUtil.getConnection();
		System.out.println(status);
		String activity_name=(String)activityRecordsMap.get("activity_name");
		String businessArea = (String) activityRecordsMap.get(BUSINES_AREA);
		String reconName = (String) activityRecordsMap.get(RECON_NAME);
		String comments = (String) activityRecordsMap.get(COMMENTS);
		
		String tableNameStg=integrationName+"_STG";
		String tableNameAudit=integrationName+"_STG_AUDIT";
		//if(integrationName.equalsIgnoreCase("IMLO_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMLO_PAYMENTS_CREDIT")){
			
			/*if(integrationName.equalsIgnoreCase("IMAL_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMAL_PAYMENTS_CREDIT")){
			tableNameStg="IMLO_PAYMENTS_STG";
			tableNameAudit="IMLO_PAYMENTS_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("PO") || integrationName.equalsIgnoreCase("TRADE")){
			tableNameStg="RETAIL_ASSET_STG";
			tableNameAudit="RETAIL_ASSET_STG_AUDIT";
		}
		else if(integrationName.equalsIgnoreCase("AUTH_ISS")){
			tableNameStg="AUTH_ISSUER_STG";
			tableNameAudit="AUTH_ISSUER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("AUTH_ACQ")){
			tableNameStg="AUTH_ACQUIRER_STG";
			tableNameAudit="AUTH_ACQUIRER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("MAST")){
			tableNameStg="MASTER_STG";
			tableNameAudit="MASTER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("CTL")){
			if(reconTableName.contains("ATM")){
			tableNameStg="CISO_STG";
			tableNameAudit="CISO_STG_AUDIT";
			}else if(reconTableName.contains("POS")){
				if(reconTableName.contains("ISSUER")){
					tableNameStg="CISO_STG";
					tableNameAudit="CISO_STG_AUDIT";
				}else{
					tableNameStg="MISO_STG";
					tableNameAudit="MISO_STG_AUDIT";
				}
			}
		}else if(integrationName.equalsIgnoreCase("VISA")){
			if(reconTableName.contains("ISSUER")){
			tableNameStg="VISA_ISSUER_STG";
			tableNameAudit="VISA_ISSUER_STG_AUDIT";
			}
		}*/
		if (APPROVED.equalsIgnoreCase(status)) {
			String stagingTableIdSeqName = tableNameStg + "MM_ID_SEQ";
		    String stagingTableIdGenQry = "SELECT NEXT VALUE FOR " + stagingTableIdSeqName + " as sno";
				String idseqname=tableNameStg + "MM_ID_SEQ";
				
			 PreparedStatement stagingTableIdGenPstmt = connection.prepareStatement(stagingTableIdGenQry);
			 String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
			 String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
				
			 PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
			 
			 
			 String reconTableReconIdSeqName = reconTableName + "MM_RECON_ID_SEQ";
			 String reconTableReconIdGenQry = "SELECT NEXT VALUE FOR " + reconTableReconIdSeqName + " as sno";
				
			 PreparedStatement reconTableReconIdGenPstmt = connection.prepareStatement(reconTableReconIdGenQry);
			 /*String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			 Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			 String debitOrCredit=null;*/
			 PreparedStatement updateStmt=null;
			 PreparedStatement selectAuditStmt=null;
			 PreparedStatement auditInsertPstmt=null;
			 PreparedStatement	stagingInsertPstmt=null;
			 PreparedStatement	stagingUpdatePstmt=null;
			 PreparedStatement reconDataSelectFromStagingPstmt=null;
			 PreparedStatement reconToAuditPstmt=null;
			 PreparedStatement reconDataInsertPstmt=null;
			 PreparedStatement stgUpdatePstmt=null;
			try{
			     long reconId=PersistanceUtil.generateSeqNo(connection, reconTableReconIdGenPstmt, reconTableReconIdSeqName);
			     reconId=reconId+System.currentTimeMillis();
			for(Map<String,Object> selectedrecord:selectedRecords){
				
				/*selectAuditStmt=connection.prepareStatement(auditSelectQry);
				
				List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
				
				Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
				  int version=0;
				for(Map rec : auditData){
					Map paramValueMap=new HashMap();
					paramValueMap.put("PARAM_VALUE_MAP", rec);
					version=Integer.parseInt(rec.get("VERSION").toString());
					insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
				}*/
				
				String approveComments=userid+" : "+comment;
				int version=Integer.parseInt(selectedrecord.get("VERSION").toString());
				version = version+2;
				
				long sid=(long) selectedrecord.get("SID");
				
				//STG logic
				String StgAuditInsert="INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?";
				auditInsertPstmt=connection.prepareStatement(StgAuditInsert);
				auditInsertPstmt.setObject(1,sid);
				auditInsertPstmt.executeUpdate();
				System.out.println("record inserted from STG to Audit");
				
				stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,RECON_STATUS=?,RECON_ID=?,UPDATED_ON=?,VERSION=? WHERE SID=?");
				stagingUpdatePstmt.setObject(1, "N");
				stagingUpdatePstmt.setObject(2, approveComments);
				stagingUpdatePstmt.setObject(3, "MM");
				stagingUpdatePstmt.setObject(4, reconId);
				stagingUpdatePstmt.setObject(5, new Timestamp(Calendar.getInstance().getTimeInMillis()));
				stagingUpdatePstmt.setObject(6, version);
				stagingUpdatePstmt.setObject(7, sid);
				int rows=stagingUpdatePstmt.executeUpdate();
				
				if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
					
					/*String reconTableToAudit="INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?";
					reconToAuditPstmt=connection.prepareStatement(reconTableToAudit);
					reconToAuditPstmt.setObject(1,sid);
					reconToAuditPstmt.executeUpdate();
					System.out.println("record inserted from Recon to Audit");*/
					
					//"UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='N',ACTIVITY_COMMENTS=?,RECON_STATUS='MM',RECON_ID=?,UPDATED_ON=?,VERSION="+(Long.valueOf(selectedrecord.get("VERSION")+"")+2)+" WHERE SID="+sid)"
					String updateQuery="update "+reconTableName+" set MATCH_TYPE=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,UPDATED_ON=?,VERSION=?,RECON_ID=?,STATUS=? where sid =?";
					stgUpdatePstmt=connection.prepareStatement(updateQuery);
					stgUpdatePstmt.setObject(1,"MM");
					stgUpdatePstmt.setObject(2,"N");
					stgUpdatePstmt.setObject(3,approveComments);
					stgUpdatePstmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					stgUpdatePstmt.setObject(5,version);
					stgUpdatePstmt.setObject(6,reconId);
					stgUpdatePstmt.setObject(7,"SAME SIDE REVERSAL");
					stgUpdatePstmt.setObject(8,sid);
					stgUpdatePstmt.executeUpdate();
					System.out.println("Recon updated");
					
				}
				else
				{
					String reocnside=integrationName;
					String queryName=reconTableName+"_"+tableNameStg;
					
					Query query = queries.getQueryConf(queryName);
					reconDataSelectFromStagingPstmt=connection.prepareStatement(query.getQueryString());
					reconDataSelectFromStagingPstmt.setObject(1, sid);
					ResultSet unreconRs=reconDataSelectFromStagingPstmt.executeQuery();
					ResultSetMetaData unreconRsm= unreconRs.getMetaData();
					int columnCount=unreconRsm.getColumnCount();
					Map<String,Object> reconDataMap=new HashMap<String,Object>();
					while(unreconRs.next()){
						
						for(int i=1;i<=columnCount;i++){
							System.out.println(unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
							reconDataMap.put(unreconRsm.getColumnName(i), unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
						}
					}
			
					long seqId=PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName);
					seqId=seqId+System.currentTimeMillis();
					reconDataMap.put("ID", seqId);
					reconDataMap.put("RECON_ID", reconId);
					reconDataMap.put("UPDATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
					reconDataMap.put("MATCH_TYPE",  "MM");
					reconDataMap.put("USER_ID",  userid);
					reconDataMap.put("WORKFLOW_STATUS",  "N");
					reconDataMap.put("ACTIVE_INDEX",  "Y");
					reconDataMap.put("SID", sid );
					reconDataMap.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
					
					reconDataMap.put("VERSION",  1);
					reconDataMap.put("COMMENTS",  "AMOUNTS MATCHED");
					reconDataMap.put("STATUS",  "SAME SIDE REVERSAL");
					reconDataMap.put("SUPPORTING_DOC_ID", "-1");
					reconDataMap.put("ACTIVITY_ID", "-1");
					reconDataMap.put("ACTIVITY_STATUS", "APPROVED");
					reconDataMap.put("COMMENTS",  activity_name);
					//reconDataMap.put("ACTIVITY_COMMENTS",  approveComments);
				    Query reconQuery=	OperationsUtil.getInsertQueryConf(reconTableName, connection);
				    reconDataInsertPstmt=connection.prepareStatement(reconQuery.getQueryString());
				    
				    Map<String, Object> paramValueMap=new HashMap<String, Object>();
					paramValueMap.put("PARAM_VALUE_MAP", reconDataMap);
				 
				    insertRegulator.insert(reconDataInsertPstmt, paramValueMap, reconQuery.getQueryParam());
			    
				}
			  
			}
			
			}catch(Exception e){
				e.printStackTrace();
			}finally{
	
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closePreparedStatement(stagingInsertPstmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closePreparedStatement(reconDataSelectFromStagingPstmt);
				DbUtil.closePreparedStatement(reconDataInsertPstmt);
				DbUtil.closePreparedStatement(stgUpdatePstmt);
				DbUtil.closePreparedStatement(reconToAuditPstmt);
			}
			
			
		} else if (REJECTED.equalsIgnoreCase(status)) {
		
			/*String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);*/
			
			PreparedStatement updateStmt=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement stagingUpdatePstmt =null;
			PreparedStatement reconAuditInsertPstmt =null;
			PreparedStatement reconUpdatePstmt =null;
			
			try{
				for(Map<String,Object> selectedrecord:selectedRecords){
					
					/*selectAuditStmt=connection.prepareStatement(auditSelectQry);
					
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					
					for(Map rec : auditData){
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}*/
					
					String approveComments=userid+" : "+comment;
					long sid=(long) selectedrecord.get("SID");
					int version=Integer.parseInt(selectedrecord.get("VERSION").toString());
					version = version+2;
					
					//STG logic
					auditInsertPstmt=connection.prepareStatement("INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?");
					auditInsertPstmt.setObject(1,sid);
					auditInsertPstmt.executeUpdate();
					System.out.println("Same Side Reversal : Record inserted from STG to Audit");
					
					stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? WHERE SID=?");
					stagingUpdatePstmt.setObject(1, "N");
					stagingUpdatePstmt.setObject(2, approveComments);
					stagingUpdatePstmt.setObject(3, version);
					stagingUpdatePstmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
					stagingUpdatePstmt.setObject(5, sid);
					stagingUpdatePstmt.executeUpdate();
					System.out.println("Same Side Reversal : STG updated");
					
					
					//Exception logic
					if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
						
						/*reconAuditInsertPstmt=connection.prepareStatement("INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?");
						reconAuditInsertPstmt.setObject(1,sid);
						reconAuditInsertPstmt.executeUpdate();
						System.out.println("Same Side Reversal : Record inserted from Recon to Audit");*/
						
						reconUpdatePstmt=connection.prepareStatement("UPDATE "+reconTableName+" SET WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? WHERE SID=?");
						reconUpdatePstmt.setObject(1, "N");
						reconUpdatePstmt.setObject(2, approveComments);
						reconUpdatePstmt.setObject(3, version);
						reconUpdatePstmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
						reconUpdatePstmt.setObject(5, sid);
						reconUpdatePstmt.executeUpdate();
						System.out.println("Same Side Reversal : Recon updated");
						
					}
					
					
					
				}
			}catch(Exception e){
				e.printStackTrace();
			}
			finally{
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
			}
			
		} else if("PENDING".equalsIgnoreCase(status)){
			
		}

	} catch (Exception e) {
		e.printStackTrace();
	}
	
	return result;

	
}


}

