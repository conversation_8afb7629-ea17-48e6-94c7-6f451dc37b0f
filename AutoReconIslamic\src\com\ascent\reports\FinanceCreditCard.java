package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class FinanceCreditCard {
	
	private static Logger logger = LogManager.getLogger(FinanceCreditCard.class.getName());
	
	private static final String CREDIT_INTERNAL_RECONCILED_RECON = "CREDIT_INTERNAL_RECONCILED_RECON";
	private static final String CREDIT_EXTERNAL_RECONCILED_RECON = "CREDIT_EXTERNAL_RECONCILED_RECON";
	private static final String CREDIT_INTERNAL_UNRECONCILED_RECON = "CREDIT_INTERNAL_UNRECONCILED_RECON";
	private static final String CREDIT_EXTERNAL_UNRECONCILED_RECON = "CREDIT_EXTERNAL_UNRECONCILED_RECON";
	private static final String CREDIT_SUPRESS_INTERNAL_RECON = "CREDIT_SUPRESS_INTERNAL_RECON";
	private static final String CREDIT_SUPRESS_EXTERNAL_RECON = "CREDIT_SUPRESS_EXTERNAL_RECON";
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> CreditcardInternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("FORACID", rset.getString(2));
				map.put("CUST ID", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("PART TRAN TYPE", rset.getString(6));
				map.put("TRAN AMT", rset.getString(7));
				map.put("CARD NUM", rset.getString(8));
				map.put("TRAN PARTICULAR", rset.getString(9));
				map.put("TRAN NOTES", rset.getString(10));
				map.put("TXN CAT", rset.getString(11));
				map.put("TXN ID", rset.getString(12));
				map.put("PSTD FLG", rset.getString(13));
				map.put("PSTD DATE", rset.getString(14));
				map.put("RECON STATUS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("RECON ID", rset.getString(17));
				map.put("ACTIVITY ID", rset.getString(18));
				map.put("WORKFLOW STATUS", rset.getString(19));
				map.put("RECON REMARKS", rset.getString(20));
				map.put("CREATIONTIME", rset.getString(21));
				map.put("UPDATETIME", rset.getString(22));
				map.put("UPDATEDBY", rset.getString(23));
				map.put("OPSOURCEID", rset.getString(24));
				map.put("OPIPADDRESS", rset.getString(25));
				map.put("SOURCE NAME", rset.getString(26));
				map.put("SOURCE TYPE", rset.getString(27));
				map.put("TTUM STATU", rset.getString(28));
				map.put("RECON DATE", rset.getString(29));
				map.put("MAIN REV IND", rset.getString(30));
				map.put("ACTIVE INDEX", rset.getString(31));
				map.put("UPDATED ON", rset.getString(32));
				map.put("OPERATION", rset.getString(33));
				map.put("ACTIVITY COMMENTS", rset.getString(34));

				list.add(map);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> CreditcardExternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("DB TS", rset.getString(2));
				map.put("TXN ID", rset.getString(3));
				map.put("TXN DATE", rset.getString(4));
				map.put("TXN SRL NO", rset.getString(5));
				map.put("TXN AMT", rset.getString(6));
				map.put("ORG AMT", rset.getString(7));
				map.put("ORG CUR", rset.getString(8));
				map.put("TXN TYP", rset.getString(9));
				map.put("TXN CAT", rset.getString(10));
				map.put("TXN ORGN", rset.getString(11));
				map.put("TXN DESC", rset.getString(12));
				map.put("BANK ID", rset.getString(13));
				map.put("BRANCH ID", rset.getString(14));
				map.put("AC ID", rset.getString(15));
				map.put("PRIMARYACID", rset.getString(16));
				map.put("CARD NUMBER", rset.getString(17));
				map.put("PST DATE", rset.getString(18));
				map.put("FREE AMOUNT", rset.getString(19));
				map.put("VALUE DATE", rset.getString(20));
				map.put("DEL FLG", rset.getString(1));
				map.put("R MOD ID", rset.getString(22));
				map.put("R MOD TIME", rset.getString(23));
				map.put("R CRE ID", rset.getString(24));
				map.put("R CRE TIME", rset.getString(25));
				map.put("RECON STATUS", rset.getString(26));
				map.put("VERSION", rset.getString(27));
				map.put("RECON ID", rset.getString(28));
				map.put("ACTIVITY ID", rset.getString(29));
				map.put("WORKFLOW STATUSE", rset.getString(30));
				map.put("RECON REMARKS", rset.getString(31));
				map.put("CREATIONTIME", rset.getString(32));
				map.put("UPDATETIME", rset.getString(33));
				map.put("UPDATEDBY", rset.getString(34));
				map.put("OPSOURCEID", rset.getString(35));
				map.put("OPIPADDRESS", rset.getString(36));
				map.put("SOURCE NAME", rset.getString(37));
				map.put("SOURCE TYPE", rset.getString(38));
				map.put("TTUM STATUS", rset.getString(39));
				map.put("RECON DATE", rset.getString(40));
				map.put("ACTIVE INDEXS", rset.getString(41));
				map.put("MAIN REV IND", rset.getString(42));
				map.put("UPDATED ON", rset.getString(43));
				map.put("OPERATION", rset.getString(44));
				map.put("ACTIVITY COMMENTS", rset.getString(45));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> CreditcardInternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("FORACID", rset.getString(2));
				map.put("CUST ID", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("PART TRAN TYPE", rset.getString(6));
				map.put("TRAN AMT", rset.getString(7));
				map.put("CARD NUM", rset.getString(8));
				map.put("TRAN PARTICULAR", rset.getString(9));
				map.put("TRAN NOTES", rset.getString(10));
				map.put("'TXN CAT", rset.getString(11));
				map.put("TXN ID", rset.getString(12));
				map.put("PSTD FLG", rset.getString(13));
				map.put("PSTD DATE", rset.getString(14));
				map.put("RECON STATUS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("RECON ID", rset.getString(17));
				map.put("ACTIVITY ID", rset.getString(18));
				map.put("WORKFLOW STATUS", rset.getString(19));
				map.put("RECON REMARKS", rset.getString(20));
				map.put("CREATIONTIME", rset.getString(21));
				map.put("UPDATETIME", rset.getString(22));
				map.put("UPDATEDBY", rset.getString(23));
				map.put("OPSOURCEID", rset.getString(24));
				map.put("OPIPADDRESS", rset.getString(25));
				map.put("SOURCE NAME", rset.getString(26));
				map.put("SOURCE TYPE", rset.getString(27));
				map.put("TTUM STATU", rset.getString(28));
				map.put("RECON DATE", rset.getString(29));
				map.put("MAIN REV IND", rset.getString(30));
				map.put("ACTIVE INDEX", rset.getString(31));
				map.put("UPDATED ON", rset.getString(32));
				map.put("OPERATION", rset.getString(33));
				map.put("ACTIVITY COMMENTS", rset.getString(34));
				map.put("AGE", rset.getString(35));

				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>>CreditcardExternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("DB TS", rset.getString(2));
				map.put("TXN ID", rset.getString(3));
				map.put("TXN DATE", rset.getString(4));
				map.put("TXN SRL NO", rset.getString(5));
				map.put("TXN AMT", rset.getString(6));
				map.put("ORG AMT", rset.getString(7));
				map.put("ORG CUR", rset.getString(8));
				map.put("TXN TYP", rset.getString(9));
				map.put("TXN CAT", rset.getString(10));
				map.put("TXN ORGN", rset.getString(11));
				map.put("TXN DESC", rset.getString(12));
				map.put("BANK ID", rset.getString(13));
				map.put("BRANCH ID", rset.getString(14));
				map.put("AC ID", rset.getString(15));
				map.put("PRIMARYACID", rset.getString(16));
				map.put("CARD NUMBER", rset.getString(17));
				map.put("PST DATE", rset.getString(18));
				map.put("FREE AMOUNT", rset.getString(19));
				map.put("VALUE DATE", rset.getString(20));
				map.put("DEL FLG", rset.getString(1));
				map.put("R MOD ID", rset.getString(22));
				map.put("R MOD TIME", rset.getString(23));
				map.put("R CRE ID", rset.getString(24));
				map.put("R CRE TIME", rset.getString(25));
				if(rset.getString(26)==null)
					map.put("RECON STATUS", "AU");
				else
				map.put("RECON STATUS", rset.getString(26));
				map.put("VERSION", rset.getString(27));
				map.put("RECON ID", rset.getString(28));
				map.put("ACTIVITY ID", rset.getString(29));
				map.put("WORKFLOW STATUSE", rset.getString(30));
				map.put("RECON REMARKS", rset.getString(31));
				map.put("CREATIONTIME", rset.getString(32));
				map.put("UPDATETIME", rset.getString(33));
				map.put("UPDATEDBY", rset.getString(34));
				map.put("OPSOURCEID", rset.getString(35));
				map.put("OPIPADDRESS", rset.getString(36));
				map.put("SOURCE NAME", rset.getString(37));
				map.put("SOURCE TYPE", rset.getString(38));
				map.put("TTUM STATUS", rset.getString(39));
				map.put("RECON DATE", rset.getString(40));
				map.put("ACTIVE INDEXS", rset.getString(41));
				map.put("MAIN REV IND", rset.getString(42));
				map.put("UPDATED ON", rset.getString(43));
				map.put("OPERATION", rset.getString(44));
				map.put("ACTIVITY COMMENTS", rset.getString(45));
				map.put("AGE", rset.getString(46));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> CreditcardSupressInternal(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_SUPRESS_INTERNAL_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("FORACID", rset.getString(2));
				map.put("CUST ID", rset.getString(3));
				map.put("TRAN DATE", rset.getString(4));
				map.put("VALUE DATE", rset.getString(5));
				map.put("PART TRAN TYPE", rset.getString(6));
				map.put("TRAN AMT", rset.getString(7));
				map.put("CARD NUM", rset.getString(8));
				map.put("TRAN PARTICULAR", rset.getString(9));
				map.put("TRAN NOTES", rset.getString(10));
				map.put("TXN CAT", rset.getString(11));
				map.put("TXN ID", rset.getString(12));
				map.put("PSTD FLG", rset.getString(13));
				map.put("PSTD DATE", rset.getString(14));
				map.put("RECON STATUS", rset.getString(15));
				map.put("VERSION", rset.getString(16));
				map.put("RECON ID", rset.getString(17));
				map.put("ACTIVITY ID", rset.getString(18));
				map.put("WORKFLOW STATUS", rset.getString(19));
				map.put("RECON REMARKS", rset.getString(20));
				map.put("CREATIONTIME", rset.getString(21));
				map.put("UPDATETIME", rset.getString(22));
				map.put("UPDATEDBY", rset.getString(23));
				map.put("OPSOURCEID", rset.getString(24));
				map.put("OPIPADDRESS", rset.getString(25));
				map.put("SOURCE NAME", rset.getString(26));
				map.put("SOURCE TYPE", rset.getString(27));
				map.put("TTUM STATU", rset.getString(28));
				map.put("RECON DATE", rset.getString(29));
				map.put("MAIN REV IND", rset.getString(30));
				map.put("ACTIVE INDEX", rset.getString(31));
				map.put("UPDATED ON", rset.getString(32));
				map.put("OPERATION", rset.getString(33));
				map.put("ACTIVITY COMMENTS", rset.getString(34));
				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	public List<Map<String, Object>> CreditcardSupressExternal(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(CREDIT_SUPRESS_EXTERNAL_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("SID", rset.getString(1));
				map.put("DB TS", rset.getString(2));
				map.put("TXN ID", rset.getString(3));
				map.put("TXN DATE", rset.getString(4));
				map.put("TXN SRL NO", rset.getString(5));
				map.put("TXN AMT", rset.getString(6));
				map.put("ORG AMT", rset.getString(7));
				map.put("ORG CUR", rset.getString(8));
				map.put("TXN TYP", rset.getString(9));
				map.put("TXN CAT", rset.getString(10));
				map.put("TXN ORGN", rset.getString(11));
				map.put("TXN DESC", rset.getString(12));
				map.put("BANK ID", rset.getString(13));
				map.put("BRANCH ID", rset.getString(14));
				map.put("AC ID", rset.getString(15));
				map.put("PRIMARYACID", rset.getString(16));
				map.put("CARD NUMBER", rset.getString(17));
				map.put("PST DATE", rset.getString(18));
				map.put("FREE AMOUNT", rset.getString(19));
				map.put("VALUE DATE", rset.getString(20));
				map.put("DEL FLG", rset.getString(1));
				map.put("R MOD ID", rset.getString(22));
				map.put("R MOD TIME", rset.getString(23));
				map.put("R CRE ID", rset.getString(24));
				map.put("R CRE TIME", rset.getString(25));
				map.put("RECON STATUS", rset.getString(26));
				map.put("VERSION", rset.getString(27));
				map.put("RECON ID", rset.getString(28));
				map.put("ACTIVITY ID", rset.getString(29));
				map.put("WORKFLOW STATUSE", rset.getString(30));
				map.put("RECON REMARKS", rset.getString(31));
				map.put("CREATIONTIME", rset.getString(32));
				map.put("UPDATETIME", rset.getString(33));
				map.put("UPDATEDBY", rset.getString(34));
				map.put("OPSOURCEID", rset.getString(35));
				map.put("OPIPADDRESS", rset.getString(36));
				map.put("SOURCE NAME", rset.getString(37));
				map.put("SOURCE TYPE", rset.getString(38));
				map.put("TTUM STATUS", rset.getString(39));
				map.put("RECON DATE", rset.getString(40));
				map.put("ACTIVE INDEXS", rset.getString(41));
				map.put("MAIN REV IND", rset.getString(42));
				map.put("UPDATED ON", rset.getString(43));
				map.put("OPERATION", rset.getString(44));
				map.put("ACTIVITY COMMENTS", rset.getString(45));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static void main(String[] args) {
		FinanceCreditCard c = new FinanceCreditCard();
		c.CreditcardInternalUnReconsiledMethod("2018-01-01", "2018-10-01");
		c.CreditcardInternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.CreditcardExternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.CreditcardExternalUnReconsiledMethod("2018-01-01", "2018-10-01");
		c.CreditcardSupressInternal("2018-01-01", "2018-10-01");
		c.CreditcardSupressExternal("2018-01-01", "2018-10-01");
	}

	

}
