package com.ascent.banknizwa.source.ej;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.enrichment.AscentEnrichmentPlugin;
import com.ascent.integration.persistance.AscentPersistanceIntf;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.integration.validation.AscentValidationPlugin;
import com.ascent.util.AscentAutoReconConstants;
import com.onus.atm.etl.OnUsAtmEtl;

public class EJPersistancePlugin implements AscentPersistanceIntf, AscentAutoReconConstants {

	private static Logger logger = LogManager.getLogger(EJPersistancePlugin.class.getName());

	@Override
	public Map<String, Object> persist(Integration integration, Queries queries, Connection connection,
			List<Map<String, Object>> txnList, Boolean exFound) throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();

		AscentEnrichmentPlugin ascentEnrichmentPlugin = null;
		AscentValidationPlugin ascentPreEnrichValidationPlugin = null;
		AscentValidationPlugin ascentPostEnrichValidationPlugin = null;

		PreparedStatement stagingInsertPstmt = null;
		PreparedStatement stagingExInsertPstmt = null;
		PreparedStatement seqNoPstmt = null;
		PreparedStatement stagingExUpdatePstmt = null;
		Query stgInsertQry = null;
		Query stgExInsertQry = null;
		int stagingCount = 0;
		int stagingExCount = 0;
		int ignoreCount = 0;
		Map<String, Object> tempTxn = new HashMap<String, Object>();
		try {

			String enrichClass = integration.getEnrichmentPlugin();
			String preValidationClass = integration.getPreEnrichValidationPlugin();
			String postValidationClass = integration.getPostEnrichValidationPlugin();

			ascentEnrichmentPlugin = (AscentEnrichmentPlugin) ((Class.forName(enrichClass)).newInstance());
			ascentPreEnrichValidationPlugin = (AscentValidationPlugin) ((Class.forName(preValidationClass))	.newInstance());
			ascentPostEnrichValidationPlugin = (AscentValidationPlugin) ((Class.forName(postValidationClass))	.newInstance());
//comment by shivam 06-06-2017
			String seqName = integration.getSequenceName();
			String seqNumQry = "SELECT NEXT VALUE FOR " + seqName + " as sno";
			stgInsertQry = queries.getQueryConf(integration.getStagingInsertQueryName());
			stgExInsertQry = queries.getQueryConf(integration.getStagingExInsertQueryName());
			Query stgExUpdateQry = queries.getQueryConf(integration.getStagingExUpdateQueryName());

			stagingInsertPstmt = connection.prepareStatement(stgInsertQry.getQueryString());
			stagingExInsertPstmt = connection.prepareStatement(stgExInsertQry.getQueryString());
			seqNoPstmt = connection.prepareStatement(seqNumQry);

			stagingExUpdatePstmt = connection.prepareStatement(stgExUpdateQry.getQueryString());

			for (Map<String, Object> txn : txnList) {
				OnUsAtmEtl.counter++;
				if (exFound) {

					Long exid = (Long) txn.get("SID");
					
					txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
					txn.put("CREATED_ON", new Timestamp(((java.util.Date)txn.get("CREATED_ON")).getTime()));
					txn.put("COMMENTS", "");
					txn.put("WORKFLOW_STATUS", "N");
					txn.put("OPERATION", "Re-Process");
					txn.put("TRA_DATE", new java.sql.Date(((java.util.Date)					txn.get("TRA_DATE")).getTime()));
					txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));

					tempTxn.putAll(txn);
					boolean flag = ascentPreEnrichValidationPlugin.validate(txn);

					if (flag) {
						//boolean isIgnored = canIgnoreRec(txn);
						/*if (isIgnored) {
							continue;
						}*///comment by shivam08-06-2017
						ascentEnrichmentPlugin.enrich(txn);
						flag = ascentPostEnrichValidationPlugin.validate(txn);

						if (flag) {
							//tempTxn.put("TRA_DATE", new java.sql.Date(((java.util.Date)tempTxn.get("TRA_DATE")).getTime()));
							Long version = Long.valueOf(tempTxn.get("VERSION")+"");
							txn.put("VERSION", ++version);
							try {
								PersistanceUtil.persistTxn(stagingInsertPstmt, stgInsertQry, txn);
							} catch (Exception e) {
								System.out.println(e.getMessage());

								tempTxn.put("COMMENTS", "Recard Already Exist");
								PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
							}
							PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
							exFound = false;

						} else {
							long version = (Long) tempTxn.get("VERSION");
							tempTxn.put("VERSION", ++version);
							tempTxn.put("COMMENTS", txn.get("COMMENTS"));
							tempTxn.put("TRA_DATE", new java.sql.Date(((java.util.Date)tempTxn.get("TRA_DATE")).getTime()));
							PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
							PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
						}

					} else {
						int version = (int) tempTxn.get("VERSION");
						tempTxn.put("VERSION", ++version);
						tempTxn.put("COMMENTS", txn.get("COMMENTS"));
						tempTxn.put("TRA_DATE", new java.sql.Date(((java.util.Date)tempTxn.get("TRA_DATE")).getTime()));
						PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
						PersistanceUtil.updateExTxn(stagingExUpdatePstmt, stgExUpdateQry, exid, version);
					}

				} else {
					long uid = PersistanceUtil.generateSeqNo(connection, seqNoPstmt, seqName);
					txn.put("SID", uid);
					txn.put("VERSION", 1);
					txn.put("ACTIVE_INDEX", "Y");
					txn.put("STATUS", "new");
					txn.put("COMMENTS", "Staging");
					txn.put("WORKFLOW_STATUS", "N");
					txn.put("USER_ID", "system");
					txn.put("OPERATION", "ETL");

					txn.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
					txn.put("CREATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				/*	String card_no = (String) txn.get("PAN");
					if (card_no != null && !card_no.isEmpty()) {
						int lenght = card_no.length();
						if (lenght > 11) {
							card_no = card_no.substring(0, 6) + "******" + card_no.substring(lenght - 4, lenght);
							txn.put("PAN", card_no);
						} else {

							// TODO: invvalid card data exception need to
							// capture
							// throw new Exception("invalid card No");
						}

					}*/ //COMMENTS BY SHIVAM 06-06-2017
					tempTxn.putAll(txn);
					///boolean flag = ascentPreEnrichValidationPlugin.validate(txn);//comment by shivam 05-06-2017

				/*	if (flag) {
						boolean isIgnored = canIgnoreRec(txn);
						if (isIgnored) {
							ignoreCount++;
							continue;
						}*/

						//ascentEnrichmentPlugin.enrich(txn);////comment by shivam 05-06-2017

					//	flag = ascentPostEnrichValidationPlugin.validate(txn);//comment by shivam 05-06-2017


					//if (flag) {
							try {
								PersistanceUtil.persistTxn(stagingInsertPstmt, stgInsertQry, txn);
								stagingCount++;
							} catch (Exception e) {
							
						
							try {
								tempTxn.put("COMMENTS", txn.get("COMMENTS"));
								PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, txn);
								stagingExCount++;
							} catch (Exception e1) {
logger.error(e.getMessage(),e);
							}
						}

					//} else {
					/*	try {
							tempTxn.put("COMMENTS", txn.get("COMMENTS"));
							PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, txn);
							stagingExCount++;
						} catch (Exception e) {
							logger.error(e.getMessage(),e);
						}*/
					}

				//}
			
			}
			}catch (Exception e) {
			logger.error(e.getMessage(),e);
			String duplicateMessage = e.getMessage();
			if (duplicateMessage.contains("duplicate key")) {
				tempTxn.put("COMMENTS", "Duplicate Record");
				PersistanceUtil.persistTxn(stagingExInsertPstmt, stgExInsertQry, tempTxn);
			}
			throw e;
		} finally {
			DbUtil.closePreparedStatement(stagingExInsertPstmt);
			DbUtil.closePreparedStatement(stagingInsertPstmt);
			DbUtil.closePreparedStatement(stagingExUpdatePstmt);
			result.put(STAG_CNT, stagingCount);
			result.put(STAG_EX_CNT, stagingExCount);
			result.put("IGN_CNT", ignoreCount);
		}
			
		return result;
	}

	boolean canIgnoreRec(Map<String, Object> txn) {

		boolean flag = false;

		/*String amtTrn = (String) (String) txn.get("AMT_TRAN");
		if (amtTrn != null && !(amtTrn.isEmpty())) {
			Double tempAmt = Double.parseDouble(amtTrn);
			txn.put("AMT_TRAN", amtTrn);
		}
		txn.put("AMOUNT", (String) txn.get("AMT_TRAN"));

		String amount = (String) txn.get("AMOUNT");
		String amtTranStr = (String) txn.get("AMT_TRAN");
		String amtSettStr = (String) txn.get("AMT_SETT");
		String amt_c_hldr_bill = (String) txn.get("AMT_C_HLDR_BILL");
		String curry_tran = (String) txn.get("CURR_CODE_TRAN");
		String currency_sett = (String) txn.get("CURR_CODE_SETT");

		if (currency_sett == null || currency_sett.isEmpty()) {

			txn.put("CURR_CODE_SETT", curry_tran);

		}

		if (amt_c_hldr_bill != null && !amt_c_hldr_bill.isEmpty()) {
			double bil_amt = Double.parseDouble(amt_c_hldr_bill);
			bil_amt = bil_amt / 100;
			txn.put("AMT_C_HLDR_BILL", bil_amt);
		}

		Double amt_tran = 0.00d;
		Double amt_sett = 0.00d;
		try {
			// amount=amtTranStr;
			if (amtTranStr == null || amtTranStr.isEmpty()) {
				if (amount != null && !(amount.trim()).isEmpty()) {
					amt_tran = Double.parseDouble(amount);

				}
			} else {
				amt_tran = Double.parseDouble(amtTranStr);

			}

			if (amtSettStr == null || amtSettStr.isEmpty()) {
				amt_sett = amt_tran;
			} else {
				amt_sett = Double.parseDouble(amtSettStr);

			}
		} catch (Exception e) {
			System.err.println("amount" + amount);
			System.err.println("amtTranStr" + amtTranStr);
			System.err.println("amtSettStr" + amtSettStr);
			e.printStackTrace();
		}
		txn.put("AMT_TRAN", amt_tran);
		txn.put("AMT_SETT", amt_sett);

		String[] allowedProcCode = { "00", "01", "06", "21", "24", "25", "30", "56", "72", "73", "82", "86" };
		List<String> allowedProcCodeList = Arrays.asList(allowedProcCode);
*///comment by shivam 
	/*	String proc_code = (String) txn.get("PROC_CODE");
		String proc_code_2 = null;
		if (proc_code.length() == 6) {
			proc_code_2 = proc_code.substring(0, 2);
		} else if (proc_code.length() == 5) {
			proc_code_2 = "0" + proc_code.substring(0, 1);
		} else {
			proc_code_2 = "00";
		}
		txn.put("PROC_CODE_FIRST_2", proc_code_2);

		String resp_code = (String) txn.get("RESP_CODE");
		if (!allowedProcCodeList.contains(proc_code_2)) {
			flag = true;
		} else if ("01".equalsIgnoreCase(proc_code_2) && "000".equalsIgnoreCase(resp_code) && amt_tran == 0) {
			flag = true;
		} else if ("01".equalsIgnoreCase(proc_code_2)
				&& ("036".equalsIgnoreCase(resp_code) || "037".equalsIgnoreCase(resp_code)) && amt_sett == 0) {
			flag = true;
		}*/ //COMMENT BY SHIVAM 06-06-2017
		return flag;
	}
}
