package com.ascent.recon;

import java.io.IOException;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.log.SysoLogger;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.isomorphic.rpc.RPCManager;
import com.isomorphic.rpc.RPCRequest;

/**
 * Servlet implementation class ReconProcessServlet
 */
@WebServlet("/GenerateExportPdf")
public class GenerateExportPdf extends HttpServlet {
	private static final long serialVersionUID = 1L;
       
    /**
     * @see HttpServlet#HttpServlet()
     */
    public GenerateExportPdf() {
        super();
        // TODO Auto-generated constructor stub
    }

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse response)
	 */
    
    @Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
		
		PrintWriter out =response.getWriter();
		RPCManager rpc = null;
		try {
			rpc = new RPCManager(request, response, out);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}

		 for(Iterator k = rpc.getRequests().iterator(); k.hasNext();) {
		     RPCRequest rpcRequest = (RPCRequest)k.next();
		     Map mainMap = (Map) rpcRequest.getData();
		     System.out.println("mainMap :" +  mainMap);
		    
		 	
		 	List  fieldsList=(List) mainMap.get("fields");
		 	 System.out.println("fieldsList :" +  fieldsList);
		 	 final int tableSize= fieldsList.size();
		 	List  data=(List) mainMap.get("data");
		 	 System.out.println("data :" +  data);
		 	 for(int r=0;r<data.size();r++){
		 		Map m=(Map) data.get(r);
		 	 }
		 	Rectangle pageSize = new Rectangle(5400f, 7000f);
			Document document = new Document(pageSize);
			response.setContentType("application/pdf");
	        try {
	        	response.setContentType("application/pdf");

	            PdfWriter.getInstance(document, response.getOutputStream());
			} catch (DocumentException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
	        document.open();
	       System.out.println(document.isOpen());
		
			PdfPTable table = new PdfPTable(tableSize);
			
			for(int q=0;q<fieldsList.size();q++){
				String f=(String) fieldsList.get(q);
				table.addCell(f);
			}
			System.out.println("table :" +  table);
			table.setHeaderRows(1);
			// table.setWidthPercentage(100);
			PdfPCell[] cells = table.getRow(0).getCells();
			for (int j = 0; j < cells.length; j++) {
				cells[j].setBackgroundColor(BaseColor.GRAY);
			}
			try {
				document.add(table);
			} catch (DocumentException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			/* for(int r=0;r<data.size();r++){
			 		Map m=(Map) data.get(r);
			 		//Long keyValue=(Long) m.get(fieldsList.get(r));
			 		//table.addCell(":" + keyValue);
			 	 }*/
		
			
			
			document.close();

			System.out.println("Done");
	}
	}
}
