package com.ascent.recon;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ascent.boot.etl.EtlMetaInstance;
import com.ascent.util.AscentAutoReconConstants;
import com.isomorphic.rpc.RPCManager;
import com.isomorphic.rpc.RPCRequest;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;

/**
 * Servlet implementation class ExportPdf
 */
@WebServlet("/ExportPdf")
public class ExportPdf extends HttpServlet {
	int i = 0;
	private static final long serialVersionUID = 1L;

	/**
	 * @see HttpServlet#HttpServlet()
	 */
File file=null;
 String extPath="";
	public ExportPdf() {
		EtlMetaInstance etlMetaInstance = EtlMetaInstance.getInstance();
		Properties appProps = etlMetaInstance.getApplicationProperties();
		System.out.println("Properties Loaded");
	 	extPath = (String) appProps.get(AscentAutoReconConstants.EXPORT_PDF_FILE_PATH);
		
		 
	}

	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doGet(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		// TODO Auto-generated method stub
	}

	/**
	 * @see HttpServlet#doPost(HttpServletRequest request, HttpServletResponse
	 *      response)
	 */
	protected void doPost(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		i++;
		PrintWriter out =response.getWriter();
		
		RPCManager rpc = null;
			
		File folderPath= new File(extPath);
		if(!folderPath.exists()){
			
			folderPath.mkdir();
		}
		
		
		try {
			rpc = new RPCManager(request, response, out);
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
	
		/* Properties properties= new Properties();
		 String propertiesFileName="pdfFilePath.properties";
		 propertyReader(propertiesFileName,properties);*/
		 
		// String filePath=properties.getProperty(propertiesFileName);
	
		String FILE_PATH=extPath+"/pdfFile.pdf";
		 
		response.setContentType("application/pdf");
		
		Rectangle pageSize = new Rectangle(5400f, 5400f);
		Document document = new Document(pageSize);
		PdfPTable table = new PdfPTable(31);
		for (Iterator k = rpc.getRequests().iterator(); k.hasNext();) {
			RPCRequest rpcRequest = (RPCRequest) k.next();
			Map mainMap = (Map) rpcRequest.getData();
			Map fields = (Map) mainMap.get("fields");
			List data = (List) mainMap.get("data");
			System.out.println("pdf fecth=================");
			Set set =fields.keySet();
			Iterator iter = set.iterator();
			while (iter.hasNext()) {
			  
				String key=(String) iter.next();
				table.addCell(key);
				
			}
			table.setHeaderRows(1);
			
		PdfPCell[] cells = table.getRow(0).getCells();
			for (int j = 0; j < cells.length; j++) {
				cells[j].setBackgroundColor(BaseColor.GRAY);
			}
			for(int u=0;u<data.size();u++){
				Map objData=(Map) data.get(u);
				Set set1 =fields.keySet();
				Iterator iter1 = set1.iterator();
				while (iter1.hasNext()) {
				  
					String key=(String) iter1.next();
				
					table.addCell("" + objData.get(key));
				}
				
			}
			
			try {
			File	f = new File(FILE_PATH);
			
 
				PdfWriter
						.getInstance(document, new FileOutputStream(f));
			} catch (FileNotFoundException | DocumentException e) {

				e.printStackTrace();
			}
			document.open();
			try {
				document.add(table);
			} catch (DocumentException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			document.close();

			System.out.println("Done");
		}
	

}

	private void propertyReader(String propertiesFileName, Properties properties) {
		try{
			InputStream input=getClass().getClassLoader().getResourceAsStream(propertiesFileName);
			if(input!=null){
				properties.load(input);
			}
			else{
			throw new FileNotFoundException();
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		
		
		
	}
	
	
}
