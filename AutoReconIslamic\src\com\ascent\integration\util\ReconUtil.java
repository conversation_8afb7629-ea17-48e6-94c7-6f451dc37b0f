package com.ascent.integration.util;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.custumize.query.Query;


public class ReconUtil {
	private static Logger logger = LogManager.getLogger(ReconUtil.class.getName());

	public static Long onedayMilliSec = 24 * 60 * 60000l;
	public static final String COMMA = ",";
	public static final String AU = "AU";
	public static final String AM = "AM";
	public static long reconId = -1;
	public static long start = 0l;
	public static long tableId = -1;

	public static List<String> qcbReversalCodes = new ArrayList<String>();
	public static List<String> irisReversalCodes = new ArrayList<String>();
	public static List<String> irisMainCodes = new ArrayList<String>();
	public static List<String> visaRevCodes = new ArrayList<String>();
	public static List<String> visaPosRevCodes = new ArrayList<String>();
	public static List<String> visaAtmRevCodes = new ArrayList<String>();

	public static List<String> visaPosMainCodes = new ArrayList<String>();
	public static List<String> visaAtmMainCodes = new ArrayList<String>();

	static {
		visaPosRevCodes.add("06");
		visaPosRevCodes.add("25");

		visaPosMainCodes.add("05");
		visaPosMainCodes.add("26");

		visaAtmRevCodes.add("27");

		visaAtmMainCodes.add("07");

		irisMainCodes.add("000");
		irisMainCodes.add("976");

		irisReversalCodes.add("036");
		irisReversalCodes.add("037");

		irisReversalCodes.add("967");

		irisReversalCodes.add("994");
		irisReversalCodes.add("995");
		irisReversalCodes.add("996");

		qcbReversalCodes.add("98");
		qcbReversalCodes.add("99");
		visaRevCodes.add("0420");
	}

	public static void updateStatus(Map<String, List<Map<String, Object>>> recGroup, String matchType, String comment,
			String ruleName) {
		if (recGroup != null) {
			for (List<Map<String, Object>> data : recGroup.values()) {
				if (data != null) {
					for (Map<String, Object> rec : data) {

						rec.put("MATCH_TYPE", matchType);
						rec.put("COMMENTS", comment);
						rec.put("MATCH_INDICATOR", "Y");
						rec.put("RULE_NAME", ruleName);

					}
				}
			}
		}

	}

	public static void mainMissingTxnVerification(Map<String, List<Map<String, Object>>> recGroup,
			List<Map<String, Object>> visa, StringBuilder commentsSb, String commentStr, String sourceName) {
		if (visa.size() == 0) {
			if (commentsSb.length() != 0) {
				commentsSb.append(ReconUtil.COMMA);
			}

			commentsSb.append(commentStr);
		} else if (visa.size() > 1) {
			Map<String, Object> rec = visa.get(0);
			List<Map<String, Object>> tempList = new ArrayList<Map<String, Object>>();
			tempList.add(rec);
			recGroup.put(sourceName, tempList);
		}
	}

	public static void validateTime(Map<String, List<Map<String, Object>>> recGroup, Long timeOne, Long timeTwo,
			Map<String, Object> rec, StringBuilder comments, String sourceName, String comment) {

		if (Math.abs(timeOne - timeTwo) > onedayMilliSec) {

			comments.append(COMMA).append(comment);
			recGroup.get(sourceName).remove(rec);

		}
	}

	public static void validateMissingTxn(Map<String, List<Map<String, Object>>> recGroup,
			Map<String, Object> txnToVerify, Map<String, Object> txnToInsert, StringBuilder comments, String sourceName,
			String commentString) {

		if (txnToVerify == null) {
			List<Map<String, Object>> recIns = new ArrayList<Map<String, Object>>();
			if (txnToInsert != null) {
				recIns.add(txnToInsert);
			}
			recGroup.put(sourceName, recIns);

			if (comments.length() != 0) {

				comments.append(COMMA);
			}

			comments.append(commentString);

		}

	}

	public static void insertRecon(Map<String, List<Map<String, Object>>> recGroup, Connection connection,
			PreparedStatement reconIdGenPstmt, String reconIdSeqName, PreparedStatement reconTableIdGenPstmt,
			String reconTableIdSeqName, PreparedStatement reconInsertPstmt, PreparedStatement reconUpdatePstmt,
			Query insertQryConf, Query updateQryConf, Map<String, Map<String, Object>> dynamicPstmtMap,
			String reconTableName, String reconStaticId) {
		try {
			start = System.currentTimeMillis();
			reconId = PersistanceUtil.generateSeqNo(connection, reconIdGenPstmt, reconIdSeqName);
			reconId = reconId + start;
			reconId = Long.valueOf(String.valueOf(reconId) + reconStaticId);

			boolean matchFlag = false;
			Map<String, List<Map<String, Object>>> nullRecGroup = new HashMap<String, List<Map<String, Object>>>();
			Map<String, Object> dummyRec = new HashMap<String, Object>();

			if (recGroup.size() > 0) {

				for (String key : recGroup.keySet()) {
					List<Map<String, Object>> sourceGroup = recGroup.get(key);
					List<Map<String, Object>> nullReconIdGroup = new ArrayList<Map<String, Object>>();

					for (Map<String, Object> rec : sourceGroup) {
						if (dummyRec.isEmpty()) {
							dummyRec = rec;
						}
						if (rec.get("RECON_ID") != null) {

							reconId = (Long) rec.get("RECON_ID");

						} else {

							nullReconIdGroup.add(rec);
						}
					}
					nullRecGroup.put(key, nullReconIdGroup);
				}

				updateReconGroup(dummyRec, reconIdSeqName, reconId, reconInsertPstmt.getConnection(), reconTableName);
			}

			if (nullRecGroup.size() > 0) {
				for (List<Map<String, Object>> sourceGroup : nullRecGroup.values()) {

					for (Map<String, Object> rec : sourceGroup) {

						tableId = PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName);

						rec.put("ACTIVE_INDEX", "Y");
						rec.put("USER_ID", "SYSTEM");
						rec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						rec.put("CREATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						rec.put("SUPPORTING_DOC_ID", -1);
						rec.put("ACTIVITY_STATUS", "APPROVED");
						rec.put("ACTIVITY_ID", -1);

						rec.put("STATUS", "AUTO_APPROVED");

						rec.put("RECON_ID", reconId);
						rec.put("ID", tableId);

						reconInsertPstmt.clearParameters();
						try {

							PersistanceUtil.persistTxn(reconInsertPstmt, insertQryConf, rec);
						} catch (Exception e) {
							System.out.println(e.getMessage() + rec);
							if (e.getMessage() != null && e.getMessage().contains("duplicate key value")) {

								PersistanceUtil.persistTxn(reconUpdatePstmt, updateQryConf, rec);

							}
						}

					}
				}

			}

			if (recGroup.size() > 0) {

				for (String key : recGroup.keySet()) {
					List<Map<String, Object>> sourceGroup = recGroup.get(key);
					List<Map<String, Object>> nullReconIdGroup = new ArrayList<Map<String, Object>>();

					for (Map<String, Object> rec : sourceGroup) {

						if (rec.get("RECON_ID") == null) {

							rec.put("RECON_ID", reconId);

						}
						if ((ReconUtil.AM.equalsIgnoreCase((String) rec.get("MATCH_TYPE")))
								|| (ReconUtil.AU.equalsIgnoreCase((String) rec.get("MATCH_TYPE")))) {
							matchFlag = true;

							audit(rec, dynamicPstmtMap);

						}

					}
				}

			}

		} catch (Exception e) {
			//e.printStackTrace();
			logger.error("ERROR", e);
		}
		finally{
			
		
				
			
		}
	}

	private static void updateReconGroup(Map<String, Object> rec, String reconIdSeqName, long reconId2,
			Connection connection, String reconTableName) {

		try {
			PreparedStatement updateReconGroupStmt = connection.prepareStatement("UPDATE " + reconTableName
					+ " SET MATCH_TYPE=?,COMMENTS=?,RULE_NAME=?,UPDATED_ON=? WHERE RECON_ID=?");
			updateReconGroupStmt.setObject(1, rec.get("MATCH_TYPE"));
			updateReconGroupStmt.setObject(2, rec.get("COMMENTS"));
			updateReconGroupStmt.setObject(3, rec.get("RULE_NAME"));
			updateReconGroupStmt.setObject(4, new Timestamp(Calendar.getInstance().getTimeInMillis()));
			updateReconGroupStmt.setObject(5, reconId2);
			updateReconGroupStmt.executeUpdate();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();

		}

	}

	public static void audit(Map<String, Object> recMap, Map<String, Map<String, Object>> dynamicPstmtMap) {

		Map<String, Object> auditMap = (Map<String, Object>) dynamicPstmtMap.get((String) recMap.get("SOURCE_TARGET"));
		ResultSet rs = null;
		try {
			PreparedStatement selectStmt = (PreparedStatement) auditMap.get("STG_SLECT_PSTMT");
			PreparedStatement auditStmt = (PreparedStatement) auditMap.get("STG_AUDIT_INSERT_PSTMT");
			PreparedStatement updateStmt = (PreparedStatement) auditMap.get("STG_UPDATE_PSTMT");

			Query queryConfInsert = (Query) auditMap.get("STG_AUDIT_INSERT_QUERY_CONF");

			selectStmt.setLong(1, (Long) recMap.get("SID"));
			rs = selectStmt.executeQuery();
			ResultSetMetaData meta = rs.getMetaData();

			while (rs.next()) {
				Map<String, Object> txnMap = new HashMap<String, Object>();
				for (int i = 1; i <= meta.getColumnCount(); i++) {
					String key = meta.getColumnName(i);
					String value = rs.getString(key);
					txnMap.put(key, value);

				}
				//PersistanceUtil.persistTxn(auditStmt, queryConfInsert, txnMap);
			}

			updateStmt.clearParameters();

			updateStmt.setLong(1, reconId);
			updateStmt.setObject(2, new Timestamp(Calendar.getInstance().getTimeInMillis()));
			long version = Long.parseLong(recMap.get("VERSION").toString());
			updateStmt.setLong(3, version);
			updateStmt.setString(4, "RECON");
			updateStmt.setString(5, (String) recMap.get("MATCH_TYPE"));
			updateStmt.setLong(6, (Long) recMap.get("SID"));
			updateStmt.executeUpdate();

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static Long generateReconId(String seqName, Connection connection) {

		PreparedStatement seqPstmt = null;
		try {
			seqPstmt = connection.prepareStatement("select nextval('" + seqName + "') as sno");
			ResultSet seqRs = seqPstmt.executeQuery();
			if (seqRs.next()) {
				return seqRs.getLong(1);
			}
		} catch (SQLException e) {

			PreparedStatement createSeqPstmt = null;
			PreparedStatement imalSeqNumPstmtTemp = null;
			String imalCreateSeqNumQry = null;
			String imalSeqNumQry = null;
			try {

				imalCreateSeqNumQry = "CREATE  SEQUENCE " + seqName + "  INCREMENT  BY  1 CYCLE ";
				imalSeqNumQry = "select nextval('" + seqName + "') as sno";

				createSeqPstmt = connection.prepareStatement(imalCreateSeqNumQry);
				createSeqPstmt.executeUpdate();

				imalSeqNumPstmtTemp = connection.prepareStatement(imalSeqNumQry);

				ResultSet glRs = imalSeqNumPstmtTemp.executeQuery();
				if (glRs.next()) {
					return glRs.getLong(1);
				}

			} catch (Exception e1) {
				e1.printStackTrace();
			} finally {

				try {
					if (createSeqPstmt != null && !createSeqPstmt.isClosed()) {
						createSeqPstmt.close();
					}
				} catch (Exception e2) {
					e2.printStackTrace();
				}

				try {
					if (imalSeqNumPstmtTemp != null && !imalSeqNumPstmtTemp.isClosed()) {
						imalSeqNumPstmtTemp.close();
					}
				} catch (Exception e2) {
					e2.printStackTrace();
				}

			}
		} finally {

			try {
				if (seqPstmt != null && !seqPstmt.isClosed()) {
					seqPstmt.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
				logger.trace(e.getMessage(), e);
			}
		}
		return 0l;

	}

	public static List<Map<String, Object>> getGroup(List<Map<String, Object>> recList, List<String> keys) {

		List<Map<String, Object>> retList = new ArrayList<Map<String, Object>>();

		Map<String, Object> previousRecord = null;
		boolean continueGrouping = true;

		do {
			List<Map<String, Object>> tempRecList = new ArrayList<Map<String, Object>>();
			for (Map<String, Object> lhsRecord : recList) {

				int compare = 0;

				if (previousRecord != null) {

					compare = checkRecordComparative(previousRecord, lhsRecord, keys);

					if (compare == 0) {
						tempRecList.add(lhsRecord);
					}

				} else {
					tempRecList.add(lhsRecord);
				}

				if (compare != 0) {
					continueGrouping = false;
					break;
				}
				previousRecord = lhsRecord;
			}

			retList.addAll(tempRecList);

			if (recList.size() <= tempRecList.size()) {
				continueGrouping = false;
				break;
			}

		} while (continueGrouping && (recList.size() > 0));

		return retList;
	}

	public static int checkRecordComparative(Map<String, Object> record1, Map<String, Object> record2,
			List<String> compositKeys) {

		int compare = 0;
		boolean firstItr = true;
		int firstCompositeKeyCompareValue = 0;
		for (String key : compositKeys) {

			compare = checkCompare(record1, record2, key);
			if (firstItr) {
				firstCompositeKeyCompareValue = compare;
			}
			if (compare != 0) {
				break;
			}

		}

		return ((compare == 0) ? compare : firstCompositeKeyCompareValue);
	}

	public static int checkCompare(Map<String, Object> record1, Map<String, Object> record2, String methodName) {

		if (record1 == null && record2 == null) {
			return 0;
		} else if (record1 == null && record2 != null) {
			return -1;
		} else if (record1 != null && record2 == null) {
			return 1;
		}

		Object ret1 = record1.get(methodName);
		Object ret2 = record2.get(methodName);

		if (ret1 == null && ret2 == null) {
			return 0;
		} else if (ret1 == null) {
			return -1;
		} else if (ret2 == null) {
			return 1;
		}

		if (ret1 instanceof BigDecimal) {

			return (((BigDecimal) ret1).compareTo((BigDecimal) ret2));
		} else if (ret1 instanceof BigInteger) {
			return (((BigInteger) ret1).compareTo((BigInteger) ret2));
		} else if (ret1 instanceof Integer) {
			return (((Integer) ret1).compareTo((Integer) ret2));
		} else if (ret1 instanceof Long) {
			return (((Long) ret1).compareTo((Long) ret2));
		} else if (ret1 instanceof Double) {
			return (((Double) ret1).compareTo((Double) ret2));
		} else if (ret1 instanceof Float) {
			return (((Float) ret1).compareTo((Float) ret2));
		} else if (ret1 instanceof Character) {
			return (((Character) ret1).compareTo((Character) ret2));
		} else if (ret1 instanceof Byte) {
			return (((Byte) ret1).compareTo((Byte) ret2));
		} else if (ret1 instanceof Short) {
			return (((Short) ret1).compareTo((Short) ret2));
		} else if ((ret1 instanceof java.sql.Date)) {
			return (((java.sql.Date) ret1).compareTo((java.sql.Date) ret2));
		} else if (ret1 instanceof java.util.Date) {
			return (((java.util.Date) ret1).compareTo((java.util.Date) ret2));
		} else if ((ret1 instanceof java.sql.Time)) {
			return (((java.sql.Time) ret1).compareTo((java.sql.Time) ret2));
		} else if (ret1 instanceof java.sql.Timestamp) {
			return (((java.sql.Timestamp) ret1).compareTo((java.sql.Timestamp) ret2));
		}

		return ((ret1.toString().trim()).compareTo(ret2.toString().trim()));

	}

}
