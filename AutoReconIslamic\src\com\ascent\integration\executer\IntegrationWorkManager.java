package com.ascent.integration.executer;

import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;





public class IntegrationWorkManager {
	 ExecutorService executor = null;
	Object lockObj=new Object();
     public IntegrationWorkManager(){
    	 executor= Executors.newFixedThreadPool(1);
     }
	
     public void submitWorkManager(String s,Integration integration,Queries queries,Connection connection,List<Map<String,Object>> txnList){
    	
    	
             Runnable worker = new IntegrationWorkerThread(s, integration, queries, connection, txnList);
             executor.execute(worker);
          
         
         System.out.println("Finished all threads");
     }
     public boolean shutDown(){
    	 executor.shutdown();
         while (!executor.isTerminated()) { 
         	System.out.println("awaits to close");
         	synchronized (lockObj) {
 				try{
 					lockObj.wait(10000);
 				}catch(Exception e){
 					e.printStackTrace();
 				}catch(Throwable e){
 					e.printStackTrace();
 				}
 			}
         }
         return true;
     }
     public static void runWorkManager(String[] args) {
         IntegrationWorkManager testThreadPool=new IntegrationWorkManager();
        // testThreadPool.submitWorkManager();
     }
 }
