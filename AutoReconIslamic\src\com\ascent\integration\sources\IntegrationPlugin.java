package com.ascent.integration.sources;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.integration.util.DbUtil;

public class IntegrationPlugin {

	public static List<String> onusAuthorizerArrList = new ArrayList<String>();
	public static List<String> onusAcquiringChannelIdList = new ArrayList<String>();

	public static List<String> issuerAuthorizerArrList = new ArrayList<String>();
	public static List<String> issuerAcquiringChannelIdList = new ArrayList<String>();

	public static List<String> acquirerAuthorizerArrList = new ArrayList<String>();
	public static List<String> acquirerAcquiringChannelIdList = new ArrayList<String>();
	public static List<String> atmChannelProcCodeList = new ArrayList<String>();
	public static List<String> dohaBankAcquiringChannelIdList = new ArrayList<String>();
	public static List<String> debitCardBinsList = new ArrayList<String>();
	public static List<String> creditCardBinsList = new ArrayList<String>();
	static DbUtil dbUtil = new DbUtil();
	static Connection connection = null;
	static {
		try {
			ResultSet rs = null;
			PreparedStatement channelPstmt = null;
			PreparedStatement binPstmt = null;

			PreparedStatement processCodePstmt = null;
			PreparedStatement responseCodePstmt = null;

			PreparedStatement chanelserPstmt = null;
			PreparedStatement glPsmt = null;

			Map<String, Object> channelIdMap = new HashMap<String, Object>();
			Map<String, Object> channelDesMap = new HashMap<String, Object>();

			Map<String, Object> creditMap = new HashMap<String, Object>();
			Map<String, Object> debitMap = new HashMap<String, Object>();
			Map<String, Object> payrolMap = new HashMap<String, Object>();

			Map<String, Object> pcTrnCodeMap = new HashMap<String, Object>();
			Map<String, Object> pcTrandesMap = new HashMap<String, Object>();

			Map<String, Object> respCodeMap = new HashMap<String, Object>();

			Map<String, Object> glMap = new HashMap<String, Object>();
			Map<String, Object> glAllData = new HashMap<String, Object>();

			Map<String, Object> glwithdrawal = new HashMap<String, Object>();
			Map<String, Object> gldeposit = new HashMap<String, Object>();

			Map<String, Object> chanserno = new HashMap<String, Object>();

			Map<String, Object> binMap = new HashMap<String, Object>();

			connection = dbUtil.getConnection();
			String selChannelQry = "select * from MDT_CHANNELS a where  a.active_index='Y' and version=(select max(version) from MDT_CHANNELS b where a.CHANNEL_NAME=b.CHANNEL_NAME and a.sid=b.sid)";
			channelPstmt = connection.prepareStatement(selChannelQry);
			rs = channelPstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("CHANNEL_NAME");
				String chanel_id = rs.getString("CHANNEL_ID");
				String channel_des = rs.getString("CHANNEL_DESCRIPTION");
				channelIdMap.put(name, chanel_id);
				channelDesMap.put(name, channel_des);

			}

			String selBinQry = "select * from MDT_CARD_BIN a where  a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from MDT_CARD_BIN b where a.PRODUCT_TYPE=b.PRODUCT_TYPE and a.SID=b.SID)";

			binPstmt = connection.prepareStatement(selBinQry);
			rs = binPstmt.executeQuery();

			while (rs.next()) {

				if ("CREDIT CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					creditMap.put(name, bin);

				}

				if ("DEBIT CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					debitMap.put(name, bin);

				}
				if ("PAYROL CARD".equalsIgnoreCase(rs.getString("CARD_TYPE"))) {
					String name = rs.getString("PRODUCT_TYPE");
					String bin = rs.getString("BIN");
					payrolMap.put(name, bin);

				}

			}
			binMap.put("CREDIT_CARD", creditMap);
			binMap.put("DEBIT_CARD", debitMap);
			binMap.put("PAYROL_CARD", payrolMap);

			String selProcessQry = "select * from MDT_PROCESS_CODES a where  a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from MDT_PROCESS_CODES b where a.TRAN_NAME=b.TRAN_NAME and a.SID=b.SID)";

			processCodePstmt = connection.prepareStatement(selProcessQry);
			rs = processCodePstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("TRAN_NAME");
				String tran_code = rs.getString("TRAN_CODE");
				String tran_des = rs.getString("TRAN_DESCRIPTION");
				pcTrnCodeMap.put(name, tran_code);
				pcTrandesMap.put(name, tran_des);

			}

			String selResponseQry = "select * from MDT_RESPONSE_CODES a where  a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from MDT_RESPONSE_CODES b where a.NAME=b.NAME and a.SID=b.SID)";

			responseCodePstmt = connection.prepareStatement(selResponseQry);
			rs = responseCodePstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("NAME");
				String tran_code = rs.getString("CODE");
				respCodeMap.put(name, tran_code);

			}

			String selGlQry = "select * from MDT_GL_ACCOUNTS";

			glPsmt = connection.prepareStatement(selGlQry);
			rs = glPsmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("ACCOUNT_NAME");
				String tran_code = rs.getString("SUB_ACC");

				String tran[] = { rs.getString("BRANCH_CODE"),
						rs.getString("CURRENCY"), rs.getString("CUST_NO"),
						rs.getString("SUB_ACC"), rs.getString("LEDGER"),
						rs.getString("CURENCY_CODE"), rs.getString("CODE") };
				glMap.put(name, tran_code);
				glAllData.put(name, tran);

			}

			String atmSus = "select * from MDT_ATM_SUSPENSE_ACCOUNTS a where  a.ACTIVE_INDEX='Y' and VERSION=(select max(VERSION) from MDT_ATM_SUSPENSE_ACCOUNTS b where a.ATM_LOCATION=b.ATM_LOCATION and a.SID=b.SID)";

			glPsmt = connection.prepareStatement(atmSus);
			rs = glPsmt.executeQuery();

			while (rs.next()) {

				if ("withdrawal".equalsIgnoreCase(rs.getString("TRAN_DES"))) {

					String name = rs.getString("ATM_LOCATION");

					String tran[] = { rs.getString("ATM"),
							rs.getString("BRANCH_CODE"),
							rs.getString("CUST_ID"), rs.getString("CUR"),
							rs.getString("LEDGER"), rs.getString("TXN_TYPE"),
							rs.getString("SUB_ACC_CODE") };
					glwithdrawal.put(name, tran);

				}
				if ("deposit".equalsIgnoreCase(rs.getString("TRAN_DES"))) {

					String name = rs.getString("ATM_LOCATION");

					String tran[] = { rs.getString("ATM"),
							rs.getString("BRANCH_CODE"),
							rs.getString("CUST_ID"), rs.getString("CUR"),
							rs.getString("LEDGER"), rs.getString("TXN_TYPE"),
							rs.getString("SUB_ACC_CODE") };
					gldeposit.put(name, tran);

				}
			}

			String selChanelSerno = "select * from MDT_CHANNEL_SERNO";
			chanelserPstmt = connection.prepareStatement(selChanelSerno);
			rs = chanelserPstmt.executeQuery();

			while (rs.next()) {

				String name = rs.getString("CHANNELNAME");
				String serno = rs.getString("SERNO");
				chanserno.put(name, serno);

			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		String[] onusAuthorizerArr = { "0001", "0050", "0101", "9898", "9999" };
		String[] onusAcquiringChannelIdArr = { "0001", "0050", "0101", "9898",
				"9999" };
		onusAuthorizerArrList = Arrays.asList(onusAuthorizerArr);
		onusAcquiringChannelIdList = Arrays.asList(onusAcquiringChannelIdArr);

		/**
		 * Business Area (ISSUER):
		 * 
		 * AUTHORIZER = 50 or 101 and ACQUIRING_CHANNEL_ID = 7 or 28
		 */
		String[] issuerAuthorizerArr = { "0050", "0101" };
		String[] issuerAcquiringChannelIdArr = { "0007", "0028" };
		issuerAuthorizerArrList = Arrays.asList(issuerAuthorizerArr);
		issuerAcquiringChannelIdList = Arrays
				.asList(issuerAcquiringChannelIdArr);

		/**
		 * Business Area (ACQUIRER): AUTHORIZER = 28 or 7 or 40 or 46 and
		 * ACQUIRING_CHANNEL_ID = 1 or 50 or 101 or 9898 or 9999
		 */

		String[] acquirerAuthorizerArr = { "0050", "0101" };
		String[] acquirerAcquiringChannelIdArr = { "0007", "0028" };
		acquirerAuthorizerArrList = Arrays.asList(acquirerAuthorizerArr);
		acquirerAcquiringChannelIdList = Arrays
				.asList(acquirerAcquiringChannelIdArr);

		/**
		 * ATM PROC CODES: PROC_CODE = 01 or 73 POS PROC CODE : = 00
		 * 
		 * deposites :21, 25, 76 & 86
		 */
		String[] atmChannelProcCode = { "01", "73", "21", "25", "76", "86" };

		atmChannelProcCodeList = Arrays.asList(atmChannelProcCode);
		/**
		 * DOHA BANK
		 * 
		 * ACQUIRING_CHANNEL_ID = 1 or 50 or 101 or 9898 or 9999
		 */
		String[] dohaBankAcquiringChannelId = { "0001", "0050", "0101", "9898",
				"9999" };
		dohaBankAcquiringChannelIdList = Arrays
				.asList(dohaBankAcquiringChannelId);

		/**
		 * DebitCard BIN = 434141 and 484823 and 428246
		 */
		String[] debitCardBins = { "434141", "484823", "428246" };

		/**
		 * CREDIT CARD BIN BIN = 464421, 464423, 471369, 444460, 428131, 428132,
		 * 428133, 520419, 520414, 512434, 419249, 419250, 419251, 472481,
		 * 466155, 428245, 428131, 444461
		 */

		String[] creditCardBins = { "464421", "464423", "471369", "444460",
				"428131", "428132", "428133", "520419", "520414", "512434",
				"419249", "419250", "419251", "472481", "466155", "428245",
				"428131", "444461" };

		debitCardBinsList = Arrays.asList(debitCardBins);
		creditCardBinsList = Arrays.asList(creditCardBins);
	}

	public static void main(String[] args) {

		IntegrationPlugin integrationPlugin = new IntegrationPlugin();
		try {

		} catch (Exception e) {
			e.printStackTrace();
		}
		integrationPlugin.process();

	}

	public void process() {

	}
}
