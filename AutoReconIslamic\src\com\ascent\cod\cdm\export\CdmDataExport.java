package com.ascent.cod.cdm.export;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.mail.test.Test88;
import com.ascent.persistance.LoadRegulator;
import com.ascent.scheduler.MailTrigger;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;


public class CdmDataExport extends BasicDataSource implements PagesConstants {

	private static final String GET_COD_CDM_INTERNAL_RECORDS = "GET_COD_CDM_INTERNAL_RECORDS";
	private static final String GET_CDM_INTERNAL_SUPPRESS_RECORDS = "GET_CDM_INTERNAL_SUPPRESS_RECORDS";
	private static final String GET_COD_CDM_EXTERNAL_RECONCILE_RECORDS = "GET_COD_CDM_EXTERNAL_RECONCILE_RECORDS";
	private static final String GET_COD_CDM_EXTERNAL_SUPPRESS_RECORDS = "GET_COD_CDM_EXTERNAL_SUPPRESS_RECORDS";

	@SuppressWarnings("unchecked")
	public Map<String, Object> getCdmInternalreconcileDataLoad() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;

		List<Map<String, Object>> internalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> internaldatamap = new HashMap<String, Object>();
		

		try {

			queryConf = queryConfs.getQueryConf(GET_COD_CDM_INTERNAL_RECORDS);
			internalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal :" + internalReconcile);
			
			List internalDataList = new ArrayList();
			for (Map<String, Object> recordMap : internalReconcile) {

				internaldatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Reconciles Data :");
				internalDataList.add(recordMap);
				System.out.println("kaushal Records:" + internalDataList);

			}
			internaldatamap.put("internalrecondataList", internalDataList);
			System.out.println("Unmatch Kaushal ============:" + internaldatamap);
			exportExcelAndTriggerMailIInternalReconCDM(internaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internaldatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIInternalReconCDM(Map<String, Object> internaldatamap) {

		Map<String, Object> mailData = new HashMap<String, Object>();

	
		String fileName = null;
		try {
			// Excel Export Internal recon data export
			fileName = ExportExcelInternalReconcileCDM
					.exportExcel3((List<Map<String, Object>>) internaldatamap.get("internalrecondataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}
 

	// atmInternalSuppressDataload

	@SuppressWarnings("unchecked")
	public Map<String, Object> cdmInternalSuppressDataload() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> suppressInternal = new ArrayList<Map<String, Object>>();
		Map<String, Object> internalSuppressdatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_CDM_INTERNAL_SUPPRESS_RECORDS);
			suppressInternal = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:" + suppressInternal);
			
			List suppressDataList = new ArrayList();
			for (Map<String, Object> recordMap : suppressInternal) {

				internalSuppressdatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm Internal Suppress Data:");
				internalSuppressdatamap.put("Mailid", "<EMAIL>");
				suppressDataList.add(recordMap);
				System.out.println("kaushal Records:" + suppressDataList);

			}
			internalSuppressdatamap.put("internalsuppressdataList", suppressDataList);
			System.out.println("Unmatch Kaushal ============:" + internalSuppressdatamap);
			exportExcelAndTriggerMailInternalSuppressCDM(internalSuppressdatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return internalSuppressdatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailInternalSuppressCDM(Map<String, Object> internalSuppressdatamap) {

		Map<String, Object> mailData = new HashMap<String, Object>();

		String fileName = null;

		try {
			// Excel Export Internal Suppress Data
			fileName = ExportExcelInternalSuppressCDM
					.exportExcel1((List<Map<String, Object>>) internalSuppressdatamap.get("internalsuppressdataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}
	/////// ================internal suppress End

	// External reconcile records

	@SuppressWarnings("unchecked")
	public Map<String, Object> getCDMExternalreconcileDataLoad() {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		LoadRegulator loadRegulator = new LoadRegulator();
		Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
		Query queryConf;
		// ALLpendingCases With Recon
		List<Map<String, Object>> externalReconcile = new ArrayList<Map<String, Object>>();
		Map<String, Object> externaldatamap = new HashMap<String, Object>();
		try {

			queryConf = queryConfs.getQueryConf(GET_COD_CDM_EXTERNAL_RECONCILE_RECORDS);
			externalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
			System.out.println("kaushal----:"+ externalReconcile);
			
			List externalDataList = new ArrayList();
			for (Map<String, Object> recordMap : externalReconcile) {

				externaldatamap.put("content",
						"PFA for :" + recordMap.get("ACTIVE INDEX") + "  Atm External Reconciles Data :");
				externalDataList.add(recordMap);
				System.out.println("kaushal Records:" + externalDataList);

			}
			externaldatamap.put("externalrecondataList", externalReconcile);
			System.out.println(" Kaushal ============:" + externaldatamap);
			exportExcelAndTriggerMailIExternlReconCDM(externaldatamap);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return externaldatamap;

	}

	@SuppressWarnings("unchecked")
	public static String exportExcelAndTriggerMailIExternlReconCDM(Map<String, Object> externaldatamap) {

		Map<String, Object> mailData = new HashMap<String, Object>();

		MailTrigger mailTrigger = new MailTrigger();
		String fileName = null;

		try {
			// Excel Export Internal Suppress Data
			fileName = ExportExcelExternalReconcileCDM
					.exportExcel4((List<Map<String, Object>>) externaldatamap.get("externalrecondataList"));

		} catch (IOException e) {
			e.printStackTrace();
		}
		return fileName;
	}

	//getExternalSuppressDataLoad
	
		@SuppressWarnings("unchecked")
		public Map<String, Object> getcdmExternalSuppressDataLoad()
		{
			AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
			LoadRegulator loadRegulator = new LoadRegulator();
			Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
			Query queryConf;
			// ALLpendingCases With Recon
			List<Map<String, Object>> suppressexternalReconcile = new ArrayList<Map<String, Object>>();
			Map<String, Object> externalSuppdatamap = new HashMap<String, Object>();
			try {

				queryConf = queryConfs.getQueryConf(GET_COD_CDM_EXTERNAL_SUPPRESS_RECORDS);
				suppressexternalReconcile = loadRegulator.loadCompleteData(new HashMap<String, Object>(), queryConf);
				System.out.println("kaushaL:" + suppressexternalReconcile);
				List externalsuppressDataList = new ArrayList();
				for (Map<String, Object> recordMap : suppressexternalReconcile) {

					
					externalSuppdatamap.put("content", "PFA for :" + recordMap.get("ACTIVE INDEX") + " Atm External Suppress Data :");
					//externalSuppdatamap.put("Mailid", externalSuppdatamap.get("<EMAIL>"));
					externalsuppressDataList.add(recordMap);
					System.out.println("kaushal Records:" + externalsuppressDataList);

				}
				externalSuppdatamap.put("externalsuppressDataList", externalsuppressDataList);
				System.out.println("Kaushal ============:" + externalSuppdatamap);
				exportExcelAndTriggerMailIExternalSuppressCDM(externalSuppdatamap);
			} catch (Exception e) {
				e.printStackTrace();
			}
			return externalSuppdatamap;
			
		}
		
		@SuppressWarnings("unchecked")
		public static String exportExcelAndTriggerMailIExternalSuppressCDM(Map<String, Object> externalSuppdatamap) {

			Map<String, Object> mailData = new HashMap<String, Object>();

			MailTrigger mailTrigger = new MailTrigger();
			String fileName = null;

			try {
				// Excel Export external Supress recon data export
				fileName = ExportExcelExternalSuppressCDM.exportExcel2((List<Map<String, Object>>) externalSuppdatamap.get("externalsuppressDataList"));
				
			} catch (IOException e) {
				e.printStackTrace();
			}
			return fileName;
		}
		public  void trigarMailInternalorExternalRecon() {
			
			 String[] reconcileFiles= new String[2];
			// String[] supressFilesFiles= new String[2];

			 Map<String, Object> internalreconmap= getCdmInternalreconcileDataLoad();
			 String reconcile_internal_file_name = exportExcelAndTriggerMailIInternalReconCDM(internalreconmap);
			 reconcileFiles[0]= reconcile_internal_file_name;
			 
			 Map<String, Object> externalreconmap= getCDMExternalreconcileDataLoad();
			 String reconcile_external_file_name = exportExcelAndTriggerMailIExternlReconCDM(externalreconmap);
			 System.out.println("kaushal"+reconcile_external_file_name );
			 reconcileFiles[1]	=  reconcile_external_file_name ;
			 
			 Test88 testmail = new Test88();
			 String masssageformate = testmail.messageFormat();
			 //String mail ="<EMAIL>";
			 try {
				testmail.sendMailWithAttachments(masssageformate,"<EMAIL>", reconcileFiles);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
						
		}
		public  void trigarMailInternalorExternalSuppress() {
			
			 String[] reconcileFiles= new String[2];
			// String[] supressFilesFiles= new String[2];

			 Map<String, Object> internalsuppresmap= cdmInternalSuppressDataload();
			 String reconcile_internal_file_name = exportExcelAndTriggerMailInternalSuppressCDM(internalsuppresmap);
			 reconcileFiles[0]= reconcile_internal_file_name;
			 
			 Map<String, Object> externalsuppressmap= getcdmExternalSuppressDataLoad();
			 String reconcile_external_file_name = exportExcelAndTriggerMailIExternalSuppressCDM(externalsuppressmap);
			 System.out.println("kaushal"+reconcile_external_file_name );
			 reconcileFiles[1]	=  reconcile_external_file_name ;
			 
			 Test88 testmail = new Test88();
			 String masssageformate = testmail.messageFormat();
			 //String mail ="<EMAIL>";
			 try {
				testmail.sendMailWithAttachments(masssageformate,"<EMAIL>", reconcileFiles);
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
						
		}
			
	public static void main(String[] args) {

		CdmDataExport es = new CdmDataExport();
		//es.trigarMailInternalorExternalRecon();
		//es.trigarMailInternalorExternalSuppress();
		 es.cdmInternalSuppressDataload();
         es.getCDMExternalreconcileDataLoad();
	     es.getCdmInternalreconcileDataLoad();
		 es.getcdmExternalSuppressDataLoad();
	}

}
