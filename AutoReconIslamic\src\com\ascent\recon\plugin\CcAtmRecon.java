package com.ascent.recon.plugin;

import java.io.FileInputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;


import com.ascent.boot.recon.ReconMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.custumize.recon.Recon;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.ReconUtil;
import com.ascent.recon.AscentAutoReconPlugin;

import com.ascent.util.AscentAutoReconConstants;

public class CcAtmRecon extends AscentAutoReconPlugin {

	private static Logger logger = LogManager.getLogger(CcAtmRecon.class
			.getName());

	Queries reconQueriesConf = null;
	Exception e1;
	String reconIdSeqName = null;
	String reconTableIdSeqName = null;

	Query insertQryConf = null;
	Query updateQryConf = null;
	Query upstreamQryConf = null;

	Connection connection = null;

	ResultSet upstreamRs = null;

	PreparedStatement upstreamPstmt = null;
	PreparedStatement reconInsertPstmt = null;
	PreparedStatement reconUpdatePstmt = null;
	PreparedStatement reconTableIdGenPstmt = null;
	PreparedStatement reconIdGenPstmt = null;
	ReconMetaInstance reconMetaInstance = null;
	String reconTableName = null;

	public static long start = 01;

	Map<String, Map<String, Object>> dynamicPstmtMap = new HashMap<String, Map<String, Object>>();

	public CcAtmRecon() {

		this.reconMetaInstance = ReconMetaInstance.getInstance();

	}

	public static void main(String[] args) {

		CcAtmRecon atmIssRecon = new CcAtmRecon();

	}

	public List<Map<String, Object>> getRsNextBatch(ResultSet rs)
			throws SQLException {
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();
		ResultSetMetaData rsmd = rs.getMetaData();
		int rhsColumnCount = rsmd.getColumnCount();
		int recCnt = 0;
		int batchSize = 500;
		while (recCnt < batchSize && rs.next()) {
			Map<String, Object> rhsRecon = new HashMap<String, Object>();

			for (int i = 1; i <= rhsColumnCount; i++) {
				String columnName = rsmd.getColumnName(i);
				rhsRecon.put(columnName, rs.getObject(columnName));

			}
			recCnt++;
			recordsData.add(rhsRecon);
		}
		return recordsData;
	}

	public static long totalRetrievedRecs = 0l;

	public Map<String, Object> process(Recon recon) throws Exception {

		Map<String, Object> result = new HashMap<String, Object>();
		totalRetrievedRecs = 0l;
		this.reconQueriesConf = this.reconMetaInstance.getReconQueries(recon
				.getName());

	this.connection = DbUtil.getConnection();
		//this.connection.setAutoCommit(false);
		List<Map<String, Object>> recordsData = new ArrayList<Map<String, Object>>();

		try {
			start = System.currentTimeMillis();
			insertQryConf = reconQueriesConf.getQueryConf(recon
					.getReconInsertQueryName());
			upstreamQryConf = reconQueriesConf.getQueryConf(recon
					.getReconUpstreamQueryName());

			List<String> upstreamTableNameList = upstreamQryConf
					.getTargetTableNameList();

			if (upstreamTableNameList == null
					|| upstreamTableNameList.size() < 0) {
				throw new Exception(
						"Please configure the Upstream table names for Upstream query");
			}

			if (insertQryConf.getTargetTableNameList() == null
					|| insertQryConf.getTargetTableNameList().size() <= 0) {
				throw new Exception(
						"Please configure the recon table name for recon insert query");
			}

			reconTableName = insertQryConf.getTargetTableNameList().get(0);

			this.reconIdSeqName = reconTableName + "_SEQ";
			String reconIdGenQry = "SELECT NEXT VALUE FOR " + reconIdSeqName
					+ " as sno";
			this.reconIdGenPstmt = this.connection
					.prepareStatement(reconIdGenQry);

			this.reconTableIdSeqName = reconTableName + "_ID_SEQ";
			String reconTableIdGenQry = "SELECT NEXT VALUE FOR "
					+ reconTableIdSeqName + " as sno";

			this.reconTableIdGenPstmt = this.connection
					.prepareStatement(reconTableIdGenQry);

			this.reconInsertPstmt = this.connection
					.prepareStatement(insertQryConf.getQueryString());

			String reconUpdateQryString = "UPDATE "
					+ reconTableName
					+ " SET RECON_ID=?,MATCH_TYPE=?,ACTIVE_INDEX=?,USER_ID=?,UPDATED_ON=?,COMMENTS=?,RULE_NAME=?,ACTIVITY_STATUS=?,STATUS=?"
					+ " WHERE SID=? and RECON_SIDE=?";
			String reconUpdateQryParams = "RECON_ID@BIGINT,MATCH_TYPE@VARCHAR,ACTIVE_INDEX@VARCHAR,USER_ID@VARCHAR,UPDATED_ON@TIMESTAMP,COMMENTS@VARCHAR,RULE_NAME@VARCHAR,ACTIVITY_STATUS@VARCHAR,STATUS@VARCHAR,SID@BIGINT,RECON_SIDE@VARCHAR";
			this.updateQryConf = new Query();
			this.updateQryConf.setQueryString(reconUpdateQryString);
			this.updateQryConf.setQueryParam(reconUpdateQryParams);
			this.updateQryConf.bootConf();
			this.reconUpdatePstmt = this.connection
					.prepareStatement(reconUpdateQryString);

			for (String upstreamTable : upstreamTableNameList) {

				String updateQuery = "UPDATE "
						+ upstreamTable
						+ " SET RECON_ID=?,UPDATED_ON=?,VERSION=?,OPERATION=?,RECON_STATUS=? WHERE SID=?";
				String selectQry = "select * from " + upstreamTable
						+ " where SID=?";

				// TODO: plugin to create audit Query
				Query stagingAuditInsertQryConf = reconQueriesConf
						.getQueryConf(upstreamTable + "_AUDIT_INSERT_QRY");

				PreparedStatement pstmt = connection
						.prepareStatement(updateQuery);
				PreparedStatement selectPstmt = connection
						.prepareStatement(selectQry);
				PreparedStatement stagingAuditInsertPstmt = connection
						.prepareStatement(stagingAuditInsertQryConf
								.getQueryString());

				Map<String, Object> auditPstmtMap = (Map<String, Object>) dynamicPstmtMap
						.get(upstreamTable);
				if (auditPstmtMap == null) {
					auditPstmtMap = new HashMap<String, Object>();
					dynamicPstmtMap.put(upstreamTable, auditPstmtMap);

				}

				auditPstmtMap.put("STG_AUDIT_INSERT_QUERY_CONF",
						stagingAuditInsertQryConf);

				auditPstmtMap.put("STG_UPDATE_PSTMT", pstmt);
				auditPstmtMap.put("STG_SLECT_PSTMT", selectPstmt);
				auditPstmtMap.put("STG_AUDIT_INSERT_PSTMT",
						stagingAuditInsertPstmt);

			}

			String upstreamQry = upstreamQryConf.getQueryString();
			this.upstreamPstmt = connection.prepareStatement(upstreamQry);
			this.upstreamRs = upstreamPstmt.executeQuery();
			recordsData.addAll(getRsNextBatch(upstreamRs));
			totalRetrievedRecs = recordsData.size();

			while (recordsData.size() > 0) {
				List<String> keys = new ArrayList<String>();

				keys.add("CARD_NUM");
				keys.add("TRA_AMT");

				System.out.println("Size" + recordsData.size() + "Total:"
						+ totalRetrievedRecs);
				if (recordsData.size() < 50) {
					List<Map<String, Object>> temp = getRsNextBatch(upstreamRs);
					if (temp != null && !temp.isEmpty()) {
						totalRetrievedRecs = totalRetrievedRecs + temp.size();
						recordsData.addAll(temp);
					}
				}

				List<Map<String, Object>> subGroup = ReconUtil.getGroup(
						recordsData, keys);
				recordsData.removeAll(subGroup);

				Map<String, List<Map<String, Object>>> recGroup = new HashMap<String, List<Map<String, Object>>>();

				recGroup.put("DAILY_GL_TRXN", new ArrayList<Map<String, Object>>());
				recGroup.put("CC_PAYMENTS_POOL", new ArrayList<Map<String, Object>>());

				for (Map<String, Object> rec : subGroup) {

					List<Map<String, Object>> sourceGroup = recGroup
							.get((String) rec.get("RECON_SIDE"));

					sourceGroup.add(rec);

				}

				if (recGroup.get("DAILY_GL_TRXN") != null
						&& ((List<Map<String, Object>>) recGroup.get("DAILY_GL_TRXN"))
								.size() > 0) {
					reconCheck(recGroup);
				}

			}

		} catch (Exception e) {
			e1 = e;
			System.out
					.println("after stoping we get exception ------------->  "
							+ e.getMessage());
			//e.printStackTrace();
			logger.error(e.getMessage(), e);
		} finally {
			System.out
					.println("after stoping we get exception finally block ------------->  ");
			;
			String flag = "";
			// =props.getProperty("RECONPROCESS");

		/*	try {
				System.out.println(connection);
				Connection con = DbUtil.getConnection();
				PreparedStatement ps = con
						.prepareStatement("select status from schedulestatus_tbl");
				ResultSet rs = ps.executeQuery();

				if (rs.next()) {
					flag = rs.getString("status");
					System.out.println(flag);
				}
				rs.close();
				ps.close();
				DbUtil.closeConnection(con);
			} catch (Exception e) {
				System.out.println(e.getMessage());

			}
			System.out.println("flag in finally block    --------------->  "
					+ flag);*/
			//DbUtil.closeResultSet(upstreamRs);
			//DbUtil.closePreparedStatement(reconIdGenPstmt);
		
			/*if (!ReconProcessTrigger.getThreadBoolean()) {
				this.connection.commit();
				logger.trace("Recon success fully completed");
			} else {
				System.out.println("false    --------------");
				logger.trace("You stop the Recon in middle so recon not completd rollback successfully");
				
				CronSchedulerUtility.updateSchedulerStatus("reconflag", 0);
				CronSchedulerUtility.updateSchedulerStatus("reconendtime",
						CronSchedulerUtility.getTime());
				//System.exit(0);

				String command = "C:/Program Files/Apache Software Foundation/Tomcat 8.0/bin/shutdown.bat";//for linux use .sh
				try{
				Process child = Runtime.getRuntime().exec(command);
				}catch(Exception e){
					System.out.println("in stoping server "+e.getMessage());
				}
				//this.connection.rollback();
				System.out.println("after roll back");
				try {
					Connection con = DbUtil.getConnection();
					PreparedStatement ps = con
							.prepareStatement("update schedulestatus_tbl set status=0");
					ps.executeUpdate();
					ps.close();
					//con.rollback();
					//DbUtil.closeConnection(con);
				} catch (Exception e) {
					System.out.println(e.getMessage());
				}
								
			}
*/
			
			// e1.getMessage());
			//DbUtil.closeResultSet(upstreamRs);

			//DbUtil.closePreparedStatement(reconIdGenPstmt);
			for (Map<String, Object> auditStmtMap : dynamicPstmtMap.values()) {

				if (auditStmtMap != null) {
					PreparedStatement pstmtUpdate = (PreparedStatement) auditStmtMap
							.get("STG_UPDATE_PSTMT");
					PreparedStatement pstmtSelect = (PreparedStatement) auditStmtMap
							.get("STG_SLECT_PSTMT");
					PreparedStatement pstmtAudit = (PreparedStatement) auditStmtMap
							.get("STG_AUDIT_INSERT_PSTMT");

				//	DbUtil.closePreparedStatement(pstmtUpdate);
				//	DbUtil.closePreparedStatement(pstmtSelect);
					//DbUtil.closePreparedStatement(pstmtAudit);
//
				}

			}
			DbUtil.closePreparedStatement(reconUpdatePstmt);
			DbUtil.closePreparedStatement(reconTableIdGenPstmt);
			DbUtil.closePreparedStatement(reconIdGenPstmt);
			DbUtil.closePreparedStatement(upstreamPstmt);
			DbUtil.closePreparedStatement(reconInsertPstmt);
			DbUtil.closeConnection(connection);
			System.out
					.println("in finally block connection     ------------->  "
							+ connection);
			logger.trace(System.currentTimeMillis() - start);
		}
		return result;
	}

	public void reconCheck(Map<String, List<Map<String, Object>>> recGroup) {

		List<Map<String, Object>> iris = recGroup.get("DAILY_GL_TRXN");
		List<Map<String, Object>> gl1002 = recGroup.get("CC_PAYMENTS_POOL");

		String respCode = (String) iris.get(0).get("RESP_CODE");

		Map<String, Object> gl1002MainRec = null;
		Map<String, Object> gl1002RevRec = null;

		for (Map<String, Object> rec : gl1002) {
			if ("CASH DEPOSIT".equalsIgnoreCase((String) rec.get("DEB_CRE_IND"))) {
				gl1002MainRec = rec;
			} else {
				gl1002RevRec = rec;
			}
		}

		List<Map<String, Object>> gl1002TempList = new ArrayList<Map<String, Object>>();

		if (gl1002MainRec != null) {
			gl1002TempList.add(gl1002MainRec);
		}

		if (gl1002RevRec != null) {
			gl1002TempList.add(gl1002RevRec);
		}

		recGroup.put("CC_PAYMENTS_POOL", gl1002TempList);

		gl1002 = recGroup.get("CC_PAYMENTS_POOL");

		if (iris.size() > 1) {

			recGroup.remove("CC_PAYMENTS_POOL");

			String refNO = (String) iris.get(0).get("CARD_NUM");
			 System.out.println("================Exception Transaction=============="+iris.get(0).get("TRA_AMT"));
			Double amt = Double.valueOf( iris.get(0).get("TRA_AMT")+"");
			ReconUtil.updateStatus(recGroup, ReconUtil.AU,
					"UNCOMPATIBLE COMBINATION FOUND WITH CARD_NUM:" + refNO
							+ " and AMT:" + amt, "UNCOMPATIBLE COMBINATION");

		} else /*if (respCode != null
				&& ReconUtil.irisMainCodes.contains(respCode))*///COMMENTS BY SHIVAM 07-06-2017
		{

			if (iris.size() == 1 && gl1002.size() == 1) {

				ReconUtil.updateStatus(recGroup, ReconUtil.AM,
						"AMOUNT,CARD_NUM MATCH RULE SUCCESSFUL",
						"AMOUNT,CARD_NUM MATCH RULE");

			} else {
				StringBuilder commentsSb = new StringBuilder();

				if (gl1002.size() == 0) {
					if (commentsSb.length() != 0) {
						commentsSb.append(ReconUtil.COMMA);
					}

					commentsSb.append("CC_PAYMENTS_POOL");
				}

				String comment = "MISSING TXNS  " + commentsSb.toString();

				ReconUtil.updateStatus(recGroup, ReconUtil.AU, comment,
						"MISSING TXNS RULE");
			}

		} /*else if (respCode != null
				&& ReconUtil.irisReversalCodes.contains(respCode)) {/*

			if (iris.size() == 1 && gl1002.size() == 2) {

				if (gl1002MainRec != null && gl1002RevRec != null) {

					ReconUtil.updateStatus(recGroup, ReconUtil.AM,
							"DATE,AMOUNT,RRN REV MATCH RULE SUCCESSFUL",
							"DATE,AMOUNT,RRN REV MATCH RULE");

				} else {
					StringBuilder comments = new StringBuilder();

					ReconUtil.validateMissingTxn(recGroup, gl1002MainRec,
							gl1002RevRec, comments, "CC_PAYMENTS_POOL", "CC_PAYMENTS_POOL MAIN");
					ReconUtil.validateMissingTxn(recGroup, gl1002RevRec,
							gl1002MainRec, comments, "CC_PAYMENTS_POOL", "CC_PAYMENTS_POOL REV");

					String cmmt = "MISSING TXNS   " + comments.toString();
					ReconUtil.updateStatus(recGroup, ReconUtil.AU, cmmt,
							"MISSING REV TXNS");
				}

			} else {
				StringBuilder comments = new StringBuilder();

				ReconUtil.validateMissingTxn(recGroup, gl1002MainRec,
						gl1002RevRec, comments, "CC_PAYMENTS_POOL", "CC_PAYMENTS_POOL MAIN");
				ReconUtil.validateMissingTxn(recGroup, gl1002RevRec,
						gl1002MainRec, comments, "CC_PAYMENTS_POOL", "CC_PAYMENTS_POOL REV");

				String cmmt = "MISSING TXNS   " + comments.toString();
				ReconUtil.updateStatus(recGroup, ReconUtil.AU, cmmt,
						"MISSING REV TXNS");
			}

		}*///COMMENTS BY SHIVAM 07-06-2017

		ReconUtil.insertRecon(recGroup, this.connection, this.reconIdGenPstmt,
				reconIdSeqName, reconTableIdGenPstmt, reconTableIdSeqName,
				reconInsertPstmt, reconUpdatePstmt, insertQryConf,
				updateQryConf, dynamicPstmtMap, reconTableName, "80");

	}

}
