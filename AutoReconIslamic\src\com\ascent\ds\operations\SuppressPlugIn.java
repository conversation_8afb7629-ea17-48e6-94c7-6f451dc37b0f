


package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class SuppressPlugIn extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = -6870261822605048138L;
	

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		System.out.println("reqCriteria----------------->"+reqCriteria);
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		String userId = user.getUserId();
		String userName=user.getUserName();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName =(String)reqCriteria.get("moduleName");
		String dsName=(String)reqCriteria.get("dsName");
		String reconTableName=(String)reqCriteria.get("reconTableName");
		String sourceType = null;
		if(reqCriteria.get("sourceType") != null)	sourceType=(String)reqCriteria.get("sourceType");
		String centrifugalAmountField="";
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
				if(!(moduleName.equalsIgnoreCase("UPSTREAM")) && reqCriteria.get("centrifugalAmount")!=null){
				 centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
				}else if(moduleName.equalsIgnoreCase("UPSTREAM")&& reqCriteria.get("centrifugalAmount")!=null){
					centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
				}else{
					centrifugalAmountField=null;
				}
		// action :"SUPPRESS",

		//TODO: operation synchronization.
		if (selectedRecords != null) {

			StringBuilder commentSb = new StringBuilder();
			List<Object> workflowIds = new ArrayList<Object>();
			if (workflowIds.size() > 0) {
				result = new HashMap<String, Object>();
				String commentPrefix = " " ;
				if (workflowIds.size() == 1) {
					commentSb.append("Selected record with SID ");
				} else if (workflowIds.size() > 1) {
					commentSb.append("Selected records with SIDs ");
				}
				for (Object obj : workflowIds) {
					if (commentSb.length() != 0) {
						commentSb.append(",");
					}
					commentSb.append(obj);
				}
				commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
				updateResultStatus(result, FAILED, commentPrefix);
				response.setData(result);
				return response;
			}
						
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		paramsMap.put("reconTableName", reconTableName);
		paramsMap.put("sourceType", sourceType);
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		//if(centrifugalAmountField!=null){
		paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		//}
		
		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> suppressArgs) {

		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
			Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) suppressArgs.get(USER_ID);
			String dsName = (String) suppressArgs.get(DS_NAME);
			suppressArgs.put(PERSIST_CLASS, SUPPRESS_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", suppressArgs);
		//	String moduleName=(String) suppressArgs.get(MODULE);
			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) suppressArgs.get(BUSINES_AREA);
				String reconName = (String) suppressArgs.get(RECON_NAME);
				String comments = (String) suppressArgs.get(COMMENTS);
				String moduleName=(String)suppressArgs.get(MODULE);
				String reconTableName=(String)suppressArgs.get("reconTableName");
				String sourceType=(String)suppressArgs.get("sourceType");
				userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
						SUPPRESS_OPERATION, activityDataMap, activityStatus, comments);

				updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				
				String integrationName= (String) suppressArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator=new LoadRegulator();
				InsertRegulator insertRegulator=new InsertRegulator();
				
				String tableNameStg="";
				String tableNameAudit="";
				if(moduleName.equalsIgnoreCase("UPSTREAM")){
					 tableNameStg=integrationName+"_STG_EX";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				
				}else if(moduleName.equalsIgnoreCase("RECON")){
				     tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				
				//OperationsUtil.updateQueryConf(tableNameStg,connection);
				//String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=? from  "+tableNameStg+" where sid = ?";
				//Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				PreparedStatement reconToAuditPstmt=null;
				PreparedStatement updateReconPstmt=null;
				try{
					List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) suppressArgs.get(SELECTED_RECORDS);
					/*for(Map<String,Object> selectedRec:selectedRecords){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String reqComments=user.getUserId()+" : "+(String) suppressArgs.get(COMMENTS);
					//	selectedRec.put("ACTIVITY_COMMENTS", reqComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Map m=new HashMap();
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						Long version= Long.valueOf(selectedRec.get("VERSION")+"");
						++version;
						//stgUpdatePstmt.setObject(1,version);
						stgUpdatePstmt.setObject(1,"Y");
						stgUpdatePstmt.setObject(2,reqComments);
						stgUpdatePstmt.setObject(3,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
						
					}*/
					
					for(Map<String,Object> selectedRec:selectedRecords){
						String reqComments=user.getUserId()+" : "+(String) suppressArgs.get(COMMENTS);
						
						//STG logic
						 if(moduleName.equalsIgnoreCase("RECON")){
							String StgAuditInsert="INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?";
							auditInsertPstmt=connection.prepareStatement(StgAuditInsert);
							auditInsertPstmt.setObject(1,selectedRec.get("SID"));
							auditInsertPstmt.executeUpdate();
							System.out.println("record inserted from STG to Audit");
						 }
						
						String updateQuery="update "+tableNameStg+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? from  "+tableNameStg+" where sid = ?";
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						stgUpdatePstmt.setObject(1,"Y");
						stgUpdatePstmt.setObject(2,reqComments);
						stgUpdatePstmt.setObject(3,Long.valueOf(selectedRec.get("VERSION")+"")+1);
						stgUpdatePstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stgUpdatePstmt.setObject(5,selectedRec.get("SID"));
						stgUpdatePstmt.executeUpdate();
						System.out.println("STG updated");
						
						
						//Exception logic
						//if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
						if(sourceType !=null && sourceType.equals("INTERNAL")) {
							//copy record from Recon table to Audit table
							/*String reconTableToAudit="INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?";
							reconToAuditPstmt=connection.prepareStatement(reconTableToAudit);
							reconToAuditPstmt.setObject(1,selectedRec.get("SID"));
							reconToAuditPstmt.executeUpdate();
							System.out.println("record inserted from Recon to Audit");*/
							
							//updating Recon table
							String updateReconToAudit="update "+reconTableName+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? where SID = ?";
							updateReconPstmt=connection.prepareStatement(updateReconToAudit);
							updateReconPstmt.setObject(1,"Y");
							updateReconPstmt.setObject(2,reqComments);
							updateReconPstmt.setObject(3,Long.valueOf(selectedRec.get("VERSION")+"")+1);
							updateReconPstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
							updateReconPstmt.setObject(5,selectedRec.get("SID"));
							updateReconPstmt.executeUpdate();
							System.out.println("Recon updated");
						}
						
					}
					
					
					
					try{
						String strquery="select email_id from users where user_name='"+user.getReporting()+"'";
						Statement st=connection.createStatement();
						ResultSet rs=st.executeQuery(strquery);
						if(rs.next()){
							String appMailId=rs.getString(1);
							OperationMail mail=	new OperationMail(user.getEmailId(), appMailId);
							mail.sendMail(user.getEmailId(), appMailId);
							mail.sentMail(appMailId, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, selectedRecords, "SUPRESS OPERATION PENDING FOR APPROVAL");
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
				//audit();
				return result;
			} else {

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED,OPERATION_FAILED );
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}
		
	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection
			) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator=new LoadRegulator();
		InsertRegulator insertRegulator=new InsertRegulator();
		try {
            String userid=(String) activityDataMap.get("userId");
			String comment=(String) activityDataMap.get("comment");
			Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
			String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
			List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
			String moduleName=(String) activityRecordsMap.get(MODULE);
			String reconName = (String) (activityRecordsMap.get(RECON_NAME));
			String dsName = (String) activityRecordsMap.get(DS_NAME);
			String reconTableName=(String)activityRecordsMap.get("reconTableName");
			String sourceType = null;
			if(activityRecordsMap.get("sourceType") != null)	sourceType=(String)activityRecordsMap.get("sourceType");
			connection = DbUtil.getConnection();
			System.out.println(status);
			if (APPROVED.equalsIgnoreCase(status)) {
				String comments = (String) activityDataMap.get("comment");
				String tableNameStg="";
				String tableNameAudit="";
				if(moduleName.equalsIgnoreCase("UPSTREAM")){
					 tableNameStg=integrationName+"_STG_EX";
					 tableNameAudit=integrationName+"_STG_AUDIT";
					
				}else if(moduleName.equalsIgnoreCase("RECON")){
					 tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				//OperationsUtil.updateQueryConf(tableNameStg,connection);
				//String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,ACTIVE_INDEX=? from  "+tableNameStg+" where sid = ?";
				//String updateQuery="update "+tableNameStg+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,ACTIVE_INDEX=? from  "+tableNameStg+" where sid = ?";
				//Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				PreparedStatement reconToAuditPstmt=null;
				PreparedStatement updateReconPstmt=null;
				try{
			
					/*for(Map<String,Object> selectedRec:records){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String approverComments=userid+" : "+comment;
						selectedRec.put("ACTIVITY_COMMENTS", approverComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						selectedRec.put("WORKFLOW_STATUS", "N");
						
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Long version= Long.parseLong( selectedRec.get("VERSION")+"");
						++version;
						Map m=new HashMap();
						 selectedRec.put("VERSION",version);
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						//stgUpdatePstmt.setObject(1,++version);
						stgUpdatePstmt.setObject(1,"N");
						stgUpdatePstmt.setObject(2,approverComments);
						stgUpdatePstmt.setObject(3,"N");
						stgUpdatePstmt.setObject(4,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
					
					}*/
					
					
					for(Map<String,Object> selectedRec:records){
						
						String approverComments=userid+" : "+comment;
						int version=Integer.parseInt(selectedRec.get("VERSION").toString());
						version = version+2;
						
						//STG logic
						if(moduleName.equalsIgnoreCase("RECON")){
							String StgAuditInsert="INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?";
							auditInsertPstmt=connection.prepareStatement(StgAuditInsert);
							auditInsertPstmt.setObject(1,selectedRec.get("SID"));
							auditInsertPstmt.executeUpdate();
							System.out.println("record inserted from STG to Audit");
						}
						
						String updateQuery="update "+tableNameStg+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,ACTIVE_INDEX=?,VERSION=?,UPDATED_ON=? "
											+ "from  "+tableNameStg+" where sid = ?";
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						
						stgUpdatePstmt.setObject(1,"N");
						stgUpdatePstmt.setObject(2,approverComments);
						stgUpdatePstmt.setObject(3,"N");
						stgUpdatePstmt.setObject(4,version);
						stgUpdatePstmt.setObject(5,new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stgUpdatePstmt.setObject(6,selectedRec.get("SID"));
						stgUpdatePstmt.executeUpdate();
						System.out.println("STG updated");
						
						
						//Exception logic
						//if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
						if(sourceType !=null && sourceType.equals("INTERNAL")) {
							
							//copy record from Recon table to Audit table
							/*String reconTableToAudit="INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?";
							reconToAuditPstmt=connection.prepareStatement(reconTableToAudit);
							reconToAuditPstmt.setObject(1,selectedRec.get("SID"));
							reconToAuditPstmt.executeUpdate();
							System.out.println("record inserted from Recon to Audit");*/
							
							//updating Recon table
							String updateReconToAudit="update "+reconTableName+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=?,ACTIVE_INDEX=? where SID = ?";
							updateReconPstmt=connection.prepareStatement(updateReconToAudit);
							updateReconPstmt.setObject(1,"N");
							updateReconPstmt.setObject(2,approverComments);
							updateReconPstmt.setObject(3,version);
							updateReconPstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
							updateReconPstmt.setObject(5,"N");
							updateReconPstmt.setObject(6,selectedRec.get("SID"));
							updateReconPstmt.executeUpdate();
							System.out.println("Recon updated");
						}
						
					}
					
					
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, records, "SUPRESS  OPERATION APPROVED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
				
				
			} else if (REJECTED.equalsIgnoreCase(status)) {
				
				String tableNameStg="";
		     	String tableNameAudit="";
		     	String comments = (String) activityDataMap.get("comment");
				if(moduleName.equalsIgnoreCase("UPSTREAM")){
					tableNameStg=integrationName+"_STG_EX";
					tableNameAudit=integrationName+"_STG_AUDIT";
				}else if(moduleName.equalsIgnoreCase("RECON")){
					 tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				//String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=? from  "+tableNameStg+" where sid = ?";
				//String updateQuery="update "+tableNameStg+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=? from  "+tableNameStg+" where sid = ?";
				//Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				//System.out.println(insertQueryConf);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				PreparedStatement reconToAuditPstmt=null;
				PreparedStatement updateReconPstmt=null;
				try{
					/*for(Map<String,Object> selectedRec:records){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String approverComments=userid+" : "+comment;
						selectedRec.put("ACTIVITY_COMMENTS", approverComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Long version= Long.parseLong( selectedRec.get("VERSION")+"");
						++version;
						Map m=new HashMap();
						selectedRec.put("WORKFLOW_STATUS", "N");
						selectedRec.put("VERSION",version);
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						//stgUpdatePstmt.setObject(1,++version);
						stgUpdatePstmt.setObject(1,"N");
						stgUpdatePstmt.setObject(2,approverComments);
						stgUpdatePstmt.setObject(3,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
					
					}*/
					
					for(Map<String,Object> selectedRec:records){
						String approverComments=userid+" : "+comment;
						int version=Integer.parseInt(selectedRec.get("VERSION").toString());
						version = version+2;
						
						if(moduleName.equalsIgnoreCase("RECON")){
							//STG logic
							String StgAuditInsert="INSERT INTO "+tableNameStg+"_AUDIT SELECT * FROM "+tableNameStg+" WHERE SID=?";
							auditInsertPstmt=connection.prepareStatement(StgAuditInsert);
							auditInsertPstmt.setObject(1,selectedRec.get("SID"));
							auditInsertPstmt.executeUpdate();
							System.out.println("record inserted from STG to Audit");
						}
						
						String updateQuery="update "+tableNameStg+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? from  "+tableNameStg+" where sid = ?";
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						stgUpdatePstmt.setObject(1,"N");
						stgUpdatePstmt.setObject(2,approverComments);
						stgUpdatePstmt.setObject(3,version);
						stgUpdatePstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stgUpdatePstmt.setObject(5,selectedRec.get("SID"));
						stgUpdatePstmt.executeUpdate();
						System.out.println("STG updated");
						
						
						//Exception logic
						//if(dsName !=null && (reconTableName+"_UNMATCH").equalsIgnoreCase(dsName)) {
						if(sourceType !=null && sourceType.equals("INTERNAL")) {
							//copy record from Recon table to Audit table
							/*String reconTableToAudit="INSERT INTO "+reconTableName+"_AUDIT SELECT * FROM "+reconTableName+" WHERE SID=?";
							reconToAuditPstmt=connection.prepareStatement(reconTableToAudit);
							reconToAuditPstmt.setObject(1,selectedRec.get("SID"));
							reconToAuditPstmt.executeUpdate();
							System.out.println("record inserted from Recon to Audit");*/
							
							//updating Recon table
							String updateReconToAudit="update "+reconTableName+" set WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,VERSION=?,UPDATED_ON=? where SID = ?";
							updateReconPstmt=connection.prepareStatement(updateReconToAudit);
							updateReconPstmt.setObject(1,"N");
							updateReconPstmt.setObject(2,approverComments);
							updateReconPstmt.setObject(3,version);
							updateReconPstmt.setObject(4,new Timestamp(Calendar.getInstance().getTimeInMillis()));
							updateReconPstmt.setObject(5,selectedRec.get("SID"));
							updateReconPstmt.executeUpdate();
							System.out.println("Recon updated");
						}
						
					}
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, records, "SUPRESS OPERATION REJECTED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}

	@SuppressWarnings("unused")
	private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				int version=   (int) auditRec.get("VERSION");
				++version;
				auditRec.put("WORKFLOW_STATUS",rec.get("WORKFLOW_STATUS"));
				auditRec.put("VERSION",version);
				auditRec.put("ACTIVITY_COMMENTS", rec.get("ACTIVITY_COMMENTS"));
				auditRec.put("ACTIVE_INDEX", rec.get("ACTIVE_INDEX"));
				auditRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);
				insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}
		
	
	
		
	}
	
	
	private void reconProcess(Map<String, Object> paramsMap) {
		// TODO Auto-generated method stub
		
	}
}
/*package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class SuppressPlugIn extends BasicDataSource implements PagesConstants {

	private static final long serialVersionUID = -6870261822605048138L;
	

	public DSResponse executeFetch(final DSRequest request) throws Exception {
		
		Map<String, Object> result = null;
		DSResponse response = new DSResponse();
		Map reqCriteria = request.getValues();
		HttpSession httpSession = request.getHttpServletRequest().getSession();
		User user = (User) httpSession.getAttribute("userId");
		if (user == null) {
			result = new HashMap<String, Object>();
			result.put(STATUS, FAILED);
			result.put(COMMENT, "Session Already Expired, Please Re-Login");
			response.setData(result);
			return response;
		}
		String userId = user.getUserId();
		String userName=user.getUserName();
		String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
		String reconName = (String) httpSession.getAttribute("user_selected_recon");

		List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) reqCriteria.get("selectedRecords");
		String integrationName = (String) reqCriteria.get("integrationName");
		String action = (String) reqCriteria.get("action");
		String comments = (String) reqCriteria.get("comments");
		String moduleName =(String)reqCriteria.get("moduleName");
		String dsName=(String)reqCriteria.get("dsName");
		String centrifugalAmountField="";
		// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
				if(!(moduleName.equalsIgnoreCase("UPSTREAM")) && reqCriteria.get("centrifugalAmount")!=null){
				 centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
				}else if(moduleName.equalsIgnoreCase("UPSTREAM")&& reqCriteria.get("centrifugalAmount")!=null){
					centrifugalAmountField= reqCriteria.get("centrifugalAmount").toString();
				}else{
					centrifugalAmountField=null;
				}
		// action :"SUPPRESS",

		//TODO: operation synchronization.
		if (selectedRecords != null) {

			StringBuilder commentSb = new StringBuilder();
			List<Object> workflowIds = new ArrayList<Object>();
			if (workflowIds.size() > 0) {
				result = new HashMap<String, Object>();
				String commentPrefix = " " ;
				if (workflowIds.size() == 1) {
					commentSb.append("Selected record with SID ");
				} else if (workflowIds.size() > 1) {
					commentSb.append("Selected records with SIDs ");
				}
				for (Object obj : workflowIds) {
					if (commentSb.length() != 0) {
						commentSb.append(",");
					}
					commentSb.append(obj);
				}
				commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
				updateResultStatus(result, FAILED, commentPrefix);
				response.setData(result);
				return response;
			}
						
		}

		Map<String, Object> paramsMap = new HashMap<String, Object>();
		
		paramsMap.put(ACTION, action);
		paramsMap.put(USER_ID, userId);
		paramsMap.put(SELECTED_RECORDS, selectedRecords);
		paramsMap.put(INTEGRATION_NAME, integrationName);
		paramsMap.put(BUSINES_AREA, businesArea);
		paramsMap.put(RECON_NAME, reconName);
		paramsMap.put(COMMENTS, comments);
		paramsMap.put(MODULE, moduleName);
		paramsMap.put(DS_NAME, dsName);
		// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
		//if(centrifugalAmountField!=null){
		paramsMap.put("centrifugalAmountField",centrifugalAmountField);
		//}
		
		result = process(paramsMap);

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);

		return result;
	}

	

	public Map<String, Object> reject() {
		return null;
	}

	public Map<String, Object> approve() {
		return null;
	}

	// will Submit the operation basis on user credintials
	@SuppressWarnings("finally")
	private Map<String, Object> process(Map<String, Object> suppressArgs) {

		Connection connection = null;
		Map<String, Object> result = null;
		try {
			connection = DbUtil.getConnection();
			Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
			Map<String, Object> activityDataMap = new HashMap<String, Object>();

			String userId = (String) suppressArgs.get(USER_ID);
			String dsName = (String) suppressArgs.get(DS_NAME);
			suppressArgs.put(PERSIST_CLASS, SUPPRESS_PLUGIN_CLASS_NAME);
			activityDataMap.put("activity_data", suppressArgs);
		//	String moduleName=(String) suppressArgs.get(MODULE);
			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

			if (userAdminManager.isUserUnderWorkflow(user)) {
				result = new HashMap<String, Object>();

				String activityStatus = PENDING_APPROVAL;

				String businessArea = (String) suppressArgs.get(BUSINES_AREA);
				String reconName = (String) suppressArgs.get(RECON_NAME);
				String comments = (String) suppressArgs.get(COMMENTS);
				String moduleName=(String)suppressArgs.get(MODULE);
				userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
						SUPPRESS_OPERATION, activityDataMap, activityStatus, comments);

				updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				
				String integrationName= (String) suppressArgs.get(INTEGRATION_NAME);
				LoadRegulator loadRegulator=new LoadRegulator();
				InsertRegulator insertRegulator=new InsertRegulator();
				
				String tableNameStg="";
				String tableNameAudit="";
				if(moduleName.equalsIgnoreCase("UPSTREAM")){
					 tableNameStg=integrationName+"_STG_EX";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				
				}else if(moduleName.equalsIgnoreCase("RECON")){
					
				 if(integrationName.equalsIgnoreCase("UnionPay"))
				 {
					 integrationName="CUP";
				 }
				// if(integrationName.equalsIgnoreCase("IMLO_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMLO_PAYMENTS_CREDIT")){
				 if(integrationName.equalsIgnoreCase("IMAL_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMAL_PAYMENTS_CREDIT")){	
				 integrationName="IMLO_PAYMENTS";
						//auditTableName="IMLO_PAYMENTS_STG_AUDIT";
					}else if(integrationName.equalsIgnoreCase("PO") || integrationName.equalsIgnoreCase("TRADE")){
						integrationName="RETAIL_ASSET";
						//auditTableName="RETAIL_ASSET_STG_AUDIT";
					}
				     tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				
				//OperationsUtil.updateQueryConf(tableNameStg,connection);
				String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=? from  "+tableNameStg+" where sid = ?";
				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				try{
					List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) suppressArgs.get(SELECTED_RECORDS);
					for(Map<String,Object> selectedRec:selectedRecords){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String reqComments=user.getUserId()+" : "+(String) suppressArgs.get(COMMENTS);
					//	selectedRec.put("ACTIVITY_COMMENTS", reqComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Map m=new HashMap();
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						Long version= Long.valueOf(selectedRec.get("VERSION")+"");
						++version;
						stgUpdatePstmt.setObject(1,version);
						stgUpdatePstmt.setObject(2,"Y");
						stgUpdatePstmt.setObject(3,reqComments);
						stgUpdatePstmt.setObject(4,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
					}
					
					try{
						String strquery="select email_id from users where user_name='"+user.getReporting()+"'";
						Statement st=connection.createStatement();
						ResultSet rs=st.executeQuery(strquery);
						if(rs.next()){
							String appMailId=rs.getString(1);
							OperationMail mail=	new OperationMail(user.getEmailId(), appMailId);
							mail.sendMail(user.getEmailId(), appMailId);
							mail.sentMail(appMailId, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, selectedRecords, "SUPRESS OPERATION PENDING FOR APPROVAL");
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
				//audit();
				return result;
			} else {

				result = persist(activityDataMap, APPROVED, connection);

			}
		} catch (Exception e) {
			e.printStackTrace();

			updateResultStatus(result, FAILED,OPERATION_FAILED );
		} finally {
			try {
				if (connection != null && !connection.isClosed()) {
					connection.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return result;
		}
		
	}

	public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection
			) {

		Map<String, Object> result = new HashMap<String, Object>();
		LoadRegulator loadRegulator=new LoadRegulator();
		InsertRegulator insertRegulator=new InsertRegulator();
		try {
            String userid=(String) activityDataMap.get("userId");
			String comment=(String) activityDataMap.get("comment");
			Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
			String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
			List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
			String moduleName=(String) activityRecordsMap.get(MODULE);
			String reconName = (String) (activityRecordsMap.get(RECON_NAME));
			connection = DbUtil.getConnection();
			System.out.println(status);
			if (APPROVED.equalsIgnoreCase(status)) {
				String comments = (String) activityDataMap.get("comment");
				String tableNameStg="";
				String tableNameAudit="";
				if(moduleName.equalsIgnoreCase("UPSTREAM")){

					 tableNameStg=integrationName+"_STG_EX";
					 tableNameAudit=integrationName+"_STG_AUDIT";
					
				}else if(moduleName.equalsIgnoreCase("RECON")){
					 if(integrationName.equalsIgnoreCase("UnionPay"))
					 {
						 integrationName="CUP";
					 }  //if(integrationName.equalsIgnoreCase("IMLO_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMLO_PAYMENTS_CREDIT")){
						
						 if(integrationName.equalsIgnoreCase("IMAL_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMAL_PAYMENTS_CREDIT")){
						 integrationName="IMLO_PAYMENTS";
							//auditTableName="IMLO_PAYMENTS_STG_AUDIT";
						}else if(integrationName.equalsIgnoreCase("PO") || integrationName.equalsIgnoreCase("TRADE")){
							integrationName="RETAIL_ASSET";
							//auditTableName="RETAIL_ASSET_STG_AUDIT";
						}
					 tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				//OperationsUtil.updateQueryConf(tableNameStg,connection);
				String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=?,ACTIVE_INDEX=? from  "+tableNameStg+" where sid = ?";
				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				try{
			
					for(Map<String,Object> selectedRec:records){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String approverComments=userid+" : "+comment;
						selectedRec.put("ACTIVITY_COMMENTS", approverComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						selectedRec.put("WORKFLOW_STATUS", "N");
						
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Long version= Long.parseLong( selectedRec.get("VERSION")+"");
						++version;
						Map m=new HashMap();
						 selectedRec.put("VERSION",version);
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						stgUpdatePstmt.setObject(1,++version);
						stgUpdatePstmt.setObject(2,"N");
						stgUpdatePstmt.setObject(3,approverComments);
						stgUpdatePstmt.setObject(4,"N");
						stgUpdatePstmt.setObject(5,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
					
					}
					
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, records, "SUPRESS  OPERATION APPROVED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
				
				
			} else if (REJECTED.equalsIgnoreCase(status)) {
				
				String tableNameStg="";
		     	String tableNameAudit="";
		     	String comments = (String) activityDataMap.get("comment");
				if(moduleName.equalsIgnoreCase("UPSTREAM")){
		
				 tableNameStg=integrationName+"_STG_EX";
				 tableNameAudit=integrationName+"_STG_AUDIT";
				
				
				}else if(moduleName.equalsIgnoreCase("RECON")){
					 if(integrationName.equalsIgnoreCase("UnionPay"))
					 {
						 integrationName="CUP";
					 }
					// if(integrationName.equalsIgnoreCase("IMLO_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMLO_PAYMENTS_CREDIT")){
						 if(integrationName.equalsIgnoreCase("IMAL_PAYMENTS_DEBITS") ||integrationName.equalsIgnoreCase("IMAL_PAYMENTS_CREDIT")){
						 integrationName="IMLO_PAYMENTS";
							//auditTableName="IMLO_PAYMENTS_STG_AUDIT";
						}else if(integrationName.equalsIgnoreCase("PO") || integrationName.equalsIgnoreCase("TRADE")){
							integrationName="RETAIL_ASSET";
							//auditTableName="RETAIL_ASSET_STG_AUDIT";
						}
					 tableNameStg=integrationName+"_STG";
					 tableNameAudit=integrationName+"_STG_AUDIT";
				}
				String updateQuery="update "+tableNameStg+" set version=?,WORKFLOW_STATUS=?,ACTIVITY_COMMENTS=? from  "+tableNameStg+" where sid = ?";
				Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				//System.out.println(insertQueryConf);
				PreparedStatement selectAuditStmt=null;
				PreparedStatement auditInsertPstmt=null;
				PreparedStatement stgUpdatePstmt=null;
				try{
					for(Map<String,Object> selectedRec:records){
						//selectedRec.put("WORKFLOW_STATUS", "Y");
						String approverComments=userid+" : "+comment;
						selectedRec.put("ACTIVITY_COMMENTS", approverComments);
						selectedRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
						stgUpdatePstmt=connection.prepareStatement(updateQuery);
						Long version= Long.parseLong( selectedRec.get("VERSION")+"");
						++version;
						Map m=new HashMap();
						selectedRec.put("WORKFLOW_STATUS", "N");
						selectedRec.put("VERSION",version);
						m.put("PARAM_VALUE_MAP",selectedRec);
						auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
						insertRegulator.insert(auditInsertPstmt, m, insertQueryConf.getQueryParam());
						System.out.println("inserted");
						stgUpdatePstmt.setObject(1,++version);
						stgUpdatePstmt.setObject(2,"N");
						stgUpdatePstmt.setObject(3,approverComments);
						stgUpdatePstmt.setObject(4,selectedRec.get("SID"));
						//stgUpdatePstmt.addBatch();
						stgUpdatePstmt.executeUpdate();
					
					}
					try{
						String apprUserId=(String) activityDataMap.get("userId");
						String makerId=(String) activityDataMap.get("activity_owner");
						String strforApproverquery="select email_id from users where user_id='"+apprUserId+"'";
						
						String strforMakerquery="select email_id from users where user_id='"+makerId+"'";
						String appMailId="";
						String makerEmail="";
						
						try{
							Statement st=connection.createStatement();
							ResultSet rs=st.executeQuery(strforApproverquery);
							if(rs.next()){
								
								appMailId=rs.getString(1);
							}
							st=connection.createStatement();
							 rs=st.executeQuery(strforMakerquery);
							if(rs.next()){
								
								makerEmail=rs.getString(1);
							}
							
							OperationMail mail=	new OperationMail(appMailId, makerEmail);
							mail.sendMail(appMailId, makerEmail);
							mail.sentMail(makerEmail, "MODULE NAME :"+reconName  +"  <BR/>COMENTS BY USER :   "+comments, records, "SUPRESS OPERATION REJECTED");
					
						}catch(Exception e){
							
						}
					}catch(Exception e){
						
					}
					//stgUpdatePstmt.executeBatch();
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					DbUtil.closePreparedStatement(selectAuditStmt);
					DbUtil.closePreparedStatement(auditInsertPstmt);
					DbUtil.closePreparedStatement(stgUpdatePstmt);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return result;
	}

	@SuppressWarnings("unused")
	private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
			PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
					throws ClassNotFoundException, SQLException {
		String QueryParam=insertQueryConf.getQueryParam();
		List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
		
		if(auditData!=null){
			for(Map<String,Object> auditRec:auditData){
				Map paramValueMap=new HashMap();
				int version=   (int) auditRec.get("VERSION");
				++version;
				auditRec.put("WORKFLOW_STATUS",rec.get("WORKFLOW_STATUS"));
				auditRec.put("VERSION",version);
				auditRec.put("ACTIVITY_COMMENTS", rec.get("ACTIVITY_COMMENTS"));
				auditRec.put("ACTIVE_INDEX", rec.get("ACTIVE_INDEX"));
				auditRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
				paramValueMap.put("PARAM_VALUE_MAP", auditRec);
				insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
			}
		}
		
	
	
		
	}



}
*/