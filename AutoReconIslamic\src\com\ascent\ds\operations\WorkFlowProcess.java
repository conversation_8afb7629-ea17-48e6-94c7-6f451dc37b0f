package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.DbCursor;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class WorkFlowProcess extends BasicDataSource implements PagesConstants {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@SuppressWarnings("unused")
	public WorkFlowProcess() {
	}

	@SuppressWarnings("unchecked")
	Map<String, Object> result = null;

	@SuppressWarnings("unchecked")
	public DSResponse executeFetch(final DSRequest request) throws Exception {

		DSResponse response = new DSResponse();
		try {
			HttpSession httpSession = request.getHttpServletRequest().getSession();
			User loginUserId = (User) httpSession.getAttribute("userId");
			String userId = loginUserId.getUserId();

			if (userId == null) {
				result = new HashMap<String, Object>();
				result.put(STATUS, FAILED);
				result.put(COMMENT, "Session Already Expired, Please Re-Login");

				response.setData(result);

				return response;
			}

			@SuppressWarnings("rawtypes")

			Map map = request.getCriteria();
			String comments = (String) map.get("comment");
			String submitType;
			Map<String, Object> data = new HashMap<String, Object>();
			List<Map<String, Object>> activity = new ArrayList<Map<String, Object>>();
			submitType = (String) map.get("submitType");
			List<Map<String, Object>> records = new ArrayList<Map<String, Object>>();
			if (submitType.equalsIgnoreCase(RE_SUBMIT)) {
				/*
				 * java.util.Date str=(
				 * java.util.Date)resubmitMap.get("created_on");
				 * java.sql.Timestamp created_on = new
				 * java.sql.Timestamp(str.getTime());
				 * resubmitMap.put("created_on",created_on);
				 */
			} else {
				records = (List<Map<String, Object>>) map.get("selectedData");
			}
			Long activityId = -1L;
			int activityLevel = -1;
			if (submitType.equalsIgnoreCase(RE_SUBMIT)) {
				/*
				 * activityId = (Long) resubmitMap.get("activity_id");
				 * activityLevel = ((Long) resubmitMap.get("activity_level"))
				 * .intValue(); processActivity(activityId, activityLevel,
				 * submitType, comment, loginUserId.getUserId(), resubmitMap,
				 * true);
				 */} else {
				for (Map<String, Object> selectedRecord : records) {
					try {
						activityId = (Long) selectedRecord.get("activity_id");
						activityLevel = ((Long) selectedRecord.get("activity_level")).intValue();
						//System.out.println(activityLevel);
						result = processActivity(activityId, activityLevel, submitType, comments,
								loginUserId.getUserId(), selectedRecord, false);
					} catch (Exception e) {
						e.printStackTrace();
						throw new Exception("Unable to process activity with Activity Id:" + activityId
								+ " Activity Level: " + activityLevel, e);
					}
					
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);
		return result;
	}

	private Map<String, Object> processActivity(Long activityId, Integer currentActivityLevel, String action,
			String comment, String userId, Map<String, Object> selectedRecord, boolean isReSubmit) {
		Connection connection = null;
		try {
		LoadRegulator loadRegulator = new LoadRegulator();
		
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		Query query = ascentWebMetaInstance.getWebQueryConfs().getQueryConf("select_recon_activity_flow");
		Map<String, Object> parmMap = new HashMap<String, Object>();
		Map<String, Object> parmValueMap = new HashMap<String, Object>();
		parmMap.put("PARAM_VALUE_MAP", parmValueMap);
		connection = DbUtil.getConnection();
		DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);
		List<Map<String, Object>> rolesRecordsTemp = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
		Map<String, Object> statusMap=new HashMap<String, Object>();
		
		while (recordsTemp.size() > 0) {
			rolesRecordsTemp.addAll(recordsTemp);
			recordsTemp.clear();
			recordsTemp = dbCursor.getNextBatch();
		}
		//System.out.println(selectedRecord.get("activity_id"));
		
		for(int i=0;i<rolesRecordsTemp.size();i++){
			Map<String, Object> checkingMap=rolesRecordsTemp.get(i);
			//System.out.println("activity_id---"+checkingMap.get("activity_id")+"      recent_actor---"+checkingMap.get("recent_actor"));
			if(checkingMap.get("activity_id").equals(activityId)){
				return updateResultStatus(statusMap, "This activity is already"+ (String)checkingMap.get("status") +"by the user   ",(String)checkingMap.get("recent_actor"));
			}
			}
		if (comment != null) {
		}
		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		//System.out.println("submit Type of the button" + action);
		if (action.equalsIgnoreCase("APPROVED")) {
			result = new HashMap<String, Object>();
			updateResultStatus(result, APPROVED, "the data successfully");
		} else if (action.equalsIgnoreCase("REJECTED")) {
			result = new HashMap<String, Object>();
			updateResultStatus(result, REJECTED, "the data successfully");
		}
		userAdminManager.processActivity(activityId, currentActivityLevel, action, userId, comment, null);
		
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			try {
				connection.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return result;
		
	}
		
}