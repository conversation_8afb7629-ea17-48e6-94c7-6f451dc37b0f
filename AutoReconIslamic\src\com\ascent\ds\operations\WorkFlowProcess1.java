package com.ascent.ds.operations;

import java.io.ByteArrayInputStream;

import java.io.ObjectInputStream;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpSession;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.DbCursor;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dao.CustomerDao;
import com.ascent.service.dto.User;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class WorkFlowProcess1 extends BasicDataSource implements PagesConstants {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@SuppressWarnings("unused")
	public WorkFlowProcess1() {
	}

	@SuppressWarnings("unchecked")
	Map<String, Object> result = null;

	@SuppressWarnings("unchecked")
	public DSResponse executeFetch(final DSRequest request) throws Exception {

		DSResponse response = new DSResponse();
		try {
			HttpSession httpSession = request.getHttpServletRequest().getSession();
			User loginUserId = (User) httpSession.getAttribute("userId");
			String userId = loginUserId.getUserId();

			if (userId == null) {
				result = new HashMap<String, Object>();
				result.put(STATUS, FAILED);
				result.put(COMMENT, "Session Already Expired, Please Re-Login");

				response.setData(result);

				return response;
			}

			@SuppressWarnings("rawtypes")

			Map map = request.getCriteria();
			String comments = (String) map.get("comment");
			String submitType;
			Map<String, Object> data = new HashMap<String, Object>();
			List<Map<String, Object>> activity = new ArrayList<Map<String, Object>>();
			submitType = (String) map.get("submitType");
			List<Map<String, Object>> records = new ArrayList<Map<String, Object>>();
			if (submitType.equalsIgnoreCase(RE_SUBMIT)) {
				/*
				 * java.util.Date str=(
				 * java.util.Date)resubmitMap.get("created_on");
				 * java.sql.Timestamp created_on = new
				 * java.sql.Timestamp(str.getTime());
				 * resubmitMap.put("created_on",created_on);
				 */
			} else {
				records = (List<Map<String, Object>>) map.get("selectedData");
			}
			Long activityId = -1L;
			int activityLevel = -1;
			if (submitType.equalsIgnoreCase(RE_SUBMIT)) {
				/*
				 * activityId = (Long) resubmitMap.get("activity_id");
				 * activityLevel = ((Long) resubmitMap.get("activity_level"))
				 * .intValue(); processActivity(activityId, activityLevel,
				 * submitType, comment, loginUserId.getUserId(), resubmitMap,
				 * true);
				 */} else {
				for (Map<String, Object> selectedRecord : records) {
					try {
						activityId = (Long) selectedRecord.get("activity_id");
						activityLevel = ((Long) selectedRecord.get("activity_level")).intValue();
						System.out.println(activityLevel);
						result = processActivity(activityId, activityLevel, submitType, comments,
								loginUserId.getUserId(), selectedRecord, false);
					} catch (Exception e) {
						e.printStackTrace();
						throw new Exception("Unable to process activity with Activity Id:" + activityId
								+ " Activity Level: " + activityLevel, e);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		response.setData(result);
		return response;
	}

	private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
		result.put(STATUS, status);
		result.put(COMMENT, comment);
		return result;
	}

	private Map<String, Object> processActivity(Long activityId, Integer currentActivityLevel, String action,
			String comment, String userId, Map<String, Object> selectedRecord, boolean isReSubmit) throws Exception {

		LoadRegulator loadRegulator = new LoadRegulator();
		Connection connection = null;

		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();

		Queries queries = ascentWebMetaInstance.getWebQueryConfs();

		Query query = queries.getQueryConf("select_recon_activity_flow");

		Map<String, Object> parmMap = new HashMap<String, Object>();
		Map<String, Object> parmValueMap = new HashMap<String, Object>();
		parmMap.put("PARAM_VALUE_MAP", parmValueMap);

		connection = DbUtil.getConnection();
		DbCursor dbCursor = loadRegulator.load(connection, query, parmMap);

		List<Map<String, Object>> rolesRecordsTemp = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> recordsTemp = dbCursor.getNextBatch();
		while (recordsTemp.size() > 0) {
			rolesRecordsTemp.addAll(recordsTemp);
			recordsTemp.clear();
			recordsTemp = dbCursor.getNextBatch();
		}
		String ssss = "";

		boolean flag = false;

		Map<String, Object> activityData = (Map<String, Object>) selectedRecord.get("activity_data");
		String activityType = (String) selectedRecord.get("activity_type");//

		java.lang.Long Sid = null;
		if (activityData.get("SELECTED_RECORDS") instanceof java.util.ArrayList) {
			List SELECTED_RECORDS = (List) activityData.get("SELECTED_RECORDS");
			boolean flag1 = false;
			for (Object obj : SELECTED_RECORDS) {

				Map<String, Object> m = (Map<String, Object>) obj;

				Sid = (java.lang.Long) m.get("SID");
				;
				flag1 = true;
				break;
			}
		} else if (activityData.get("SELECTED_RECORDS") instanceof org.apache.commons.collections.map.LinkedMap) {
			Map<String, Object> SELECTED_RECORDS = (Map<String, Object>) activityData.get("SELECTED_RECORDS");

			Set<String> keySet = SELECTED_RECORDS.keySet();
			String stringId = "";
			for (String string : keySet) {
				List ListOfMap = (List) SELECTED_RECORDS.get(string);
				boolean flag1 = false;
				for (Object object : ListOfMap) {
					Map<String, Object> mapSS = (Map<String, Object>) object;
					Sid = (java.lang.Long) mapSS.get("SID");
					;
					flag1 = true;
					break;
				}
				if (flag1) {
					break;
				}
			}
		}

		java.lang.Long SidTemp = null;

		for (Map<String, Object> map : rolesRecordsTemp) {
			String activityTypeFromDb = (String) map.get("activity_type");
			if (activityTypeFromDb.equalsIgnoreCase(activityType)) {
				ByteArrayInputStream bis = new ByteArrayInputStream((byte[]) map.get("activity_data"));
				ObjectInputStream ois = new ObjectInputStream(bis);
				Object obj = ois.readUnshared();

				Map<String, Object> map2 = (Map<String, Object>) obj;

				bis.close();
				ois.close();

				if (map2.get("SELECTED_RECORDS") instanceof org.apache.commons.collections.map.LinkedMap) {
					org.apache.commons.collections.map.LinkedMap linkedMap = (org.apache.commons.collections.map.LinkedMap) map2
							.get("SELECTED_RECORDS");

					Set keySet2 = linkedMap.keySet();
					for (Object object : keySet2) {
						String stringKey = (String) object;
						boolean tempFlag = false;
						List listLinkedMap = (List) linkedMap.get(stringKey);
						for (Object object2 : listLinkedMap) {
							Map<String, Object> mapLinkedMap = (Map<String, Object>) object2;
							SidTemp = (java.lang.Long) mapLinkedMap.get("SID");

							tempFlag = true;
							break;
						}

						if (tempFlag) {
							if (SidTemp.equals(Sid)) {
								flag = true;
								break;
							}

						}

					}

				} else if (map2.get("SELECTED_RECORDS") instanceof java.util.ArrayList) {
					System.out.println(map2.get("SELECTED_RECORDS") instanceof java.util.ArrayList);
					List list = (List) map2.get("SELECTED_RECORDS");
					for (Object object : list) {
						Map<String, Object> mapPravin = (Map<String, Object>) object;
						SidTemp = (java.lang.Long) mapPravin.get("SID");
						if (SidTemp.equals(Sid)) {
							flag = true;
							break;
						}

					}
					if (flag) {
						break;
					}

				}

			}

			if (flag) {
				System.out.println("Already Exist");

				Map<String, Object> mapRes = new HashMap<String, Object>();
				CustomerDao customerDao = new CustomerDao();
				mapRes.put("active_index", "p");
				mapRes.put("activity_id", activityId);
				Connection conn = DbUtil.getConnection();
				customerDao.update(conn, mapRes, "UpdateReconActivity");

				return updateResultStatus(mapRes, "", "This activity is already Approved ");
			}
		}

		if (isReSubmit) {
			/*
			 * if (comment != null) { } UserAdminManager userAdminManager =
			 * UserAdminManager .getAuthorizationManagerSingleTon();
			 * userAdminManager.processActivity(activityId,
			 * currentActivityLevel, action, userId, comment, selectedRecord);
			 */
			} else {
			if (comment != null) {
			}
			UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
			System.out.println("submit Type of the button" + action);
			if (action.equalsIgnoreCase("APPROVED")) {
				result = new HashMap<String, Object>();
				updateResultStatus(result, APPROVED, "THE DATA SUCCESSFULLY");
			} else if (action.equalsIgnoreCase("REJECTED")) {
				result = new HashMap<String, Object>();
				updateResultStatus(result, REJECTED, "THE DATA SUCCESSFULLY");
			}
			userAdminManager.processActivity(activityId, currentActivityLevel, action, userId, comment, null);
		}

		return result;
	}
}