package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ascent.integration.util.DbUtil;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class ReasonData extends BasicDataSource{

	private static final long serialVersionUID = 1L;
	public static final String GET_RESON_FOR_SPECIFIC_GL="select * from Reason where GL_ID=(select GL_ID from GL where GL=?)";
	static Connection connection=null;
	public ReasonData() {
	}
public DSResponse executeFetch(final DSRequest request)throws Exception
{
	DSResponse response=new DSResponse();
	Map dataMap=request.getValues();
	String exception=(String) dataMap.get("exception");
	if(exception.equalsIgnoreCase("no"))
	{
		List<Map<String, Object>> reasonDataList=ReasonData.getReasonDataForSpecificGL(dataMap);
		System.out.println("reasonDataList :" + reasonDataList);
		response.setData(reasonDataList);
	}
	else
	{
		List<Map<String, Object>> reasonDataList=ReasonData.getReasonDataForSpecificReconSide(dataMap);
		response.setData(reasonDataList);
	}
	
	return response;
}

public static List<Map<String, Object>> getReasonDataForSpecificGL(Map  dataMap){
	List<Map<String, Object>> reasonDataList=new ArrayList<Map<String, Object>>();
	Map<String, Object> reasonDataMap=null;
	String gl_code=(String) dataMap.get("glcode");
	try
	{
		connection=DbUtil.getConnection();
		PreparedStatement pstmt=connection.prepareStatement(GET_RESON_FOR_SPECIFIC_GL);
		pstmt.setString(1, gl_code);
		ResultSet rs=pstmt.executeQuery();
	    while (rs.next()) {
			System.out.println("Reason&&&&&&&&&&&&&&&&& :" + rs.getString("Reason"));
			 reasonDataMap = new HashMap<String,Object>(); 
			 reasonDataMap.put("Reason",rs.getString("Reason"));
			 reasonDataList.add(reasonDataMap);
		}
		//System.out.println("reasonDataMap"+reasonDataMap);
	    //connection.close();
	}
	catch(Exception e)
	{
		e.printStackTrace();
	}
	finally {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	return reasonDataList;
}
public static List<Map<String, Object>> getReasonDataForSpecificReconSide(Map dataMap)
{
	List<Map<String, Object>> reasonDataList=new ArrayList<Map<String, Object>>();
	Map<String, Object> reasonDataMap=null;
	String gl_code=(String) dataMap.get("recon_side");
	try{
		
		 String str[]=gl_code.split("_");
		  String newStr=str[1];
		  if(newStr.equalsIgnoreCase("2247"))
		  {
			  newStr=newStr+"/0";
		  }else if(newStr.equalsIgnoreCase("1472"))
		  {
			  newStr=newStr+"/0";
		  }else if(newStr.equalsIgnoreCase("1482"))
		  {
			  newStr=newStr+"/0";
		  }
		connection=DbUtil.getConnection();
		PreparedStatement pstmt=connection.prepareStatement(GET_RESON_FOR_SPECIFIC_GL);
		pstmt.setString(1, newStr);
		ResultSet rs=pstmt.executeQuery();
		 while (rs.next()) {
				
			 reasonDataMap = new HashMap<String,Object>(); 
			 reasonDataMap.put("Reason",rs.getString("Reason"));
			reasonDataList.add(reasonDataMap);
		}
		connection.close();
	}catch(Exception e)
	{
		e.printStackTrace();
	}
	return reasonDataList;
}
}
