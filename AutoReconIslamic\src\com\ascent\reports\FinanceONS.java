package com.ascent.reports;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.persistance.LoadRegulator;

public class FinanceONS {
	
	private static Logger logger = LogManager.getLogger(FinanceONS.class.getName());
	
	private static final String ONS_INTERNAL_RECONCILED_RECON = "ONS_INTERNAL_RECONCILED_RECON";
	private static final String ONS_EXTERNAL_RECONCILED_RECON = "ONS_EXTERNAL_RECONCILED_RECON";
	private static final String ONS_INTERNAL_UNRECONCILED_RECON = "ONS_INTERNAL_UNRECONCILED_RECON";
	private static final String ONS_EXTERNAL_UNRECONCILED_RECON = "ONS_EXTERNAL_UNRECONCILED_RECON";
	LoadRegulator loadRegulator = new LoadRegulator();
	String dbUser;
	String dbURL;
	String dbPassword;

	AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
	Queries queryConfs = ascentWebMetaInstance.getWebQueryConfs();
	Queries queries = ascentWebMetaInstance.getWebQueryConfs();

	public void ReportsJDBCConnection(HttpServletRequest request) {
		
		ResourceBundle bundle = ResourceBundle.getBundle("local.db", Locale.getDefault());

		//String driver = bundle.getString("driver");
		String dataBaseName = bundle.getString("dataBaseName");
		String db_server = bundle.getString("db_server");
		String url = bundle.getString("url");
		url = url.replace("db_server", db_server);
		dbURL = url.replace("dataBaseName", dataBaseName);
		dbUser = bundle.getString("username");
		dbPassword = bundle.getString("password");

	}

	public List<Map<String, Object>> OnsInternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE NUMBER", rset.getString(1));
				map.put("INTERNAL REFERENCE NUMBER", rset.getString(2));
				map.put("TRANSACTION AMOUNT", rset.getString(3));
				map.put("TRANSACTION DATE", rset.getString(4));
				map.put("TRAN PARTICULAR", rset.getString(5));
				map.put("TRAN REMARKS", rset.getString(6));
				map.put("DEB/CR INDICATOR", rset.getString(7));
				map.put("ACCOUNT NUMBER", rset.getString(8));
				map.put("CARD NUMBER", rset.getString(9));
				map.put("ACCOUNT BRANCH ID", rset.getString(10));

				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public List<Map<String, Object>> OnsExternalReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_RECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE NUMBER", rset.getString(1));
				map.put("INTERNAL REFERENCE NUMBER", rset.getString(2));
				map.put("TRANSACTION AMOUNT", rset.getString(3));
				map.put("TRANSACTION DATE", rset.getString(4));
				map.put("TRAN PARTICULAR", rset.getString(5));
				map.put("TRAN REMARKS", rset.getString(6));
				map.put("DEB/CR INDICATOR", rset.getString(7));
				map.put("ACCOUNT NUMBER", rset.getString(8));
				map.put("PAN NUMBER", rset.getString(9));
				map.put("ORIGINATOR BID", rset.getString(10));
				map.put("DESTINATION BID", rset.getString(11));
				map.put("ACQUIRING INSTITUTION ID", rset.getString(12));
				map.put("CARD_ACCEPTOR_NAME", rset.getString(13));
				map.put("MERCHANT_CATEGORY_CODE", rset.getString(14));
				map.put("TRANSACTION_TYPE", rset.getString(15));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> OnsInternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsInternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_INTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE NUMBER", rset.getString(1));
				map.put("INTERNAL REFERENCE NUMBER", rset.getString(2));
				map.put("TRANSACTION AMOUNT", rset.getString(3));
				map.put("TRANSACTION DATE", rset.getString(4));
				map.put("TRAN PARTICULAR", rset.getString(5));
				map.put("TRAN REMARKS", rset.getString(6));
				map.put("DEB/CR INDICATOR", rset.getString(7));
				map.put("ACCOUNT NUMBER", rset.getString(8));
				map.put("CARD NUMBER", rset.getString(9));
				map.put("ACCOUNT BRANCH ID", rset.getString(10));

				list.add(map);
				//logger.debug("OnsInternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}
	
	public List<Map<String, Object>> OnsExternalUnReconsiledMethod(String fromDate, String toDate) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		Connection connection = DbUtil.getConnection();
		logger.debug("Fetching OnsExternalUnReconsiled data..");

		try {

			Query queryConf = ascentWebMetaInstance.getWebQueryConfs().getQueryConf(ONS_EXTERNAL_UNRECONCILED_RECON);
			String query = queryConf.getQueryString();
			//logger.debug("query. : "+query);
			PreparedStatement pstmt = connection.prepareStatement(query);
			pstmt.setString(1, fromDate);
			pstmt.setString(2, toDate);
			ResultSet rset = pstmt.executeQuery();
			while (rset.next()) {
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("REFERENCE NUMBER", rset.getString(1));
				map.put("INTERNAL REFERENCE NUMBER", rset.getString(2));
				map.put("TRANSACTION AMOUNT", rset.getString(3));
				map.put("TRANSACTION DATE", rset.getString(4));
				map.put("TRAN PARTICULAR", rset.getString(5));
				map.put("TRAN REMARKS", rset.getString(6));
				map.put("DEB/CR INDICATOR", rset.getString(7));
				map.put("ACCOUNT NUMBER", rset.getString(8));
				map.put("PAN NUMBER", rset.getString(9));
				map.put("ORIGINATOR BID", rset.getString(10));
				map.put("DESTINATION BID", rset.getString(11));
				map.put("ACQUIRING INSTITUTION ID", rset.getString(12));
				map.put("CARD_ACCEPTOR_NAME", rset.getString(13));
				map.put("MERCHANT_CATEGORY_CODE", rset.getString(14));
				map.put("TRANSACTION_TYPE", rset.getString(15));

				list.add(map);
				//logger.debug("OnsExternalReconsiled : "+list);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return list;
	}

	public static void main(String[] args) {
		FinanceONS c = new FinanceONS();
		c.OnsInternalReconsiledMethod("2018-01-01", "2018-10-01");
		c.OnsExternalReconsiledMethod("2018-01-01", "2018-10-01");
	}

}
