package com.ascent.ds.operations;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.velocity.runtime.parser.node.PutExecutor;

import com.ascent.admin.authorize.UserAdminManager;
import com.ascent.boot.web.AscentWebMetaInstance;
import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;
import com.ascent.custumize.query.Query;
import com.ascent.integration.util.DbUtil;
import com.ascent.integration.util.PersistanceUtil;
import com.ascent.persistance.InsertRegulator;
import com.ascent.persistance.LoadRegulator;
import com.ascent.service.dto.User;
import com.ascent.util.OperationsUtil;
import com.ascent.util.PagesConstants;
import com.isomorphic.datasource.BasicDataSource;
import com.isomorphic.datasource.DSRequest;
import com.isomorphic.datasource.DSResponse;

public class CreateGLEntryPlugIn extends BasicDataSource implements PagesConstants{
	private static Logger logger = LogManager.getLogger(CreateGLEntryPlugIn.class.getName());
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public CreateGLEntryPlugIn() 
	{
	}
public DSResponse executeFetch(final DSRequest request)throws Exception 
{
  
  Map<String, Object> result = null;
  Map dataMap=request.getCriteria();
  DSResponse response=new DSResponse();
  Connection connection=DbUtil.getConnection();
 
  HttpSession httpSession = request.getHttpServletRequest().getSession();

	User user = (User) httpSession.getAttribute("userId");

	if (user == null) {
		result = new HashMap<String, Object>();
		result.put(STATUS, FAILED);
		result.put(COMMENT, "Session Already Expired, Please Re-Login");
		response.setData(result);
		logger.trace("Session Already Expired, Please Re-Login");
		return response;
		
	}
	
	String userId = user.getUserId();
	String userName=user.getUserName();
	String businesArea = (String) httpSession.getAttribute("user_selected_business_area");
	String reconName = (String) httpSession.getAttribute("user_selected_recon");

	List<Map<String, Object>> selectedRecords = (List<Map<String, Object>>) dataMap.get("selectedRecords");
	String integrationName = (String) dataMap.get("integrationName");
	String action = (String) dataMap.get("action");
	String comments = (String) dataMap.get("comments");
	String moduleName =(String)dataMap.get("moduleName");
	String reconDataSource =(String)dataMap.get("reconDataSource");
	String dsName=(String)dataMap.get("dsName");
	String glcode=(String)dataMap.get("glcode");
	String accCatgeory=(String)dataMap.get("accCatgeory");
	String remarks = (String)dataMap.get("remarks");
	Map debitCreditAccDetails=(Map) dataMap.get("debitCreditAccDetails");
	
	System.out.println("debitCreditAccDetails :" + debitCreditAccDetails);
	// GETTING CENTRIFUGALAMOUNT FROM RECON SUMMARY TAB  THROUGH reqCritreia MAP 
			String centrifugalAmountField= dataMap.get("centrifugalAmount").toString();
	
	if (selectedRecords != null) {
	//	Connection con=DbUtil.getConnection();
	//	String auditTable=integrationName+"_STG_AUDIT";
	//	Map record=selectedRecords[0];
		//String query="INSERT INTO "+auditTable
		StringBuilder commentSb = new StringBuilder();
		List<Object> workflowIds = new ArrayList<Object>();
		for (Map<String, Object> rec : selectedRecords) {
			if ((rec != null && rec.get("WORKFLOW_STATUS") != null && "No".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS"))) || (rec != null && rec.get("WORKFLOW_STATUS") != null && "N".equalsIgnoreCase((String) rec.get("WORKFLOW_STATUS")))) {
				
			}else{
				workflowIds.add(rec.get(SID));
			}
       }
		if (workflowIds.size() > 0) {
			result = new HashMap<String, Object>();
			String commentPrefix = " ";
			if (workflowIds.size() == 1) {
				commentSb.append("Selected record with SID ");
			} else if (workflowIds.size() > 1) {
				commentSb.append("Selected records with SIDs ");
			}
			for (Object obj : workflowIds) {
				if (commentSb.length() != 0) {
					commentSb.append(",");
				}
				commentSb.append(obj);
			}
			commentPrefix = commentPrefix + commentSb.toString()+" are already Under WorkFlow";
			updateResultStatus(result, FAILED, commentPrefix);
			response.setData(result);
			logger.trace(commentPrefix);
			return response;
		}
	//con.close();				
	}
	Map<String, Object> paramsMap = new HashMap<String, Object>();
	
	paramsMap.put(ACTION, action);
	paramsMap.put(USER_ID, userId);
	paramsMap.put(SELECTED_RECORDS, selectedRecords);
	paramsMap.put(INTEGRATION_NAME, integrationName);
	paramsMap.put(BUSINES_AREA, businesArea);
	paramsMap.put(RECON_NAME, reconName);
	paramsMap.put(COMMENTS, comments);
	paramsMap.put(MODULE, moduleName);
	paramsMap.put(DS_NAME, dsName);
	paramsMap.put(GL_CODE, glcode);
	paramsMap.put(ACC_CATEGORY, accCatgeory);
	paramsMap.put("REMARKS", remarks);
	paramsMap.put(DEBIT_CREDIT_DETAILS, debitCreditAccDetails);
	paramsMap.put("reconDataSource", reconDataSource);
	
	// KEEPING(PUT) centrifugalAmountFiled IN paramsMap
	if(centrifugalAmountField!=null){
		paramsMap.put("centrifugalAmountField",centrifugalAmountField);
	}
	result = process(paramsMap);

	response.setData(result);
  return response;
}
private Map<String, Object> updateResultStatus(Map<String, Object> result, String status, String comment) {
	result.put(STATUS, status);
	result.put(COMMENT, comment);

	return result;
}
@SuppressWarnings("finally")
private Map<String, Object> process(Map<String, Object> createGLEntryArgs) {

	Connection connection = null;
	Map<String, Object> result = null;
	try {
		connection = DbUtil.getConnection();
		Map<String, Object> activityDataInfoMap = new HashMap<String, Object>();
		Map<String, Object> activityDataMap = new HashMap<String, Object>();

		String userId = (String) createGLEntryArgs.get(USER_ID);
		String dsName = (String) createGLEntryArgs.get(DS_NAME);
		createGLEntryArgs.put(PERSIST_CLASS, CREATE_GL_ENTRY_PLUGIN_CLASS_NAME);
		activityDataMap.put("activity_data", createGLEntryArgs);
		
		

		UserAdminManager userAdminManager = UserAdminManager.getAuthorizationManagerSingleTon();
		User user = userAdminManager.getUsercontroller().getUsers().getUser(userId);

		if (userAdminManager.isUserUnderWorkflow(user)) {
			result = new HashMap<String, Object>();

			String activityStatus = PENDING_APPROVAL;

			String businessArea = (String) createGLEntryArgs.get(BUSINES_AREA);
			String reconName = (String) createGLEntryArgs.get(RECON_NAME);
			String comments = (String) createGLEntryArgs.get(COMMENTS);
			String moduleName=(String)createGLEntryArgs.get(MODULE);
			userAdminManager.createActivity(connection, user, businessArea, reconName, moduleName,
					CREATE_GL_ENTRY_OPERATION, activityDataMap, activityStatus, comments);

			
			
			String integrationName= (String) createGLEntryArgs.get(INTEGRATION_NAME);
			LoadRegulator loadRegulator=new LoadRegulator();
			InsertRegulator insertRegulator=new InsertRegulator();
		
			String tableNameStg=integrationName+"_STG";
			String tableNameStgAudit=integrationName+"_STG_AUDIT";
		//	String tableNameAudit=integrationName+"_STG_AUDIT";
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameStgAudit, connection);
			
	//		System.out.println(insertQueryConf);
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement stagingUpdatePstmt=null;
			try{
				List<Map<String,Object>> selectedRecords=(List<Map<String,Object>> ) createGLEntryArgs.get(SELECTED_RECORDS);
				
				for(Map<String,Object> selectedRec:selectedRecords){
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedRec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameStgAudit, connection);
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					Map paramValueMap=new HashMap();
					for(Map rec : auditData){
						
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}
				
					
					//System.out.println("vbnc");
				
					
					
					long version=(long) selectedRec.get("VERSION");
					++version;
					selectedRec.put("VERSION", version);
					selectedRec.put("WORKFLOW_STATUS", "Y");
					String reqComments=user.getUserId()+":"+(String) createGLEntryArgs.get(COMMENTS);
					selectedRec.put("ACTIVITY_COMMENTS", reqComments);
					long sid=(long) selectedRec.get("SID");
					
					stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='Y',ACTIVITY_COMMENTS=?,VERSION="+version+" WHERE SID="+sid);
					stagingUpdatePstmt.setObject(1, reqComments);
					
					int rows=	stagingUpdatePstmt.executeUpdate();
				
				//	selectAuditStmt=connection.prepareStatement(auditSelectQry);
				//	auditInsertPstmt=connection.prepareStatement(insertQueryConf.getQueryString());
			      //audit(loadRegulator, insertRegulator, insertQueryConf, selectAuditStmt, auditInsertPstmt, selectedRec);
					
					updateResultStatus(result, SUCCESS, TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
					logger.trace(SUCCESS+" "+TRANSACTIONS_SUBMITTED_FOR_APPROVAL_SUCESSFULLY);
				}
			}catch(Exception e){
				//e.printStackTrace();
				logger.error("Error : "+e);
			}finally{
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closeConnection(connection);
				
				
			}
			return result;
		} else {

		

			result = persist(activityDataMap, APPROVED, connection);

		}
	} catch (Exception e) {
		//e.printStackTrace();
		logger.error("Error :"+FAILED+" "+OPERATION_FAILED+" "+e);
		updateResultStatus(result, FAILED,OPERATION_FAILED );
	} finally {
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
		//	e.printStackTrace();
			logger.error("Error :"+e);
		}
		return result;
	}
	
}
public Map<String, Object> persist(Map<String, Object> activityDataMap, String status, Connection connection) {

	Map<String, Object> result = new HashMap<String, Object>();
	LoadRegulator loadRegulator=new LoadRegulator();
	InsertRegulator insertRegulator=new InsertRegulator();
	try {
		AscentWebMetaInstance ascentWebMetaInstance = AscentWebMetaInstance.getInstance();
		
		Queries queries = ascentWebMetaInstance.getWebQueryConfs();
        String userid=(String) activityDataMap.get("userId");
		String comment=(String) activityDataMap.get("comment");
		Map activityRecordsMap= (Map) activityDataMap.get("activity_data");
		String integrationName= (String) activityRecordsMap.get(INTEGRATION_NAME);
		List<Map<String, Object>> records = (List<Map<String, Object>>) activityRecordsMap.get(SELECTED_RECORDS);
		List<Map<String, Object>> glList=new ArrayList<Map<String, Object>>();
		String reconDataSource=(String) activityRecordsMap.get("reconDataSource");
		String reconTableName=reconDataSource.substring(0, reconDataSource.length()-14);
		Map<String,Object> DEBITCREDITDETAILS=(Map<String, Object>) activityRecordsMap.get(DEBIT_CREDIT_DETAILS);
		
		System.out.println("DEBITCREDITDETAILS :" + DEBITCREDITDETAILS);
		connection = DbUtil.getConnection();
		
		String businessArea = (String) activityRecordsMap.get(BUSINES_AREA);
		String reconName = (String) activityRecordsMap.get(RECON_NAME);
		String comments = (String) activityRecordsMap.get(COMMENTS);
		String remarks=(String)activityRecordsMap.get("REMARKS");
		String tableNameStg=integrationName+"_STG";
		String tableNameAudit=integrationName+"_STG_AUDIT";
		if(integrationName.equalsIgnoreCase("AUTH_ISS")){
			
			tableNameStg="AUTH_ISSUER_STG";
			tableNameAudit="AUTH_ISSUER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("AUTH_ACQ")){
			
			tableNameStg="AUTH_ACQUIRER_STG";
			tableNameAudit="AUTH_ACQUIRER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("MAST")){
			
			tableNameStg="MASTER_STG";
			tableNameAudit="MASTER_STG_AUDIT";
		}else if(integrationName.equalsIgnoreCase("CTL")){
			if(reconTableName.contains("ATM")){
				tableNameStg="CISO_STG";
				tableNameAudit="CISO_STG_AUDIT";
				}else if(reconTableName.contains("POS")){
					if(reconTableName.contains("ISSUER")){
						tableNameStg="CISO_STG";
						tableNameAudit="CISO_STG_AUDIT";
					}else{
						tableNameStg="MISO_STG";
						tableNameAudit="MISO_STG_AUDIT";
					}
				}
			}else if(integrationName.equalsIgnoreCase("VISA")){
				if(reconTableName.contains("ISSUER")){
					tableNameStg="VISA_ISSUER_STG";
					tableNameAudit="VISA_ISSUER_STG_AUDIT";
				}
			}
		
		
		
		
		
		if (APPROVED.equalsIgnoreCase(status)) {
			
			String stagingTableIdSeqName = tableNameStg + "MM_ID_SEQ";
			String stagingTableIdGenQry = "SELECT NEXT VALUE FOR " + stagingTableIdSeqName + " as sno";
			String idseqname=tableNameStg + "MM_ID_SEQ";
				
			PreparedStatement stagingTableIdGenPstmt = connection.prepareStatement(stagingTableIdGenQry);
			String reconTableIdSeqName = reconTableName + "MM_ID_SEQ";
			String reconTableIdGenQry = "SELECT NEXT VALUE FOR " + reconTableIdSeqName + " as sno";
			
			PreparedStatement reconTableIdGenPstmt = connection.prepareStatement(reconTableIdGenQry);
			 
			String reconTableReconIdSeqName = reconTableName + "MM_RECON_ID_SEQ";
			String reconTableReconIdGenQry = "SELECT NEXT VALUE FOR " + reconTableReconIdSeqName + " as sno";
				
			PreparedStatement reconTableReconIdGenPstmt = connection.prepareStatement(reconTableReconIdGenQry);
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			String debitOrCredit=null;
			String mainOrReversalIndOrig=null;
			String mainOrReversalIndGl=null;
			int debOrCreIndGl=0;
			int debOrCreIndOrig=0;
			
			PreparedStatement updateStmt=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			PreparedStatement	stagingInsertPstmt=null;
			PreparedStatement	stagingUpdatePstmt=null;
			PreparedStatement reconDataSelectFromStagingPstmt=null;
			PreparedStatement reconDataInsertPstmt=null;
			PreparedStatement generateInsertPstmt=null;
			PreparedStatement generateInsertPstmtOrig=null;
			try{
			        for(Map<String,Object> selectedrecord:records){
			        	selectAuditStmt=connection.prepareStatement(auditSelectQry);
			        	List<Map<String,Object>> insertData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
			        	Query insertQuery=OperationsUtil.getInsertQueryConf(tableNameStg, connection);
			        	stagingInsertPstmt=connection.prepareStatement(insertQuery.getQueryString());
						Map paramValueMap=new HashMap();
						for(Map rec : insertData){
							long stgsid=PersistanceUtil.generateSeqNo(connection, stagingTableIdGenPstmt, stagingTableIdSeqName);
							stgsid=stgsid+System.currentTimeMillis();
							rec.put("SID", stgsid);
							rec.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
							rec.put("VERSION",1);
							rec.put("UPDATED_ON",new Timestamp(Calendar.getInstance().getTimeInMillis()));
							debitOrCredit=(String) rec.get("DEB_CRE_IND");
							if(debitOrCredit.equalsIgnoreCase("DEBIT")){
								debitOrCredit="CREDIT";
								debOrCreIndGl=2;
								debOrCreIndOrig=1;
							}else if(debitOrCredit.equalsIgnoreCase("CREDIT")){
								debitOrCredit="DEBIT";
								debOrCreIndGl=1;
								debOrCreIndOrig=2;
							}
							mainOrReversalIndOrig=(String) rec.get("MAIN_REV_IND");
							if(mainOrReversalIndOrig.equalsIgnoreCase("MAIN")){
								mainOrReversalIndGl="REVERSAL";
								
							}else if(mainOrReversalIndOrig.equalsIgnoreCase("REVERSAL")){
								mainOrReversalIndGl="MAIN";
								
							}
							rec.put("DEB_CRE_IND",debitOrCredit);
							rec.put("MAIN_REV_IND",mainOrReversalIndGl);
							rec.put("COMMENTS", "New Gl Entry");
							paramValueMap.put("PARAM_VALUE_MAP", rec);
							insertRegulator.insert(stagingInsertPstmt, paramValueMap, insertQuery.getQueryParam());
							logger.trace("Record Inserted to "+tableNameStg);
							glList.add(rec);
							
						}
			        	glList.add(selectedrecord);
			        	
			        }
				
				
			        long reconId=PersistanceUtil.generateSeqNo(connection, reconTableReconIdGenPstmt, reconTableReconIdSeqName);
			        
			        reconId=reconId+System.currentTimeMillis();
			for(Map<String,Object> selectedrecord:glList){
				
				selectAuditStmt=connection.prepareStatement(auditSelectQry);
				
				List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
				
				Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
				
				  int version=0;
				for(Map rec : auditData){
					Map paramValueMap=new HashMap();
					paramValueMap.put("PARAM_VALUE_MAP", rec);
					version=Integer.parseInt(rec.get("VERSION").toString());
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					logger.trace("Record Inserted to "+tableNameAudit);
				}
				
				String approveComments=userid+" : "+comment;
				
				long sid=(long) selectedrecord.get("SID");
				
////////////////////////////////
				

				String reocnside=integrationName;
				String queryName=reconTableName+"_"+reocnside;
				
				Query query = queries.getQueryConf(queryName);
				reconDataSelectFromStagingPstmt=connection.prepareStatement(query.getQueryString());
				reconDataSelectFromStagingPstmt.setObject(1, sid);
				ResultSet unreconRs=reconDataSelectFromStagingPstmt.executeQuery();
				ResultSetMetaData unreconRsm= unreconRs.getMetaData();
				int columnCount=unreconRsm.getColumnCount();
				Map<String,Object> reconDataMap=new HashMap<String,Object>();
				while(unreconRs.next()){
							
					for(int i=1;i<=columnCount;i++){
						System.out.println(unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
						reconDataMap.put(unreconRsm.getColumnName(i), unreconRs.getObject(unreconRsm.getColumnName(i).toUpperCase()));
					}
				}
			
				
				long recid=PersistanceUtil.generateSeqNo(connection, reconTableIdGenPstmt, reconTableIdSeqName);
				recid=recid+System.currentTimeMillis();
				reconDataMap.put("ID",recid);
				reconDataMap.put("RECON_ID", reconId);
				reconDataMap.put("UPDATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
				reconDataMap.put("MATCH_TYPE",  "MM");
				reconDataMap.put("USER_ID",  "SYSTEM");
				reconDataMap.put("WORKFLOW_STATUS",  "N");
				reconDataMap.put("ACTIVE_INDEX",  "Y");
				reconDataMap.put("SID", sid );
				reconDataMap.put("COMMENTS", "MANUAL GL ENTRY" );
				reconDataMap.put("STATUS", "MANUAL GL ENTRY");
				reconDataMap.put("VERSION", 1 );
				reconDataMap.put("RECON_SIDE", integrationName);
				reconDataMap.put("CREATED_ON",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
				//reconDataMap.put("COMMENTS",  "SYSTEM");
			    Query reconQuery=	OperationsUtil.getInsertQueryConf(reconTableName, connection);
			    reconDataInsertPstmt=connection.prepareStatement(reconQuery.getQueryString());
			    
			    Map<String, Object> paramValueMap=new HashMap<String, Object>();
				paramValueMap.put("PARAM_VALUE_MAP", reconDataMap);
			 
			    insertRegulator.insert(reconDataInsertPstmt, paramValueMap, reconQuery.getQueryParam());
			    logger.trace("Record Inserted to "+reconTableName+" with RECON_ID "+reconId);
				++version;
				stagingUpdatePstmt=connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='N',ACTIVITY_COMMENTS=?,RECON_STATUS='MM',RECON_ID=?,VERSION="+version+" WHERE SID="+sid);
				stagingUpdatePstmt.setObject(1, approveComments);
				stagingUpdatePstmt.setObject(2, reconId);
				int rows=	stagingUpdatePstmt.executeUpdate();
				
				
				
				//////////////////////
				
				
				
				
				///////////////////////////////
			
				
				/////////////////////////////////////////////////////
				
			}
			String DOC_ALP=(String)records.get(0).get("DOC_ALP");
			String DOC_NUM=(String)records.get(0).get("DOC_NUM");
			if(records.get(0).get("DOC_ALP")==null)
			{
				DOC_ALP="0000";
			}
			if(records.get(0).get("DOC_NUM")==null)
			{
				DOC_NUM="00000000000";
			}
			// ADDING LAST_4_DIGIT_DOC_NO
			
			String retrive_4_Digit_Doc_No=records.get(0).get("DOC_NUM").toString();
			String Last_4_digit_DOC_No="";
			if(retrive_4_Digit_Doc_No.length()>4){
		       	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No.substring(retrive_4_Digit_Doc_No.length()-4);
		        }else{
		        	Last_4_digit_DOC_No=retrive_4_Digit_Doc_No;
		        }
			
			Query glQuery=OperationsUtil.getInsertQueryConf("GENERATE_GL_ENTRY", connection);
			Map<String,Object> generateGlEntryMap=new HashMap<String,Object>();
			generateGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
			generateGlEntryMap.put("BRANCH_CODE", DEBITCREDITDETAILS.get("bra_code"));
			generateGlEntryMap.put("CUSTOMER", DEBITCREDITDETAILS.get("cust_ID"));
			generateGlEntryMap.put("CHECK_DIGIT", DEBITCREDITDETAILS.get("manualCheckDigit"));
			generateGlEntryMap.put("LEDGER_CODE", DEBITCREDITDETAILS.get("led_code"));
			generateGlEntryMap.put("SUB_ACC_CODE", DEBITCREDITDETAILS.get("sub_acc_code"));
			generateGlEntryMap.put("DEB_CRE_IND", debOrCreIndOrig );
			generateGlEntryMap.put("TRANSACTION_AMOUNT", records.get(0).get("TRA_AMT"));
			generateGlEntryMap.put("RECON_ID", reconId);
			generateGlEntryMap.put("BUSINESS_AREA", businessArea);
			generateGlEntryMap.put("RECON_NAME", reconName);
			
			generateGlEntryMap.put("CURR_CODE", DEBITCREDITDETAILS.get("curr_code"));
			generateGlEntryMap.put("GL_FLAG", "N");
			generateGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
			generateGlEntryMap.put("INSERT_USER", userid);
			generateGlEntryMap.put("REMARKS",  remarks);
			//generateGlEntryMap.put("REMARKS",  records.get(0).get("REMARKS"));
			generateGlEntryMap.put("REASON",  comments);
			generateGlEntryMap.put("DOCUMENT_ALPHA",  DOC_ALP);
			generateGlEntryMap.put("DOCUMENT_NUMBER",  DOC_NUM);
			
			Map<String,Object> generateOriginalGlEntryMap=new HashMap<String,Object>();
			generateOriginalGlEntryMap.put("LAST_4_DIGIT_DOC_NO", Last_4_digit_DOC_No);
			generateOriginalGlEntryMap.put("BRANCH_CODE", records.get(0).get("BRA_CODE"));
			generateOriginalGlEntryMap.put("CUSTOMER", records.get(0).get("CUS_NUM"));
			generateOriginalGlEntryMap.put("CHECK_DIGIT",DEBITCREDITDETAILS.get("orginalCheckDigit"));
			generateOriginalGlEntryMap.put("LEDGER_CODE", records.get(0).get("LED_CODE"));
			generateOriginalGlEntryMap.put("SUB_ACC_CODE", records.get(0).get("SUB_ACCT_CODE"));
			generateOriginalGlEntryMap.put("DEB_CRE_IND",debOrCreIndGl);
			generateOriginalGlEntryMap.put("TRANSACTION_AMOUNT", records.get(0).get("TRA_AMT"));
			generateOriginalGlEntryMap.put("RECON_ID", reconId);
			generateOriginalGlEntryMap.put("BUSINESS_AREA", businessArea);
			generateOriginalGlEntryMap.put("RECON_NAME", reconName);
			generateOriginalGlEntryMap.put("CURR_CODE", records.get(0).get("CUR_CODE"));
			generateOriginalGlEntryMap.put("GL_FLAG",  "N");
			generateOriginalGlEntryMap.put("INSERT_DATE",  new Timestamp(Calendar.getInstance().getTimeInMillis()));
			generateOriginalGlEntryMap.put("INSERT_USER", userid);
			generateOriginalGlEntryMap.put("REMARKS",  remarks);
			generateOriginalGlEntryMap.put("DOCUMENT_ALPHA",  DOC_ALP);
			generateOriginalGlEntryMap.put("DOCUMENT_NUMBER",  DOC_NUM);
			 generateInsertPstmt=connection.prepareStatement(glQuery.getQueryString());
			Map<String, Object> paramValueMap=new HashMap<String, Object>();
			paramValueMap.put("PARAM_VALUE_MAP", generateGlEntryMap);
			insertRegulator.insert(generateInsertPstmt, paramValueMap, glQuery.getQueryParam());
			 logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
			 generateInsertPstmtOrig=connection.prepareStatement(glQuery.getQueryString());
			Map<String, Object> paramValueMapOrig=new HashMap<String, Object>();
			paramValueMapOrig.put("PARAM_VALUE_MAP", generateOriginalGlEntryMap);
			insertRegulator.insert(generateInsertPstmtOrig, paramValueMapOrig, glQuery.getQueryParam());
			logger.trace("Record Inserted to "+"GENERATE_GL_ENTRY"+" with RECON_ID "+reconId);
			
			}catch(Exception e){
			//	e.printStackTrace();
				logger.error(e.getMessage()+" "+e);
			}
			finally{
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				DbUtil.closePreparedStatement(stagingUpdatePstmt);
				DbUtil.closePreparedStatement(stagingInsertPstmt);
				DbUtil.closePreparedStatement(stagingTableIdGenPstmt);
				DbUtil.closePreparedStatement(reconTableIdGenPstmt);
				DbUtil.closePreparedStatement(reconTableReconIdGenPstmt);
				DbUtil.closePreparedStatement(reconDataInsertPstmt);
				DbUtil.closePreparedStatement(reconDataSelectFromStagingPstmt);	
				DbUtil.closePreparedStatement(generateInsertPstmt);
				DbUtil.closePreparedStatement(generateInsertPstmtOrig);
				logger.trace("Activity Approved!..");
			}
		
			
		} else if (REJECTED.equalsIgnoreCase(status)) {
		
			String auditSelectQry="	select * from "+tableNameStg+"  where version=(	select max(version) from "+tableNameStg+" where sid =?) and sid=?";
			Query  insertQueryConf=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
			
			PreparedStatement updateStmt=null;
			PreparedStatement selectAuditStmt=null;
			PreparedStatement auditInsertPstmt=null;
			
			try{
				for(Map<String,Object> selectedrecord:records){
					
					selectAuditStmt=connection.prepareStatement(auditSelectQry);
					
					List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(selectedrecord, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
					
					Query auditQuery=OperationsUtil.getInsertQueryConf(tableNameAudit, connection);
					auditInsertPstmt=connection.prepareStatement(auditQuery.getQueryString());
					
					for(Map rec : auditData){
						Map paramValueMap=new HashMap();
						paramValueMap.put("PARAM_VALUE_MAP", rec);
						insertRegulator.insert(auditInsertPstmt, paramValueMap, auditQuery.getQueryParam());
					}
					
					String approveComments=userid+" : "+comment;
					
					long sid=(long) selectedrecord.get("SID");
					int version=Integer.parseInt( selectedrecord.get("VERSION").toString());
					++version;
					PreparedStatement stagingUpdatePstmt = connection.prepareStatement("UPDATE "+tableNameStg+" SET WORKFLOW_STATUS='N',ACTIVITY_COMMENTS=?,VERSION="+version+" WHERE SID="+sid);
					stagingUpdatePstmt.setObject(1, approveComments);
					
					int rows=	stagingUpdatePstmt.executeUpdate();
					
					
				}
			}catch(Exception e){
			//	e.printStackTrace();
				logger.error(e.getMessage()+" "+e);
			}
			finally{
				DbUtil.closePreparedStatement(updateStmt);
				DbUtil.closePreparedStatement(selectAuditStmt);
				DbUtil.closePreparedStatement(auditInsertPstmt);
				logger.trace("Activity Rejected!..");			
			}
			
		} else if("PENDING".equalsIgnoreCase(status)){
			
		}

	} catch (Exception e) {
		e.printStackTrace();
	}
	finally{
		try {
			if (connection != null && !connection.isClosed()) {
				connection.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage()+" "+e);
		}
				
	}
	return result;
}
private void audit(LoadRegulator loadRegulator, InsertRegulator insertRegulator, Query insertQueryConf,
		PreparedStatement selectAuditStmt, PreparedStatement auditInsertPstmt, Map<String, Object> rec)
				throws ClassNotFoundException, SQLException {
	String QueryParam=insertQueryConf.getQueryParam();
	List<Map<String,Object>> auditData=loadRegulator.loadCompleteData(rec, selectAuditStmt, "SID@BIGINT,SID@BIGINT");
	
	if(auditData!=null){
		for(Map<String,Object> auditRec:auditData){
			Map paramValueMap=new HashMap();
			int version=   (int) auditRec.get("VERSION");
			++version;
			auditRec.put("WORKFLOW_STATUS",rec.get("WORKFLOW_STATUS"));
			auditRec.put("VERSION",version);
			auditRec.put("ACTIVITY_COMMENTS", rec.get("ACTIVITY_COMMENTS"));
			auditRec.put("ACTIVE_INDEX", rec.get("ACTIVE_INDEX"));
			auditRec.put("UPDATED_ON", new Timestamp(Calendar.getInstance().getTimeInMillis()));
			paramValueMap.put("PARAM_VALUE_MAP", auditRec);
			
			
			
			insertRegulator.insert(auditInsertPstmt, paramValueMap, insertQueryConf.getQueryParam());
		}
	}
	


	
}
}

