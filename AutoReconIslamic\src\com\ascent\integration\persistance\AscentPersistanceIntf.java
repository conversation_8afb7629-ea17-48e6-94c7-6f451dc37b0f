package com.ascent.integration.persistance;

import java.sql.Connection;
import java.util.List;
import java.util.Map;

import com.ascent.custumize.integration.Integration;
import com.ascent.custumize.query.Queries;

public interface AscentPersistanceIntf {
	public Map<String, Object> persist(Integration integration, Queries queries, Connection connection,
			List<Map<String, Object>> txnList, Boolean exFound) throws Exception;

}
