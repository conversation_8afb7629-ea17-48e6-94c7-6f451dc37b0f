package com.ascent.reports;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;

import com.ascent.service.dto.User;

public class UserData {

	public List<Map<String,String>> getUserData(HttpSession httpSession) {
		
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		
		Map<String,String> userMap = new HashMap<String,String>();
		User user = (User) httpSession.getAttribute("userId");
		userMap.put("userID", user.getUserId());
		userMap.put("userName", user.getUserName());
		
		//System.out.println("userMap = "+userMap);
		
		list.add(userMap);
		
		return list;
	}

}
